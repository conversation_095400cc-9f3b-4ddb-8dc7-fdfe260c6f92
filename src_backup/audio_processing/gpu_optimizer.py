"""
RTX 3070 GPU Optimizer

Hardware-specific optimizations for NVIDIA GeForce RTX 3070 with 8GB VRAM.
Provides memory management, batch optimization, and performance monitoring.
"""

import torch
import logging
import time
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class GPUMemoryStats:
    """GPU memory usage statistics"""
    allocated_gb: float
    reserved_gb: float
    total_gb: float
    utilization_percent: float
    available_gb: float


@dataclass
class ProcessingBenchmark:
    """Processing performance benchmark results"""
    processing_time: float
    memory_peak_gb: float
    gpu_utilization: float
    throughput_songs_per_minute: float
    efficiency_score: float


class RTX3070Optimizer:
    """Hardware optimizer specifically for RTX 3070 system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.device = None
        self.gpu_config = {}
        self.memory_history = []
        self.performance_history = []
        
        # Initialize GPU
        self._initialize_gpu()
    
    def _initialize_gpu(self) -> None:
        """Initialize and configure RTX 3070 GPU"""
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA not available - RTX 3070 required for Phase 2")
        
        # Verify GPU model
        gpu_name = torch.cuda.get_device_name(0)
        if "RTX 3070" not in gpu_name:
            self.logger.warning(f"Expected RTX 3070, found: {gpu_name}")
        
        self.device = torch.device("cuda:0")
        
        # RTX 3070 specific optimizations
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        torch.backends.cuda.matmul.allow_tf32 = True  # Enable TF32 for faster computation
        torch.backends.cudnn.allow_tf32 = True
        
        # Conservative memory allocation for 8GB VRAM
        torch.cuda.set_per_process_memory_fraction(0.85)  # Use 6.8GB of 8GB
        torch.cuda.empty_cache()
        
        # Get GPU configuration
        props = torch.cuda.get_device_properties(0)
        total_memory_gb = props.total_memory / (1024**3)
        
        self.gpu_config = {
            "device_name": gpu_name,
            "total_vram_gb": total_memory_gb,
            "available_vram_gb": total_memory_gb * 0.85,
            "recommended_batch_size": 4,
            "max_sequence_length": 2000,  # frames (~40 seconds at 50fps)
            "tensor_cores_available": props.major >= 7,  # RTX 3070 has Tensor Cores
            "compute_capability": f"{props.major}.{props.minor}"
        }
        
        self.logger.info(f"RTX 3070 initialized: {total_memory_gb:.1f}GB VRAM, "
                        f"Compute {self.gpu_config['compute_capability']}")
    
    def get_memory_stats(self) -> GPUMemoryStats:
        """Get current GPU memory statistics"""
        if not torch.cuda.is_available():
            return GPUMemoryStats(0, 0, 0, 0, 0)
        
        allocated = torch.cuda.memory_allocated() / (1024**3)
        reserved = torch.cuda.memory_reserved() / (1024**3)
        total = self.gpu_config["total_vram_gb"]
        utilization = (allocated / total) * 100
        available = total - allocated
        
        stats = GPUMemoryStats(
            allocated_gb=allocated,
            reserved_gb=reserved,
            total_gb=total,
            utilization_percent=utilization,
            available_gb=available
        )
        
        # Track memory history
        self.memory_history.append({
            "timestamp": time.time(),
            "allocated_gb": allocated,
            "utilization_percent": utilization
        })
        
        # Keep only last 1000 measurements
        if len(self.memory_history) > 1000:
            self.memory_history = self.memory_history[-1000:]
        
        return stats
    
    def optimize_batch_size(self, base_batch_size: int = 4, 
                           sequence_length: int = 1000) -> int:
        """Dynamically optimize batch size based on available memory"""
        memory_stats = self.get_memory_stats()
        
        # If we have less than 5GB available, reduce batch size
        if memory_stats.available_gb < 5.0:
            optimized_batch_size = max(1, base_batch_size // 2)
            self.logger.warning(f"Reduced batch size to {optimized_batch_size} "
                              f"due to limited GPU memory: {memory_stats.available_gb:.1f}GB")
            return optimized_batch_size
        
        # If sequence is very long, reduce batch size
        if sequence_length > 1500:  # > 30 seconds at 50fps
            optimized_batch_size = max(1, base_batch_size // 2)
            self.logger.info(f"Reduced batch size to {optimized_batch_size} "
                           f"for long sequence: {sequence_length} frames")
            return optimized_batch_size
        
        return base_batch_size
    
    def cleanup_memory(self) -> float:
        """Clean up GPU memory and return freed amount"""
        memory_before = torch.cuda.memory_allocated() / (1024**3)
        
        # Clear cache
        torch.cuda.empty_cache()
        
        # Force garbage collection
        import gc
        gc.collect()
        
        memory_after = torch.cuda.memory_allocated() / (1024**3)
        freed_memory = memory_before - memory_after
        
        if freed_memory > 0.1:  # Log if significant memory was freed
            self.logger.info(f"Freed {freed_memory:.2f}GB GPU memory")
        
        return freed_memory
    
    def monitor_processing_batch(self, batch_size: int, processing_func, *args, **kwargs):
        """Monitor GPU usage during batch processing"""
        start_time = time.time()
        memory_before = self.get_memory_stats()
        
        try:
            # Check memory before processing
            if memory_before.utilization_percent > 90:
                self.logger.warning(f"High GPU memory usage before processing: "
                                  f"{memory_before.utilization_percent:.1f}%")
                self.cleanup_memory()
            
            # Execute processing function
            result = processing_func(*args, **kwargs)
            
            # Monitor memory during processing
            memory_peak = self.get_memory_stats()
            
            # Check for memory issues
            if memory_peak.utilization_percent > 95:
                self.logger.error(f"Critical GPU memory usage: "
                                f"{memory_peak.utilization_percent:.1f}%")
                self.cleanup_memory()
            
            processing_time = time.time() - start_time
            
            # Calculate performance metrics
            benchmark = ProcessingBenchmark(
                processing_time=processing_time,
                memory_peak_gb=memory_peak.allocated_gb,
                gpu_utilization=memory_peak.utilization_percent,
                throughput_songs_per_minute=(batch_size / processing_time) * 60,
                efficiency_score=self._calculate_efficiency_score(
                    processing_time, memory_peak.utilization_percent, batch_size
                )
            )
            
            self.performance_history.append(benchmark)
            
            return result, benchmark
            
        except torch.cuda.OutOfMemoryError as e:
            self.logger.error(f"GPU OOM during batch processing: {e}")
            self.cleanup_memory()
            
            # Retry with smaller batch size
            if batch_size > 1:
                self.logger.info(f"Retrying with reduced batch size: {batch_size // 2}")
                return self.monitor_processing_batch(
                    batch_size // 2, processing_func, *args, **kwargs
                )
            else:
                raise RuntimeError("Cannot process even single item due to GPU memory constraints")
        
        except Exception as e:
            self.logger.error(f"Error during monitored processing: {e}")
            self.cleanup_memory()
            raise
    
    def _calculate_efficiency_score(self, processing_time: float, 
                                  gpu_utilization: float, batch_size: int) -> float:
        """Calculate processing efficiency score (0-100)"""
        # Target: 20 songs per minute, 80% GPU utilization
        target_throughput = 20 / 60  # songs per second
        actual_throughput = batch_size / processing_time
        
        throughput_score = min(100, (actual_throughput / target_throughput) * 100)
        utilization_score = min(100, (gpu_utilization / 80) * 100)
        
        # Weighted average: 60% throughput, 40% utilization
        efficiency_score = (throughput_score * 0.6) + (utilization_score * 0.4)
        
        return min(100, efficiency_score)
    
    def get_optimization_recommendations(self) -> Dict[str, Any]:
        """Get optimization recommendations based on performance history"""
        if not self.performance_history:
            return {"status": "no_data", "recommendations": []}
        
        recent_benchmarks = self.performance_history[-10:]  # Last 10 batches
        avg_efficiency = sum(b.efficiency_score for b in recent_benchmarks) / len(recent_benchmarks)
        avg_memory_usage = sum(b.memory_peak_gb for b in recent_benchmarks) / len(recent_benchmarks)
        avg_throughput = sum(b.throughput_songs_per_minute for b in recent_benchmarks) / len(recent_benchmarks)
        
        recommendations = []
        
        if avg_efficiency < 70:
            recommendations.append("Consider reducing batch size for better memory efficiency")
        
        if avg_memory_usage > 6.0:
            recommendations.append("High memory usage detected - enable more frequent cleanup")
        
        if avg_throughput < 15:
            recommendations.append("Low throughput - check for CPU bottlenecks or I/O issues")
        
        return {
            "status": "analyzed",
            "performance_summary": {
                "avg_efficiency_score": avg_efficiency,
                "avg_memory_usage_gb": avg_memory_usage,
                "avg_throughput_songs_per_minute": avg_throughput,
                "total_batches_processed": len(self.performance_history)
            },
            "recommendations": recommendations
        }
    
    def get_device(self) -> torch.device:
        """Get the configured CUDA device"""
        return self.device
    
    def get_config(self) -> Dict[str, Any]:
        """Get GPU configuration"""
        return self.gpu_config.copy()
    
    def is_ready(self) -> bool:
        """Check if GPU is ready for processing"""
        if not torch.cuda.is_available():
            return False
        
        memory_stats = self.get_memory_stats()
        return memory_stats.available_gb > 2.0  # Need at least 2GB available
