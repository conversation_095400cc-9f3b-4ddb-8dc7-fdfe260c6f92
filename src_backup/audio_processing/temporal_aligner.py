"""
Temporal Aligne<PERSON>

Creates BPM-aligned temporal grids for precise feature-to-note alignment.
Handles BPM changes, offsets, and timing commands from TJA metadata.
"""

import torch
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class TimingCommand:
    """TJA timing command representation"""
    type: str
    value: Any
    timing_ms: float
    measure: int


@dataclass
class AlignmentConfig:
    """Configuration for temporal alignment"""
    frame_rate: float = 50.0
    default_time_signature: Tuple[int, int] = (4, 4)
    interpolation_method: str = "linear"
    alignment_tolerance_ms: float = 20.0  # 20ms tolerance for alignment


class TemporalAligner:
    """Creates precise temporal alignment between audio features and TJA notation"""
    
    def __init__(self, config: Optional[AlignmentConfig] = None):
        self.config = config or AlignmentConfig()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("TemporalAligner initialized")
    
    def create_timing_grid(self, audio_duration: float, base_bpm: float, 
                          offset: float, timing_commands: Optional[List[TimingCommand]] = None) -> Dict[str, torch.Tensor]:
        """
        Create BPM-aligned temporal grid for feature-to-note alignment
        
        Args:
            audio_duration: Duration of audio in seconds
            base_bpm: Base BPM from TJA metadata
            offset: TJA offset in seconds (chart delay relative to audio)
            timing_commands: List of timing commands (BPM changes, etc.)
            
        Returns:
            Dictionary containing timing grids and metadata
        """
        try:
            # Calculate total frames
            total_frames = int(audio_duration * self.config.frame_rate)
            time_axis = np.linspace(0, audio_duration, total_frames)
            
            # Account for TJA offset (chart delay relative to audio)
            adjusted_time = time_axis - offset
            
            # Create BPM sequence (handle BPM changes)
            bpm_sequence = self._create_bpm_sequence(
                adjusted_time, base_bpm, timing_commands
            )
            
            # Create beat-aligned grid
            beat_grid = self._create_beat_grid(adjusted_time, bpm_sequence)
            
            # Create measure-aligned grid (assuming 4/4 time signature)
            measure_grid = self._create_measure_grid(adjusted_time, bpm_sequence)
            
            # Create subdivision grids (8th notes, 16th notes)
            subdivision_grids = self._create_subdivision_grids(adjusted_time, bpm_sequence)
            
            # Create timing metadata
            beat_positions = self._extract_beat_positions(adjusted_time, bpm_sequence)
            measure_positions = self._extract_measure_positions(adjusted_time, bpm_sequence)
            
            return {
                "beat_grid": torch.from_numpy(beat_grid).float(),
                "measure_grid": torch.from_numpy(measure_grid).float(),
                "eighth_note_grid": torch.from_numpy(subdivision_grids["eighth"]).float(),
                "sixteenth_note_grid": torch.from_numpy(subdivision_grids["sixteenth"]).float(),
                "time_axis": torch.from_numpy(time_axis).float(),
                "adjusted_time": torch.from_numpy(adjusted_time).float(),
                "bpm_sequence": torch.from_numpy(bpm_sequence).float(),
                "beat_positions": torch.from_numpy(beat_positions).float(),
                "measure_positions": torch.from_numpy(measure_positions).float(),
                "timing_metadata": {
                    "base_bpm": base_bpm,
                    "offset": offset,
                    "audio_duration": audio_duration,
                    "total_frames": total_frames,
                    "frame_rate": self.config.frame_rate,
                    "bpm_changes": len(timing_commands) if timing_commands else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error creating timing grid: {e}")
            # Return minimal grid as fallback
            total_frames = int(audio_duration * self.config.frame_rate)
            return {
                "beat_grid": torch.zeros(total_frames),
                "measure_grid": torch.zeros(total_frames),
                "eighth_note_grid": torch.zeros(total_frames),
                "sixteenth_note_grid": torch.zeros(total_frames),
                "time_axis": torch.linspace(0, audio_duration, total_frames),
                "adjusted_time": torch.linspace(-offset, audio_duration - offset, total_frames),
                "bpm_sequence": torch.full((total_frames,), base_bpm),
                "beat_positions": torch.tensor([]),
                "measure_positions": torch.tensor([]),
                "timing_metadata": {
                    "base_bpm": base_bpm,
                    "offset": offset,
                    "audio_duration": audio_duration,
                    "total_frames": total_frames,
                    "frame_rate": self.config.frame_rate,
                    "bpm_changes": 0
                }
            }
    
    def _create_bpm_sequence(self, time_axis: np.ndarray, base_bpm: float, 
                           timing_commands: Optional[List[TimingCommand]]) -> np.ndarray:
        """Create time-varying BPM sequence"""
        bpm_sequence = np.full_like(time_axis, base_bpm)
        
        if timing_commands:
            for cmd in timing_commands:
                if cmd.type == "BPMCHANGE":
                    # Find the time index for this BPM change
                    change_time = cmd.timing_ms / 1000.0  # Convert to seconds
                    change_idx = np.searchsorted(time_axis, change_time)
                    
                    # Apply BPM change from this point forward
                    if change_idx < len(bpm_sequence):
                        bpm_sequence[change_idx:] = float(cmd.value)
        
        return bpm_sequence
    
    def _create_beat_grid(self, time_axis: np.ndarray, bpm_sequence: np.ndarray) -> np.ndarray:
        """Create beat-aligned grid"""
        beat_grid = np.zeros_like(time_axis)
        
        current_beat_time = 0.0
        beat_count = 0
        
        for i, (time, bpm) in enumerate(zip(time_axis, bpm_sequence)):
            if time >= 0:  # Only after offset
                beat_duration = 60.0 / bpm
                
                # Check if we've reached the next beat
                while current_beat_time <= time:
                    # Find the closest frame to this beat
                    beat_frame_idx = np.argmin(np.abs(time_axis - current_beat_time))
                    if 0 <= beat_frame_idx < len(beat_grid):
                        beat_grid[beat_frame_idx] = 1.0
                    
                    current_beat_time += beat_duration
                    beat_count += 1
                
                # Calculate proximity to nearest beat
                time_to_next_beat = current_beat_time - time
                time_from_prev_beat = beat_duration - time_to_next_beat
                
                # Create smooth beat proximity signal
                beat_proximity = max(0, 1.0 - min(time_to_next_beat, time_from_prev_beat) / (beat_duration / 2))
                beat_grid[i] = max(beat_grid[i], beat_proximity * 0.5)  # Smooth component
        
        return beat_grid
    
    def _create_measure_grid(self, time_axis: np.ndarray, bpm_sequence: np.ndarray) -> np.ndarray:
        """Create measure-aligned grid (4/4 time signature)"""
        measure_grid = np.zeros_like(time_axis)
        
        current_measure_time = 0.0
        beats_per_measure = self.config.default_time_signature[0]
        
        for i, (time, bpm) in enumerate(zip(time_axis, bpm_sequence)):
            if time >= 0:  # Only after offset
                beat_duration = 60.0 / bpm
                measure_duration = beat_duration * beats_per_measure
                
                # Check if we've reached the next measure
                while current_measure_time <= time:
                    # Find the closest frame to this measure
                    measure_frame_idx = np.argmin(np.abs(time_axis - current_measure_time))
                    if 0 <= measure_frame_idx < len(measure_grid):
                        measure_grid[measure_frame_idx] = 1.0
                    
                    current_measure_time += measure_duration
                
                # Calculate proximity to nearest measure
                time_to_next_measure = current_measure_time - time
                measure_proximity = max(0, 1.0 - time_to_next_measure / (measure_duration / 2))
                measure_grid[i] = max(measure_grid[i], measure_proximity * 0.3)  # Smooth component
        
        return measure_grid
    
    def _create_subdivision_grids(self, time_axis: np.ndarray, 
                                bpm_sequence: np.ndarray) -> Dict[str, np.ndarray]:
        """Create subdivision grids (8th notes, 16th notes)"""
        eighth_grid = np.zeros_like(time_axis)
        sixteenth_grid = np.zeros_like(time_axis)
        
        current_eighth_time = 0.0
        current_sixteenth_time = 0.0
        
        for i, (time, bpm) in enumerate(zip(time_axis, bpm_sequence)):
            if time >= 0:  # Only after offset
                beat_duration = 60.0 / bpm
                eighth_duration = beat_duration / 2
                sixteenth_duration = beat_duration / 4
                
                # 8th note grid
                while current_eighth_time <= time:
                    eighth_frame_idx = np.argmin(np.abs(time_axis - current_eighth_time))
                    if 0 <= eighth_frame_idx < len(eighth_grid):
                        eighth_grid[eighth_frame_idx] = 1.0
                    current_eighth_time += eighth_duration
                
                # 16th note grid
                while current_sixteenth_time <= time:
                    sixteenth_frame_idx = np.argmin(np.abs(time_axis - current_sixteenth_time))
                    if 0 <= sixteenth_frame_idx < len(sixteenth_grid):
                        sixteenth_grid[sixteenth_frame_idx] = 1.0
                    current_sixteenth_time += sixteenth_duration
        
        return {
            "eighth": eighth_grid,
            "sixteenth": sixteenth_grid
        }
    
    def _extract_beat_positions(self, time_axis: np.ndarray, 
                              bpm_sequence: np.ndarray) -> np.ndarray:
        """Extract beat positions in frame indices"""
        beat_positions = []
        current_beat_time = 0.0
        
        for time, bpm in zip(time_axis, bpm_sequence):
            if time >= 0:  # Only after offset
                beat_duration = 60.0 / bpm
                
                while current_beat_time <= time_axis[-1]:
                    if current_beat_time >= 0:  # After offset
                        beat_frame_idx = np.argmin(np.abs(time_axis - current_beat_time))
                        beat_positions.append(beat_frame_idx)
                    current_beat_time += beat_duration
                break
        
        return np.array(beat_positions, dtype=np.int64)
    
    def _extract_measure_positions(self, time_axis: np.ndarray, 
                                 bpm_sequence: np.ndarray) -> np.ndarray:
        """Extract measure positions in frame indices"""
        measure_positions = []
        current_measure_time = 0.0
        beats_per_measure = self.config.default_time_signature[0]
        
        for time, bpm in zip(time_axis, bpm_sequence):
            if time >= 0:  # Only after offset
                beat_duration = 60.0 / bpm
                measure_duration = beat_duration * beats_per_measure
                
                while current_measure_time <= time_axis[-1]:
                    if current_measure_time >= 0:  # After offset
                        measure_frame_idx = np.argmin(np.abs(time_axis - current_measure_time))
                        measure_positions.append(measure_frame_idx)
                    current_measure_time += measure_duration
                break
        
        return np.array(measure_positions, dtype=np.int64)
    
    def align_features_to_notes(self, features: torch.Tensor, 
                               note_timings_ms: List[float],
                               timing_grid: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Align audio features to TJA note timings
        
        Args:
            features: Audio features tensor [time, features]
            note_timings_ms: List of note timings in milliseconds
            timing_grid: Timing grid from create_timing_grid
            
        Returns:
            Dictionary containing aligned features and alignment metadata
        """
        try:
            time_axis = timing_grid["time_axis"]
            frame_rate = self.config.frame_rate
            
            # Convert note timings to frame indices
            note_frame_indices = []
            alignment_errors = []
            
            for note_time_ms in note_timings_ms:
                note_time_s = note_time_ms / 1000.0
                
                # Find closest frame
                frame_idx = torch.argmin(torch.abs(time_axis - note_time_s)).item()
                note_frame_indices.append(frame_idx)
                
                # Calculate alignment error
                actual_time = time_axis[frame_idx].item()
                error_ms = abs(actual_time - note_time_s) * 1000
                alignment_errors.append(error_ms)
            
            # Extract features at note positions
            note_frame_indices = torch.tensor(note_frame_indices, dtype=torch.long)
            
            # Clamp indices to valid range
            note_frame_indices = torch.clamp(note_frame_indices, 0, features.shape[0] - 1)
            
            aligned_features = features[note_frame_indices]
            
            # Calculate alignment statistics
            alignment_stats = {
                "mean_error_ms": np.mean(alignment_errors),
                "max_error_ms": np.max(alignment_errors),
                "alignment_accuracy": np.mean(np.array(alignment_errors) < self.config.alignment_tolerance_ms),
                "total_notes": len(note_timings_ms)
            }
            
            return {
                "aligned_features": aligned_features,
                "note_frame_indices": note_frame_indices,
                "alignment_errors_ms": torch.tensor(alignment_errors),
                "alignment_stats": alignment_stats
            }
            
        except Exception as e:
            self.logger.error(f"Error aligning features to notes: {e}")
            # Return empty alignment as fallback
            return {
                "aligned_features": torch.empty((0, features.shape[1])),
                "note_frame_indices": torch.empty(0, dtype=torch.long),
                "alignment_errors_ms": torch.empty(0),
                "alignment_stats": {
                    "mean_error_ms": float('inf'),
                    "max_error_ms": float('inf'),
                    "alignment_accuracy": 0.0,
                    "total_notes": 0
                }
            }
