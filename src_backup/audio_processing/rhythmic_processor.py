"""
Rhythmic Feature Processor

Extracts rhythm-specific features for TJA note placement prediction.
Includes onset detection, tempo tracking, and beat analysis.
"""

import torch
import librosa
import numpy as np
import logging
from dataclasses import dataclass
from scipy import signal


@dataclass
class RhythmicConfig:
    """Configuration for rhythmic feature extraction"""
    sample_rate: int = 44100
    frame_rate: float = 50.0
    hop_length: int = 882  # 44100/50
    
    # Onset detection settings
    onset_method: str = "superflux"  # More robust for complex music
    onset_units: str = "frames"
    
    # Tempo tracking settings
    tempo_min: float = 60.0
    tempo_max: float = 300.0
    
    # Beat tracking settings
    beat_track_units: str = "frames"
    
    # Spectral flux settings
    n_fft: int = 2048
    
    # Feature dimensions
    rhythmic_feature_dims: int = 32


class RhythmicProcessor:
    """Rhythmic feature extraction optimized for drum content"""
    
    def __init__(self, config: Optional[RhythmicConfig] = None):
        self.config = config or RhythmicConfig()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("RhythmicProcessor initialized")
    
    def extract_onset_features(self, audio: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Extract onset detection features
        
        Args:
            audio: Audio signal as numpy array
            
        Returns:
            Dictionary containing onset-related features
        """
        try:
            # Primary onset strength using superflux method (robust for complex music)
            onset_strength = librosa.onset.onset_strength(
                y=audio,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                aggregate=np.median,  # Use median for robustness
                fmax=8000,  # Focus on drum frequency range
                n_mels=128
            )
            
            # Detect onset frames
            onset_frames = librosa.onset.onset_detect(
                onset_envelope=onset_strength,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                units=self.config.onset_units,
                delta=0.1,  # Minimum onset strength
                wait=10     # Minimum frames between onsets
            )
            
            # Create onset activation function
            onset_activation = np.zeros_like(onset_strength)
            if len(onset_frames) > 0:
                onset_activation[onset_frames] = 1.0
            
            # Smooth onset strength for better temporal continuity
            onset_strength_smooth = signal.savgol_filter(
                onset_strength, 
                window_length=min(21, len(onset_strength) // 4 * 2 + 1),  # Adaptive window
                polyorder=3
            )
            
            return {
                "onset_strength": onset_strength,
                "onset_strength_smooth": onset_strength_smooth,
                "onset_activation": onset_activation,
                "onset_frames": onset_frames,
                "onset_density": len(onset_frames) / len(onset_strength) if len(onset_strength) > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting onset features: {e}")
            # Return zero features as fallback
            n_frames = len(audio) // self.config.hop_length + 1
            return {
                "onset_strength": np.zeros(n_frames),
                "onset_strength_smooth": np.zeros(n_frames),
                "onset_activation": np.zeros(n_frames),
                "onset_frames": np.array([]),
                "onset_density": 0.0
            }
    
    def extract_tempo_features(self, audio: np.ndarray, 
                              onset_strength: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract tempo and beat tracking features
        
        Args:
            audio: Audio signal as numpy array
            onset_strength: Pre-computed onset strength (optional)
            
        Returns:
            Dictionary containing tempo-related features
        """
        try:
            # Estimate tempo using multiple methods for robustness
            if onset_strength is None:
                onset_strength = librosa.onset.onset_strength(
                    y=audio,
                    sr=self.config.sample_rate,
                    hop_length=self.config.hop_length
                )
            
            # Primary tempo estimation
            tempo, beats = librosa.beat.beat_track(
                onset_envelope=onset_strength,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                start_bpm=120.0,
                tightness=100,
                trim=False,
                units=self.config.beat_track_units
            )
            
            # Alternative tempo estimation for validation
            tempo_alt = librosa.feature.tempo(
                onset_envelope=onset_strength,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                start_bpm=120.0
            )[0]
            
            # Beat consistency analysis
            if len(beats) > 1:
                beat_intervals = np.diff(beats) * self.config.hop_length / self.config.sample_rate
                beat_consistency = 1.0 - (np.std(beat_intervals) / np.mean(beat_intervals)) if np.mean(beat_intervals) > 0 else 0.0
                beat_consistency = max(0.0, min(1.0, beat_consistency))  # Clamp to [0, 1]
            else:
                beat_consistency = 0.0
            
            return {
                "tempo": float(tempo),
                "tempo_alternative": float(tempo_alt),
                "tempo_confidence": float(abs(tempo - tempo_alt) < 10.0),  # Confidence based on agreement
                "beat_frames": beats,
                "beat_count": len(beats),
                "beat_consistency": beat_consistency,
                "estimated_duration": len(audio) / self.config.sample_rate
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting tempo features: {e}")
            return {
                "tempo": 120.0,  # Default tempo
                "tempo_alternative": 120.0,
                "tempo_confidence": 0.0,
                "beat_frames": np.array([]),
                "beat_count": 0,
                "beat_consistency": 0.0,
                "estimated_duration": len(audio) / self.config.sample_rate
            }
    
    def extract_spectral_flux(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract spectral flux for transient detection
        
        Args:
            audio: Audio signal as numpy array
            
        Returns:
            Spectral flux array
        """
        try:
            # Compute STFT
            stft = librosa.stft(
                audio,
                hop_length=self.config.hop_length,
                n_fft=self.config.n_fft
            )
            
            # Calculate spectral flux (difference between consecutive frames)
            magnitude = np.abs(stft)
            spectral_flux = np.diff(magnitude, axis=1)
            
            # Only consider positive changes (increases in energy)
            spectral_flux = np.maximum(0, spectral_flux)
            
            # Sum across frequency bins
            spectral_flux_sum = np.sum(spectral_flux, axis=0)
            
            # Pad to match other feature lengths
            spectral_flux_padded = np.pad(spectral_flux_sum, (1, 0), mode='constant', constant_values=0)
            
            # Normalize
            if np.max(spectral_flux_padded) > 0:
                spectral_flux_padded = spectral_flux_padded / np.max(spectral_flux_padded)
            
            return spectral_flux_padded
            
        except Exception as e:
            self.logger.error(f"Error extracting spectral flux: {e}")
            n_frames = len(audio) // self.config.hop_length + 1
            return np.zeros(n_frames)
    
    def extract_beat_synchronous_features(self, audio: np.ndarray, 
                                        beat_frames: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Extract beat-synchronous features
        
        Args:
            audio: Audio signal as numpy array
            beat_frames: Beat frame positions
            
        Returns:
            Dictionary containing beat-synchronous features
        """
        try:
            if len(beat_frames) < 2:
                n_frames = len(audio) // self.config.hop_length + 1
                return {
                    "beat_sync_chroma": np.zeros((n_frames, 12)),
                    "beat_sync_mfcc": np.zeros((n_frames, 13)),
                    "beat_sync_energy": np.zeros(n_frames)
                }
            
            # Extract beat-synchronous chroma
            chroma = librosa.feature.chroma_stft(
                y=audio,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length
            )
            
            beat_sync_chroma = librosa.util.sync(
                chroma,
                beat_frames,
                aggregate=np.median
            )
            
            # Extract beat-synchronous MFCC
            mfcc = librosa.feature.mfcc(
                y=audio,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                n_mfcc=13
            )
            
            beat_sync_mfcc = librosa.util.sync(
                mfcc,
                beat_frames,
                aggregate=np.median
            )
            
            # Extract beat-synchronous energy
            rms = librosa.feature.rms(
                y=audio,
                hop_length=self.config.hop_length
            )[0]
            
            beat_sync_energy = librosa.util.sync(
                rms.reshape(1, -1),
                beat_frames,
                aggregate=np.mean
            )[0]
            
            return {
                "beat_sync_chroma": beat_sync_chroma.T,
                "beat_sync_mfcc": beat_sync_mfcc.T,
                "beat_sync_energy": beat_sync_energy
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting beat-synchronous features: {e}")
            n_frames = len(audio) // self.config.hop_length + 1
            return {
                "beat_sync_chroma": np.zeros((n_frames, 12)),
                "beat_sync_mfcc": np.zeros((n_frames, 13)),
                "beat_sync_energy": np.zeros(n_frames)
            }
    
    def extract_all_rhythmic_features(self, audio: np.ndarray) -> Dict[str, torch.Tensor]:
        """
        Extract all rhythmic features and combine into fixed-size tensor
        
        Args:
            audio: Audio signal as numpy array
            
        Returns:
            Dictionary containing rhythmic features as tensors
        """
        try:
            # Extract individual feature components
            onset_features = self.extract_onset_features(audio)
            tempo_features = self.extract_tempo_features(audio, onset_features["onset_strength"])
            spectral_flux = self.extract_spectral_flux(audio)
            beat_sync_features = self.extract_beat_synchronous_features(
                audio, tempo_features["beat_frames"]
            )
            
            # Get the minimum time dimension
            n_frames = min(
                len(onset_features["onset_strength"]),
                len(spectral_flux),
                len(beat_sync_features["beat_sync_energy"])
            )
            
            # Create rhythmic feature matrix [time, 32 features]
            rhythmic_matrix = np.zeros((n_frames, self.config.rhythmic_feature_dims))
            
            # Fill feature matrix
            # Onset features (4 dimensions)
            rhythmic_matrix[:n_frames, 0] = onset_features["onset_strength"][:n_frames]
            rhythmic_matrix[:n_frames, 1] = onset_features["onset_strength_smooth"][:n_frames]
            rhythmic_matrix[:n_frames, 2] = onset_features["onset_activation"][:n_frames]
            rhythmic_matrix[:n_frames, 3] = spectral_flux[:n_frames]
            
            # Tempo features (4 dimensions - repeated for each frame)
            rhythmic_matrix[:n_frames, 4] = tempo_features["tempo"] / 200.0  # Normalize
            rhythmic_matrix[:n_frames, 5] = tempo_features["tempo_confidence"]
            rhythmic_matrix[:n_frames, 6] = tempo_features["beat_consistency"]
            rhythmic_matrix[:n_frames, 7] = tempo_features["beat_count"] / 100.0  # Normalize
            
            # Beat-synchronous features (24 dimensions: 12 chroma + 12 MFCC subset)
            if beat_sync_features["beat_sync_chroma"].shape[0] >= n_frames:
                rhythmic_matrix[:n_frames, 8:20] = beat_sync_features["beat_sync_chroma"][:n_frames]
            if beat_sync_features["beat_sync_mfcc"].shape[0] >= n_frames:
                rhythmic_matrix[:n_frames, 20:32] = beat_sync_features["beat_sync_mfcc"][:n_frames, :12]
            
            # Convert to tensor
            rhythmic_tensor = torch.from_numpy(rhythmic_matrix).float()
            
            return {
                "rhythmic_features": rhythmic_tensor,
                "onset_strength": torch.from_numpy(onset_features["onset_strength"][:n_frames]).float(),
                "tempo_info": tempo_features,
                "feature_info": {
                    "time_frames": n_frames,
                    "rhythmic_dims": self.config.rhythmic_feature_dims,
                    "onset_count": len(onset_features["onset_frames"]),
                    "beat_count": tempo_features["beat_count"]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting rhythmic features: {e}")
            # Return zero features as fallback
            n_frames = len(audio) // self.config.hop_length + 1
            return {
                "rhythmic_features": torch.zeros((n_frames, self.config.rhythmic_feature_dims)),
                "onset_strength": torch.zeros(n_frames),
                "tempo_info": {"tempo": 120.0, "beat_count": 0},
                "feature_info": {
                    "time_frames": n_frames,
                    "rhythmic_dims": self.config.rhythmic_feature_dims,
                    "onset_count": 0,
                    "beat_count": 0
                }
            }
