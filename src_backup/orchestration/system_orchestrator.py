"""
Advanced System Orchestration

Comprehensive orchestration system that coordinates all TJA Generator components,
manages workflows, and provides intelligent resource allocation and scheduling.
"""

import time
import logging
import threading
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import uuid

from ..utils.base_classes import BaseProcessor, ProcessingResult
from ..config.unified_config_manager import UnifiedConfigManager
from ..optimization.performance_optimizer import PerformanceOptimizer
from ..monitoring.system_monitor import SystemMonitor


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Task:
    """Orchestration task definition"""
    task_id: str
    task_type: str
    processor_class: str
    input_data: Any
    priority: TaskPriority = TaskPriority.NORMAL
    dependencies: List[str] = field(default_factory=list)
    max_retries: int = 3
    timeout_seconds: Optional[int] = None
    created_at: float = field(default_factory=time.time)
    
    # Runtime fields
    status: TaskStatus = TaskStatus.PENDING
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[ProcessingResult] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    assigned_worker: Optional[str] = None


@dataclass
class Workflow:
    """Workflow definition with multiple tasks"""
    workflow_id: str
    name: str
    description: str
    tasks: List[Task]
    created_at: float = field(default_factory=time.time)
    
    # Runtime fields
    status: TaskStatus = TaskStatus.PENDING
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    progress_percent: float = 0.0


@dataclass
class WorkerNode:
    """Worker node for task execution"""
    worker_id: str
    worker_type: str
    max_concurrent_tasks: int
    current_tasks: int = 0
    total_completed: int = 0
    total_failed: int = 0
    last_heartbeat: float = field(default_factory=time.time)
    is_active: bool = True


class TaskScheduler:
    """Intelligent task scheduler with resource awareness"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.logger = logging.getLogger(f"{__name__}.TaskScheduler")
        
        # Task queues by priority
        self.task_queues = {
            TaskPriority.CRITICAL: [],
            TaskPriority.HIGH: [],
            TaskPriority.NORMAL: [],
            TaskPriority.LOW: []
        }
        
        # Running tasks
        self.running_tasks: Dict[str, Task] = {}
        self.completed_tasks: Dict[str, Task] = {}
        
        # Worker management
        self.workers: Dict[str, WorkerNode] = {}
        
        # Scheduling lock
        self._scheduler_lock = threading.Lock()
        
        # Task execution pool
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_tasks)
        
        # Scheduler thread
        self._scheduler_thread = None
        self._stop_scheduler = threading.Event()
    
    def start_scheduler(self):
        """Start the task scheduler"""
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            return
        
        self._stop_scheduler.clear()
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            daemon=True
        )
        self._scheduler_thread.start()
        
        self.logger.info("Task scheduler started")
    
    def stop_scheduler(self):
        """Stop the task scheduler"""
        self._stop_scheduler.set()
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5.0)
        
        self.executor.shutdown(wait=True)
        self.logger.info("Task scheduler stopped")
    
    def submit_task(self, task: Task) -> str:
        """Submit task for execution"""
        with self._scheduler_lock:
            self.task_queues[task.priority].append(task)
        
        self.logger.info(f"Task {task.task_id} submitted with priority {task.priority.name}")
        return task.task_id
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while not self._stop_scheduler.wait(1.0):  # Check every second
            try:
                self._schedule_next_tasks()
                self._cleanup_completed_tasks()
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
    
    def _schedule_next_tasks(self):
        """Schedule next available tasks"""
        with self._scheduler_lock:
            # Check if we can schedule more tasks
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return
            
            # Find next task to schedule (by priority)
            next_task = None
            for priority in [TaskPriority.CRITICAL, TaskPriority.HIGH, 
                           TaskPriority.NORMAL, TaskPriority.LOW]:
                queue = self.task_queues[priority]
                
                # Find task with satisfied dependencies
                for i, task in enumerate(queue):
                    if self._are_dependencies_satisfied(task):
                        next_task = queue.pop(i)
                        break
                
                if next_task:
                    break
            
            if next_task:
                self._execute_task(next_task)
    
    def _are_dependencies_satisfied(self, task: Task) -> bool:
        """Check if task dependencies are satisfied"""
        for dep_id in task.dependencies:
            if dep_id not in self.completed_tasks:
                return False
            
            dep_task = self.completed_tasks[dep_id]
            if dep_task.status != TaskStatus.COMPLETED:
                return False
        
        return True
    
    def _execute_task(self, task: Task):
        """Execute a task"""
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        
        self.running_tasks[task.task_id] = task
        
        # Submit to thread pool
        future = self.executor.submit(self._run_task, task)
        
        self.logger.info(f"Task {task.task_id} started execution")
    
    def _run_task(self, task: Task):
        """Run a single task"""
        try:
            # Import and instantiate processor
            processor = self._get_processor_instance(task.processor_class)
            
            # Execute task with timeout
            if task.timeout_seconds:
                # Would implement timeout logic here
                pass
            
            # Process the task
            result = processor.process(task.input_data)
            
            # Update task
            task.result = result
            task.status = TaskStatus.COMPLETED if result.success else TaskStatus.FAILED
            task.completed_at = time.time()
            
            if not result.success:
                task.error_message = result.error_message
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = time.time()
            
            self.logger.error(f"Task {task.task_id} failed: {e}")
        
        finally:
            # Move from running to completed
            with self._scheduler_lock:
                if task.task_id in self.running_tasks:
                    del self.running_tasks[task.task_id]
                self.completed_tasks[task.task_id] = task
    
    def _get_processor_instance(self, processor_class: str):
        """Get processor instance by class name"""
        # This would be a factory method to create processor instances
        # For now, return a mock processor
        class MockProcessor(BaseProcessor):
            def process(self, input_data):
                return ProcessingResult(success=True, data={"mock": True})
        
        return MockProcessor()
    
    def _cleanup_completed_tasks(self):
        """Clean up old completed tasks"""
        cutoff_time = time.time() - 3600  # Keep for 1 hour
        
        with self._scheduler_lock:
            to_remove = [
                task_id for task_id, task in self.completed_tasks.items()
                if task.completed_at and task.completed_at < cutoff_time
            ]
            
            for task_id in to_remove:
                del self.completed_tasks[task_id]
    
    def get_task_status(self, task_id: str) -> Optional[Task]:
        """Get task status"""
        with self._scheduler_lock:
            if task_id in self.running_tasks:
                return self.running_tasks[task_id]
            elif task_id in self.completed_tasks:
                return self.completed_tasks[task_id]
            else:
                # Check queues
                for queue in self.task_queues.values():
                    for task in queue:
                        if task.task_id == task_id:
                            return task
        
        return None
    
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """Get scheduler statistics"""
        with self._scheduler_lock:
            total_queued = sum(len(queue) for queue in self.task_queues.values())
            
            return {
                "queued_tasks": total_queued,
                "running_tasks": len(self.running_tasks),
                "completed_tasks": len(self.completed_tasks),
                "max_concurrent": self.max_concurrent_tasks,
                "queue_by_priority": {
                    priority.name: len(queue) 
                    for priority, queue in self.task_queues.items()
                }
            }


class WorkflowEngine:
    """Workflow execution engine"""
    
    def __init__(self, scheduler: TaskScheduler):
        self.scheduler = scheduler
        self.logger = logging.getLogger(f"{__name__}.WorkflowEngine")
        
        # Active workflows
        self.workflows: Dict[str, Workflow] = {}
        
        # Workflow lock
        self._workflow_lock = threading.Lock()
    
    def submit_workflow(self, workflow: Workflow) -> str:
        """Submit workflow for execution"""
        with self._workflow_lock:
            self.workflows[workflow.workflow_id] = workflow
        
        # Submit all tasks
        for task in workflow.tasks:
            self.scheduler.submit_task(task)
        
        workflow.status = TaskStatus.RUNNING
        workflow.started_at = time.time()
        
        self.logger.info(f"Workflow {workflow.workflow_id} submitted with {len(workflow.tasks)} tasks")
        return workflow.workflow_id
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Workflow]:
        """Get workflow status"""
        with self._workflow_lock:
            workflow = self.workflows.get(workflow_id)
            
            if workflow:
                # Update progress
                self._update_workflow_progress(workflow)
            
            return workflow
    
    def _update_workflow_progress(self, workflow: Workflow):
        """Update workflow progress"""
        total_tasks = len(workflow.tasks)
        completed_tasks = 0
        failed_tasks = 0
        
        for task in workflow.tasks:
            task_status = self.scheduler.get_task_status(task.task_id)
            if task_status:
                if task_status.status == TaskStatus.COMPLETED:
                    completed_tasks += 1
                elif task_status.status == TaskStatus.FAILED:
                    failed_tasks += 1
        
        # Update workflow status
        if completed_tasks == total_tasks:
            workflow.status = TaskStatus.COMPLETED
            workflow.completed_at = time.time()
        elif failed_tasks > 0:
            workflow.status = TaskStatus.FAILED
            workflow.completed_at = time.time()
        
        # Update progress
        workflow.progress_percent = (completed_tasks / total_tasks) * 100


class SystemOrchestrator(BaseProcessor):
    """
    Advanced system orchestrator
    
    Coordinates all TJA Generator components, manages workflows,
    and provides intelligent resource allocation and scheduling.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "SystemOrchestrator")
        
        # Initialize configuration
        self.config_manager = UnifiedConfigManager()
        self.path_manager = PathManager()
        
        # Initialize optimization and monitoring
        self.performance_optimizer = PerformanceOptimizer()
        self.system_monitor = SystemMonitor()
        
        # Initialize scheduling components
        max_concurrent = self.config_manager.get_phase_config(1)["hardware"]["parallel_workers"]
        self.task_scheduler = TaskScheduler(max_concurrent)
        self.workflow_engine = WorkflowEngine(self.task_scheduler)
        
        # Start scheduler
        self.task_scheduler.start_scheduler()
        
        self.logger.info("SystemOrchestrator initialized with advanced scheduling")
    
    def process(self, input_data: Any) -> ProcessingResult:
        """Process orchestration request"""
        with self._processing_context("system_orchestration"):
            try:
                request_type = input_data.get("type") if isinstance(input_data, dict) else "status"
                
                if request_type == "submit_task":
                    result = self._submit_task(input_data)
                elif request_type == "submit_workflow":
                    result = self._submit_workflow(input_data)
                elif request_type == "get_status":
                    result = self._get_system_status()
                elif request_type == "get_task_status":
                    result = self._get_task_status(input_data.get("task_id"))
                elif request_type == "get_workflow_status":
                    result = self._get_workflow_status(input_data.get("workflow_id"))
                else:
                    result = {"error": f"Unknown request type: {request_type}"}
                
                return ProcessingResult(
                    success=True,
                    data=result,
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
                
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
    
    def _submit_task(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit a single task"""
        task = Task(
            task_id=str(uuid.uuid4()),
            task_type=request_data.get("task_type", "unknown"),
            processor_class=request_data.get("processor_class", "BaseProcessor"),
            input_data=request_data.get("input_data", {}),
            priority=TaskPriority(request_data.get("priority", 2))
        )
        
        task_id = self.task_scheduler.submit_task(task)
        
        return {
            "task_id": task_id,
            "status": "submitted",
            "message": f"Task {task_id} submitted successfully"
        }
    
    def _submit_workflow(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit a workflow"""
        workflow_data = request_data.get("workflow", {})
        
        # Create tasks
        tasks = []
        for task_data in workflow_data.get("tasks", []):
            task = Task(
                task_id=str(uuid.uuid4()),
                task_type=task_data.get("task_type", "unknown"),
                processor_class=task_data.get("processor_class", "BaseProcessor"),
                input_data=task_data.get("input_data", {}),
                dependencies=task_data.get("dependencies", [])
            )
            tasks.append(task)
        
        # Create workflow
        workflow = Workflow(
            workflow_id=str(uuid.uuid4()),
            name=workflow_data.get("name", "Unnamed Workflow"),
            description=workflow_data.get("description", ""),
            tasks=tasks
        )
        
        workflow_id = self.workflow_engine.submit_workflow(workflow)
        
        return {
            "workflow_id": workflow_id,
            "status": "submitted",
            "tasks_count": len(tasks),
            "message": f"Workflow {workflow_id} submitted successfully"
        }
    
    def _get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        # Get component statuses
        scheduler_stats = self.task_scheduler.get_scheduler_stats()
        system_status = self.system_monitor.get_system_status()
        performance_report = self.performance_optimizer.get_performance_report()
        
        return {
            "timestamp": time.time(),
            "orchestrator_status": "active",
            "scheduler": scheduler_stats,
            "system_health": system_status,
            "performance": performance_report
        }
    
    def _get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get task status"""
        if not task_id:
            return {"error": "Task ID required"}
        
        task = self.task_scheduler.get_task_status(task_id)
        if not task:
            return {"error": f"Task {task_id} not found"}
        
        return {
            "task_id": task_id,
            "status": task.status.value,
            "created_at": task.created_at,
            "started_at": task.started_at,
            "completed_at": task.completed_at,
            "retry_count": task.retry_count,
            "error_message": task.error_message
        }
    
    def _get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status"""
        if not workflow_id:
            return {"error": "Workflow ID required"}
        
        workflow = self.workflow_engine.get_workflow_status(workflow_id)
        if not workflow:
            return {"error": f"Workflow {workflow_id} not found"}
        
        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "progress_percent": workflow.progress_percent,
            "tasks_count": len(workflow.tasks),
            "created_at": workflow.created_at,
            "started_at": workflow.started_at,
            "completed_at": workflow.completed_at
        }
    
    def create_phase_workflow(self, phase_number: int, input_data: Any) -> str:
        """Create a workflow for a specific phase"""
        phase_configs = {
            1: {
                "name": f"Phase {phase_number}: Data Analysis and Preprocessing",
                "tasks": [
                    {
                        "task_type": "tja_discovery",
                        "processor_class": "TjaDataAnalyzer",
                        "input_data": input_data
                    }
                ]
            },
            2: {
                "name": f"Phase {phase_number}: Audio Feature Extraction",
                "tasks": [
                    {
                        "task_type": "audio_processing",
                        "processor_class": "TjaAudioFeatureAnalyzer",
                        "input_data": input_data
                    }
                ]
            }
        }
        
        config = phase_configs.get(phase_number, {
            "name": f"Phase {phase_number}: Unknown",
            "tasks": []
        })
        
        # Submit workflow
        workflow_request = {"workflow": config}
        result = self._submit_workflow(workflow_request)
        
        return result["workflow_id"]
    
    def cleanup(self):
        """Cleanup orchestrator resources"""
        self.task_scheduler.stop_scheduler()
        self.performance_optimizer.cleanup()
        self.system_monitor.cleanup()
        
        self.logger.info("SystemOrchestrator cleanup completed")
