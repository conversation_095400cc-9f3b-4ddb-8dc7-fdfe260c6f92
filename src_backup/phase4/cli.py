"""
TJA Generation Command Line Interface

Production-ready CLI for batch TJA generation with comprehensive options,
progress tracking, and error handling.
"""

import os
import sys
import argparse
import logging
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import traceback

from .pipeline import TJAGeneration<PERSON><PERSON>eline, TJAGenerationError
from .config import PHASE_4_CONFIG
from ..utils.memory_monitor import MemoryMonitor


class TJAGenerationCLI:
    """Command-line interface for TJA generation"""
    
    def __init__(self):
        self.config = PHASE_4_CONFIG
        self.pipeline = None
        self.logger = None
        self.memory_monitor = MemoryMonitor()
        
        # CLI statistics
        self.stats = {
            "total_files": 0,
            "successful": 0,
            "failed": 0,
            "start_time": None,
            "end_time": None
        }
    
    def setup_logging(self, verbose: bool = False, log_file: Optional[str] = None):
        """Setup logging for CLI"""
        level = logging.DEBUG if verbose else logging.INFO
        
        # Create logger
        self.logger = logging.getLogger("tja_cli")
        self.logger.setLevel(level)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        
        # Format
        if verbose:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        else:
            formatter = logging.Formatter('%(levelname)s: %(message)s')
        
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """Create command line argument parser"""
        parser = argparse.ArgumentParser(
            description="TJA Generator v1.0 - Generate TJA files from audio",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Generate TJA for single audio file
  python -m src.phase4.cli song.mp3
  
  # Generate with specific difficulties and output path
  python -m src.phase4.cli song.wav -d 8 9 10 -o output.tja
  
  # Batch process directory
  python -m src.phase4.cli audio_dir/ -r --batch
  
  # Generate with custom metadata
  python -m src.phase4.cli song.mp3 --title "My Song" --artist "Artist" --bpm 120
  
  # Quality assessment only
  python -m src.phase4.cli song.mp3 --assess-only
            """
        )
        
        # Input arguments
        parser.add_argument(
            'input',
            help='Input audio file or directory'
        )
        
        parser.add_argument(
            '-o', '--output',
            help='Output TJA file path (default: same as input with .tja extension)'
        )
        
        parser.add_argument(
            '-d', '--difficulties',
            nargs='+',
            type=int,
            choices=[8, 9, 10],
            default=[8, 9, 10],
            help='Difficulty levels to generate (default: 8 9 10)'
        )
        
        # Processing options
        parser.add_argument(
            '-r', '--recursive',
            action='store_true',
            help='Process directories recursively'
        )
        
        parser.add_argument(
            '--batch',
            action='store_true',
            help='Enable batch processing mode'
        )
        
        parser.add_argument(
            '--resume',
            action='store_true',
            help='Resume processing (skip existing output files)'
        )
        
        # Metadata options
        parser.add_argument(
            '--title',
            help='Song title for TJA metadata'
        )
        
        parser.add_argument(
            '--artist',
            help='Artist name for TJA metadata'
        )
        
        parser.add_argument(
            '--bpm',
            type=float,
            help='BPM for TJA metadata'
        )
        
        parser.add_argument(
            '--offset',
            type=float,
            help='Offset in seconds for TJA metadata'
        )
        
        parser.add_argument(
            '--genre',
            help='Genre for TJA metadata'
        )
        
        # Quality and validation
        parser.add_argument(
            '--assess-only',
            action='store_true',
            help='Only perform quality assessment, do not generate TJA'
        )
        
        parser.add_argument(
            '--min-quality',
            type=float,
            default=0.6,
            help='Minimum quality score to accept (default: 0.6)'
        )
        
        # Output options
        parser.add_argument(
            '--format',
            choices=['tja', 'json'],
            default='tja',
            help='Output format (default: tja)'
        )
        
        parser.add_argument(
            '--save-features',
            action='store_true',
            help='Save extracted audio features'
        )
        
        parser.add_argument(
            '--save-quality-report',
            action='store_true',
            help='Save detailed quality assessment report'
        )
        
        # Logging and debugging
        parser.add_argument(
            '-v', '--verbose',
            action='store_true',
            help='Enable verbose logging'
        )
        
        parser.add_argument(
            '--log-file',
            help='Log file path'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing'
        )
        
        # System options
        parser.add_argument(
            '--memory-limit',
            type=float,
            help='GPU memory limit in GB'
        )
        
        parser.add_argument(
            '--no-gpu',
            action='store_true',
            help='Disable GPU acceleration'
        )
        
        return parser
    
    def find_audio_files(self, input_path: str, recursive: bool = False) -> List[str]:
        """Find audio files in input path"""
        input_path = Path(input_path)
        audio_files = []
        
        supported_formats = self.config["file_formats"]["audio"]["supported_formats"]
        
        if input_path.is_file():
            if input_path.suffix.lower() in supported_formats:
                audio_files.append(str(input_path))
            else:
                self.logger.error(f"Unsupported audio format: {input_path.suffix}")
        
        elif input_path.is_dir():
            pattern = "**/*" if recursive else "*"
            
            for ext in supported_formats:
                files = list(input_path.glob(f"{pattern}{ext}"))
                files.extend(list(input_path.glob(f"{pattern}{ext.upper()}")))
                audio_files.extend([str(f) for f in files])
        
        else:
            self.logger.error(f"Input path not found: {input_path}")
        
        return sorted(audio_files)
    
    def create_metadata(self, args: argparse.Namespace, audio_file: str) -> Dict[str, Any]:
        """Create metadata dictionary from CLI arguments"""
        audio_path = Path(audio_file)
        
        metadata = {}
        
        if args.title:
            metadata["title"] = args.title
        else:
            metadata["title"] = audio_path.stem
        
        if args.artist:
            metadata["artist"] = args.artist
        
        if args.bpm:
            metadata["bpm"] = args.bpm
        
        if args.offset:
            metadata["offset"] = args.offset
        
        if args.genre:
            metadata["genre"] = args.genre
        
        return metadata
    
    def process_single_file(self, audio_file: str, args: argparse.Namespace) -> Dict[str, Any]:
        """Process a single audio file"""
        self.logger.info(f"Processing: {audio_file}")
        
        try:
            # Determine output path
            if args.output:
                output_path = args.output
            else:
                audio_path = Path(audio_file)
                output_path = str(audio_path.with_suffix('.tja'))
            
            # Check if output exists and resume is enabled
            if args.resume and Path(output_path).exists():
                self.logger.info(f"Skipping existing file: {output_path}")
                return {"success": True, "skipped": True, "output_path": output_path}
            
            # Create metadata
            metadata = self.create_metadata(args, audio_file)
            
            # Generate TJA
            if not args.assess_only:
                result = self.pipeline.generate_tja(
                    audio_file_path=audio_file,
                    difficulty_levels=args.difficulties,
                    output_path=output_path,
                    metadata=metadata
                )
                
                # Check quality threshold
                if "quality_metrics" in result:
                    overall_quality = result["quality_metrics"].get("overall_quality", 0.0)
                    if overall_quality < args.min_quality:
                        self.logger.warning(
                            f"Quality score {overall_quality:.3f} below threshold {args.min_quality}"
                        )
                
                self.logger.info(f"Generated: {result['output_file_path']}")
                
                # Save additional outputs if requested
                if args.save_quality_report and "quality_metrics" in result:
                    quality_path = Path(output_path).with_suffix('.quality.json')
                    with open(quality_path, 'w') as f:
                        json.dump(result["quality_metrics"], f, indent=2)
                    self.logger.info(f"Quality report saved: {quality_path}")
                
                return result
            
            else:
                # Assessment only mode
                self.logger.info("Assessment-only mode: TJA generation skipped")
                return {"success": True, "assessment_only": True}
                
        except TJAGenerationError as e:
            self.logger.error(f"Generation failed for {audio_file}: {e}")
            return {"success": False, "error": str(e)}
        
        except Exception as e:
            self.logger.error(f"Unexpected error processing {audio_file}: {e}")
            if args.verbose:
                self.logger.debug(traceback.format_exc())
            return {"success": False, "error": str(e)}
    
    def run(self, args: List[str] = None) -> int:
        """Run the CLI application"""
        parser = self.create_argument_parser()
        args = parser.parse_args(args)
        
        # Setup logging
        self.setup_logging(args.verbose, args.log_file)
        
        self.logger.info("TJA Generator CLI v1.0 starting...")
        
        try:
            # Initialize pipeline
            self.logger.info("Initializing TJA generation pipeline...")
            self.pipeline = TJAGenerationPipeline()
            
            if not self.pipeline.initialize():
                self.logger.error("Pipeline initialization failed")
                return 1
            
            # Find audio files
            audio_files = self.find_audio_files(args.input, args.recursive)
            
            if not audio_files:
                self.logger.error("No audio files found")
                return 1
            
            self.logger.info(f"Found {len(audio_files)} audio file(s)")
            
            if args.dry_run:
                self.logger.info("Dry run mode - files that would be processed:")
                for audio_file in audio_files:
                    self.logger.info(f"  {audio_file}")
                return 0
            
            # Process files
            self.stats["total_files"] = len(audio_files)
            self.stats["start_time"] = time.time()
            
            for i, audio_file in enumerate(audio_files, 1):
                self.logger.info(f"[{i}/{len(audio_files)}] Processing {audio_file}")
                
                result = self.process_single_file(audio_file, args)
                
                if result["success"]:
                    self.stats["successful"] += 1
                else:
                    self.stats["failed"] += 1
                
                # Memory monitoring
                if i % 5 == 0:  # Every 5 files
                    self.memory_monitor.log_memory_status(f"After {i} files")
            
            self.stats["end_time"] = time.time()
            
            # Final statistics
            self.print_final_statistics()
            
            return 0 if self.stats["failed"] == 0 else 1
            
        except KeyboardInterrupt:
            self.logger.info("Processing interrupted by user")
            return 1
        
        except Exception as e:
            self.logger.error(f"CLI execution failed: {e}")
            if args.verbose:
                self.logger.debug(traceback.format_exc())
            return 1
        
        finally:
            if self.pipeline:
                self.pipeline.cleanup()
    
    def print_final_statistics(self):
        """Print final processing statistics"""
        total_time = self.stats["end_time"] - self.stats["start_time"]
        
        self.logger.info("=" * 50)
        self.logger.info("PROCESSING COMPLETE")
        self.logger.info("=" * 50)
        self.logger.info(f"Total files: {self.stats['total_files']}")
        self.logger.info(f"Successful: {self.stats['successful']}")
        self.logger.info(f"Failed: {self.stats['failed']}")
        self.logger.info(f"Success rate: {self.stats['successful']/self.stats['total_files']*100:.1f}%")
        self.logger.info(f"Total time: {total_time:.1f} seconds")
        
        if self.stats["successful"] > 0:
            avg_time = total_time / self.stats["successful"]
            self.logger.info(f"Average time per file: {avg_time:.1f} seconds")
        
        # Memory summary
        memory_summary = self.memory_monitor.get_memory_summary()
        self.logger.info(f"Peak GPU memory: {memory_summary['peak_gpu_usage_gb']:.2f} GB")


def main():
    """Main entry point for CLI"""
    cli = TJAGenerationCLI()
    return cli.run()


if __name__ == "__main__":
    sys.exit(main())
