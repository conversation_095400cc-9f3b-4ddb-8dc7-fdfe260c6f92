"""
Quality Assessment Module

Comprehensive evaluation system for generated TJA files including musical accuracy,
difficulty appropriateness, pattern coherence, and playability assessment.
"""

import numpy as np
import logging
from dataclasses import dataclass
from scipy.stats import entropy


@dataclass
class TJAQualityMetrics:
    """Data class for TJA quality metrics"""
    musical_accuracy: float
    difficulty_appropriateness: float
    pattern_coherence: float
    rhythmic_consistency: float
    playability_score: float
    note_density_analysis: Dict[str, float]
    timing_precision: float
    overall_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "musical_accuracy": self.musical_accuracy,
            "difficulty_appropriateness": self.difficulty_appropriateness,
            "pattern_coherence": self.pattern_coherence,
            "rhythmic_consistency": self.rhythmic_consistency,
            "playability_score": self.playability_score,
            "note_density_analysis": self.note_density_analysis,
            "timing_precision": self.timing_precision,
            "overall_score": self.overall_score
        }


class QualityAssessment:
    """
    Comprehensive quality assessment system for generated TJA sequences
    
    Evaluates multiple aspects of TJA quality:
    - Musical accuracy: How well the notes align with musical structure
    - Difficulty appropriateness: Whether difficulty matches expected patterns
    - Pattern coherence: Consistency and flow of note patterns
    - Rhythmic consistency: Regularity and predictability of rhythm
    - Playability: How playable the chart is for human players
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Quality thresholds
        self.thresholds = config.get("thresholds", {})
        self.min_quality_score = self.thresholds.get("minimum_quality_score", 0.6)
        self.max_note_density = self.thresholds.get("maximum_note_density", 0.8)
        self.min_note_density = self.thresholds.get("minimum_note_density", 0.1)
        self.timing_tolerance_ms = self.thresholds.get("timing_tolerance_ms", 50)
        
        # Expected difficulty characteristics
        self.difficulty_profiles = {
            8: {"note_density": (0.2, 0.4), "pattern_complexity": (0.3, 0.5), "max_consecutive": 4},
            9: {"note_density": (0.3, 0.6), "pattern_complexity": (0.4, 0.7), "max_consecutive": 6},
            10: {"note_density": (0.5, 0.8), "pattern_complexity": (0.6, 0.9), "max_consecutive": 8}
        }
    
    def evaluate_sequence(self, sequence: np.ndarray, 
                         audio_features: Optional[np.ndarray] = None,
                         difficulty_level: int = 9) -> TJAQualityMetrics:
        """
        Evaluate the quality of a generated TJA sequence
        
        Args:
            sequence: Generated note sequence (numpy array)
            audio_features: Corresponding audio features (optional)
            difficulty_level: Target difficulty level (8, 9, or 10)
            
        Returns:
            TJAQualityMetrics object with comprehensive evaluation
        """
        self.logger.debug(f"Evaluating sequence quality for difficulty {difficulty_level}")
        
        # Individual metric evaluations
        musical_accuracy = self._evaluate_musical_accuracy(sequence, audio_features)
        difficulty_appropriateness = self._evaluate_difficulty_appropriateness(sequence, difficulty_level)
        pattern_coherence = self._evaluate_pattern_coherence(sequence)
        rhythmic_consistency = self._evaluate_rhythmic_consistency(sequence)
        playability_score = self._evaluate_playability(sequence, difficulty_level)
        note_density_analysis = self._analyze_note_density(sequence)
        timing_precision = self._evaluate_timing_precision(sequence)
        
        # Calculate overall score (weighted average)
        weights = {
            "musical_accuracy": 0.20,
            "difficulty_appropriateness": 0.20,
            "pattern_coherence": 0.15,
            "rhythmic_consistency": 0.15,
            "playability_score": 0.20,
            "timing_precision": 0.10
        }
        
        overall_score = (
            musical_accuracy * weights["musical_accuracy"] +
            difficulty_appropriateness * weights["difficulty_appropriateness"] +
            pattern_coherence * weights["pattern_coherence"] +
            rhythmic_consistency * weights["rhythmic_consistency"] +
            playability_score * weights["playability_score"] +
            timing_precision * weights["timing_precision"]
        )
        
        return TJAQualityMetrics(
            musical_accuracy=musical_accuracy,
            difficulty_appropriateness=difficulty_appropriateness,
            pattern_coherence=pattern_coherence,
            rhythmic_consistency=rhythmic_consistency,
            playability_score=playability_score,
            note_density_analysis=note_density_analysis,
            timing_precision=timing_precision,
            overall_score=overall_score
        )
    
    def _evaluate_musical_accuracy(self, sequence: np.ndarray, 
                                  audio_features: Optional[np.ndarray] = None) -> float:
        """Evaluate how well the sequence aligns with musical structure"""
        if audio_features is None:
            # Without audio features, use basic heuristics
            return self._evaluate_musical_accuracy_heuristic(sequence)
        
        # With audio features, analyze alignment
        try:
            # Extract rhythmic features from audio
            if len(audio_features.shape) > 1 and audio_features.shape[1] >= 201:
                # Use spectral features for rhythm detection
                spectral_features = audio_features[:, :128]  # Mel spectrogram
                rhythmic_features = audio_features[:, 153:185]  # Rhythmic features
                
                # Calculate onset strength
                onset_strength = np.mean(np.diff(spectral_features, axis=0) ** 2, axis=1)
                onset_strength = np.maximum(0, onset_strength)  # Remove negative values
                
                # Align sequence with onsets
                sequence_onsets = (sequence != 0).astype(float)
                
                # Resize to match if needed
                min_len = min(len(onset_strength), len(sequence_onsets))
                onset_strength = onset_strength[:min_len]
                sequence_onsets = sequence_onsets[:min_len]
                
                # Calculate correlation
                if np.std(onset_strength) > 0 and np.std(sequence_onsets) > 0:
                    correlation = np.corrcoef(onset_strength, sequence_onsets)[0, 1]
                    return max(0.0, min(1.0, (correlation + 1) / 2))  # Normalize to [0, 1]
                
        except Exception as e:
            self.logger.warning(f"Audio-based musical accuracy evaluation failed: {e}")
        
        return self._evaluate_musical_accuracy_heuristic(sequence)
    
    def _evaluate_musical_accuracy_heuristic(self, sequence: np.ndarray) -> float:
        """Heuristic musical accuracy evaluation without audio features"""
        # Look for musical patterns
        score = 0.0
        
        # Check for regular beat patterns
        non_zero_positions = np.where(sequence != 0)[0]
        if len(non_zero_positions) > 1:
            intervals = np.diff(non_zero_positions)
            
            # Prefer regular intervals (4, 8, 16 beats)
            regular_intervals = [4, 8, 16]
            for interval in regular_intervals:
                interval_matches = np.sum(intervals == interval)
                score += interval_matches / len(intervals) * 0.3
        
        # Check for balanced note distribution
        note_counts = np.bincount(sequence, minlength=8)
        note_distribution = note_counts[1:] / max(1, np.sum(note_counts[1:]))  # Exclude blanks
        
        if len(note_distribution) > 0:
            # Prefer balanced distribution (not too concentrated on one note type)
            distribution_entropy = entropy(note_distribution + 1e-8)
            max_entropy = np.log(len(note_distribution))
            score += (distribution_entropy / max_entropy) * 0.4
        
        # Check for appropriate rest patterns
        rest_ratio = np.sum(sequence == 0) / len(sequence)
        if 0.2 <= rest_ratio <= 0.6:  # Good balance of notes and rests
            score += 0.3
        
        return min(1.0, score)
    
    def _evaluate_difficulty_appropriateness(self, sequence: np.ndarray, 
                                           difficulty_level: int) -> float:
        """Evaluate if sequence matches expected difficulty characteristics"""
        if difficulty_level not in self.difficulty_profiles:
            return 0.5  # Neutral score for unknown difficulty
        
        profile = self.difficulty_profiles[difficulty_level]
        score = 0.0
        
        # Note density check
        note_density = np.sum(sequence != 0) / len(sequence)
        expected_density = profile["note_density"]
        
        if expected_density[0] <= note_density <= expected_density[1]:
            score += 0.4
        else:
            # Penalize based on distance from expected range
            if note_density < expected_density[0]:
                score += 0.4 * (note_density / expected_density[0])
            else:
                score += 0.4 * (expected_density[1] / note_density)
        
        # Pattern complexity check
        pattern_complexity = self._calculate_pattern_complexity(sequence)
        expected_complexity = profile["pattern_complexity"]
        
        if expected_complexity[0] <= pattern_complexity <= expected_complexity[1]:
            score += 0.3
        else:
            if pattern_complexity < expected_complexity[0]:
                score += 0.3 * (pattern_complexity / expected_complexity[0])
            else:
                score += 0.3 * (expected_complexity[1] / pattern_complexity)
        
        # Consecutive notes check
        max_consecutive = self._find_max_consecutive_notes(sequence)
        expected_max = profile["max_consecutive"]
        
        if max_consecutive <= expected_max:
            score += 0.3
        else:
            score += 0.3 * (expected_max / max_consecutive)
        
        return min(1.0, score)
    
    def _calculate_pattern_complexity(self, sequence: np.ndarray) -> float:
        """Calculate pattern complexity score"""
        if len(sequence) < 4:
            return 0.0
        
        # Count unique 4-note patterns
        patterns = set()
        for i in range(len(sequence) - 3):
            pattern = tuple(sequence[i:i+4])
            patterns.add(pattern)
        
        # Normalize by maximum possible patterns
        max_patterns = min(len(sequence) - 3, 4**4)  # 4 positions, 8 note types
        complexity = len(patterns) / max_patterns if max_patterns > 0 else 0.0
        
        return min(1.0, complexity)
    
    def _find_max_consecutive_notes(self, sequence: np.ndarray) -> int:
        """Find maximum consecutive non-blank notes"""
        max_consecutive = 0
        current_consecutive = 0
        
        for note in sequence:
            if note != 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _evaluate_pattern_coherence(self, sequence: np.ndarray) -> float:
        """Evaluate coherence and flow of note patterns"""
        if len(sequence) < 8:
            return 0.0
        
        score = 0.0
        
        # Check for repeating patterns
        pattern_repetitions = self._find_pattern_repetitions(sequence, pattern_length=4)
        if pattern_repetitions > 0:
            score += min(0.4, pattern_repetitions * 0.1)
        
        # Check for smooth transitions
        transition_smoothness = self._evaluate_transition_smoothness(sequence)
        score += transition_smoothness * 0.6
        
        return min(1.0, score)
    
    def _find_pattern_repetitions(self, sequence: np.ndarray, pattern_length: int) -> int:
        """Find number of pattern repetitions"""
        if len(sequence) < pattern_length * 2:
            return 0
        
        repetitions = 0
        patterns = {}
        
        for i in range(len(sequence) - pattern_length + 1):
            pattern = tuple(sequence[i:i + pattern_length])
            patterns[pattern] = patterns.get(pattern, 0) + 1
        
        for count in patterns.values():
            if count > 1:
                repetitions += count - 1
        
        return repetitions
    
    def _evaluate_transition_smoothness(self, sequence: np.ndarray) -> float:
        """Evaluate smoothness of note transitions"""
        if len(sequence) < 2:
            return 1.0
        
        smooth_transitions = 0
        total_transitions = 0
        
        for i in range(len(sequence) - 1):
            curr_note = sequence[i]
            next_note = sequence[i + 1]
            
            total_transitions += 1
            
            # Define smooth transitions
            if curr_note == 0 or next_note == 0:
                smooth_transitions += 1  # Transitions with blanks are smooth
            elif abs(curr_note - next_note) <= 1:
                smooth_transitions += 1  # Similar note types are smooth
            elif {curr_note, next_note} in [{1, 2}, {2, 1}]:  # Don-Ka alternation
                smooth_transitions += 1
        
        return smooth_transitions / total_transitions if total_transitions > 0 else 1.0

    def _evaluate_rhythmic_consistency(self, sequence: np.ndarray) -> float:
        """Evaluate rhythmic consistency and regularity"""
        non_zero_positions = np.where(sequence != 0)[0]

        if len(non_zero_positions) < 3:
            return 0.5  # Neutral score for too few notes

        # Calculate intervals between notes
        intervals = np.diff(non_zero_positions)

        # Check for consistent intervals
        interval_consistency = 1.0 - (np.std(intervals) / (np.mean(intervals) + 1e-8))
        interval_consistency = max(0.0, min(1.0, interval_consistency))

        # Check for rhythmic patterns
        common_intervals = [1, 2, 4, 8, 16]  # Common rhythmic intervals
        pattern_score = 0.0

        for interval in common_intervals:
            matches = np.sum(intervals == interval)
            pattern_score += matches / len(intervals)

        pattern_score = min(1.0, pattern_score)

        # Combine scores
        return (interval_consistency * 0.6 + pattern_score * 0.4)

    def _evaluate_playability(self, sequence: np.ndarray, difficulty_level: int) -> float:
        """Evaluate how playable the chart is for human players"""
        score = 0.0

        # Check for impossible patterns
        impossible_patterns = self._detect_impossible_patterns(sequence)
        score += (1.0 - impossible_patterns) * 0.3

        # Check hand alternation for don/ka patterns
        alternation_score = self._evaluate_hand_alternation(sequence)
        score += alternation_score * 0.3

        # Check for appropriate rest periods
        rest_distribution = self._evaluate_rest_distribution(sequence)
        score += rest_distribution * 0.2

        # Check difficulty-appropriate complexity
        complexity_appropriateness = self._evaluate_complexity_appropriateness(sequence, difficulty_level)
        score += complexity_appropriateness * 0.2

        return min(1.0, score)

    def _detect_impossible_patterns(self, sequence: np.ndarray) -> float:
        """Detect patterns that are impossible or very difficult to play"""
        impossible_count = 0
        total_patterns = 0

        # Check for patterns that are too fast or complex
        for i in range(len(sequence) - 2):
            pattern = sequence[i:i+3]
            total_patterns += 1

            # Check for rapid alternation of complex notes
            if all(note in [3, 4, 5, 6, 7] for note in pattern if note != 0):
                impossible_count += 1

            # Check for too many consecutive complex notes
            consecutive_complex = 0
            for note in pattern:
                if note in [5, 6, 7]:  # Drumroll, end_roll, special
                    consecutive_complex += 1
                else:
                    break

            if consecutive_complex >= 3:
                impossible_count += 1

        return impossible_count / total_patterns if total_patterns > 0 else 0.0

    def _evaluate_hand_alternation(self, sequence: np.ndarray) -> float:
        """Evaluate proper hand alternation for don/ka patterns"""
        don_ka_sequence = []

        for note in sequence:
            if note == 1 or note == 3:  # Don (right hand)
                don_ka_sequence.append('R')
            elif note == 2 or note == 4:  # Ka (left hand)
                don_ka_sequence.append('L')

        if len(don_ka_sequence) < 2:
            return 1.0

        # Count proper alternations
        alternations = 0
        for i in range(len(don_ka_sequence) - 1):
            if don_ka_sequence[i] != don_ka_sequence[i + 1]:
                alternations += 1

        return alternations / (len(don_ka_sequence) - 1)

    def _evaluate_rest_distribution(self, sequence: np.ndarray) -> float:
        """Evaluate distribution of rest periods"""
        rest_positions = np.where(sequence == 0)[0]

        if len(rest_positions) == 0:
            return 0.3  # Some penalty for no rests

        # Check for reasonable rest distribution
        rest_ratio = len(rest_positions) / len(sequence)

        if 0.2 <= rest_ratio <= 0.6:
            return 1.0
        elif rest_ratio < 0.2:
            return rest_ratio / 0.2
        else:
            return 0.6 / rest_ratio

    def _evaluate_complexity_appropriateness(self, sequence: np.ndarray,
                                           difficulty_level: int) -> float:
        """Evaluate if complexity matches difficulty level"""
        # Count complex notes (drumrolls, special notes)
        complex_notes = np.sum(np.isin(sequence, [5, 6, 7]))
        complex_ratio = complex_notes / len(sequence)

        # Expected complex note ratios by difficulty
        expected_complex = {8: 0.05, 9: 0.10, 10: 0.20}
        expected = expected_complex.get(difficulty_level, 0.10)

        if complex_ratio <= expected * 1.5:  # Allow some flexibility
            return 1.0
        else:
            return expected * 1.5 / complex_ratio

    def _analyze_note_density(self, sequence: np.ndarray) -> Dict[str, float]:
        """Analyze note density characteristics"""
        total_notes = len(sequence)
        non_blank_notes = np.sum(sequence != 0)

        # Overall density
        overall_density = non_blank_notes / total_notes

        # Local density variation
        window_size = min(16, total_notes // 4)
        if window_size > 0:
            local_densities = []
            for i in range(0, total_notes - window_size + 1, window_size // 2):
                window = sequence[i:i + window_size]
                local_density = np.sum(window != 0) / len(window)
                local_densities.append(local_density)

            density_variation = np.std(local_densities) if local_densities else 0.0
        else:
            density_variation = 0.0

        # Note type distribution
        note_counts = np.bincount(sequence, minlength=8)
        note_distribution = {
            "blank": note_counts[0] / total_notes,
            "don": note_counts[1] / total_notes,
            "ka": note_counts[2] / total_notes,
            "don_big": note_counts[3] / total_notes,
            "ka_big": note_counts[4] / total_notes,
            "drumroll": note_counts[5] / total_notes,
            "end_roll": note_counts[6] / total_notes,
            "special": note_counts[7] / total_notes
        }

        return {
            "overall_density": overall_density,
            "density_variation": density_variation,
            "note_distribution": note_distribution,
            "total_notes": int(non_blank_notes),
            "sequence_length": int(total_notes)
        }

    def _evaluate_timing_precision(self, sequence: np.ndarray) -> float:
        """Evaluate timing precision (simplified without audio alignment)"""
        # Without audio features, evaluate based on sequence regularity
        non_zero_positions = np.where(sequence != 0)[0]

        if len(non_zero_positions) < 2:
            return 1.0

        # Check for regular timing patterns
        intervals = np.diff(non_zero_positions)

        # Prefer intervals that are powers of 2 or common subdivisions
        preferred_intervals = [1, 2, 3, 4, 6, 8, 12, 16]
        precision_score = 0.0

        for interval in intervals:
            if interval in preferred_intervals:
                precision_score += 1.0
            else:
                # Find closest preferred interval
                closest = min(preferred_intervals, key=lambda x: abs(x - interval))
                precision_score += max(0.0, 1.0 - abs(interval - closest) / closest)

        return precision_score / len(intervals) if len(intervals) > 0 else 1.0

    def evaluate_tja_file(self, tja_file_path: str) -> Dict[str, Any]:
        """Evaluate a complete TJA file"""
        # This would parse the TJA file and evaluate each difficulty
        # For now, return a placeholder
        return {
            "file_path": tja_file_path,
            "evaluation_status": "not_implemented",
            "message": "TJA file evaluation not yet implemented"
        }
