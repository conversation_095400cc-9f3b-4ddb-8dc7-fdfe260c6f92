"""
Phase 4 Configuration

Centralized configuration management for the complete TJA generation system.
Integrates configurations from all phases with production-ready defaults.
"""

import os
from pathlib import Path

# Import phase-specific configurations
from ..model import PHASE_3_MODEL_CONFIG
from ..training import PHASE_3_TRAINING_CONFIG

# Base directories
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
OUTPUTS_DIR = PROJECT_ROOT / "outputs"
LOGS_DIR = PROJECT_ROOT / "logs"

# Phase 4 Configuration
PHASE_4_CONFIG = {
    "system": {
        "name": "TJA Generator v1.0",
        "version": "1.0.0",
        "description": "Production-ready TJA generation system",
        "author": "TJA Generator Team",
        "license": "MIT",
        "hardware_target": "RTX 3070 8GB VRAM"
    },
    
    "directories": {
        "project_root": str(PROJECT_ROOT),
        "data_dir": str(DATA_DIR),
        "models_dir": str(MODELS_DIR),
        "outputs_dir": str(OUTPUTS_DIR),
        "logs_dir": str(LOGS_DIR),
        "temp_dir": str(PROJECT_ROOT / "temp"),
        "cache_dir": str(PROJECT_ROOT / "cache")
    },
    
    "pipeline": {
        "max_audio_duration_seconds": 600,  # 10 minutes max (increased)
        "supported_audio_formats": [".mp3", ".wav", ".ogg", ".flac", ".m4a"],
        "output_tja_encoding": "utf-8",
        "temp_file_cleanup": True,
        "parallel_processing": True,
        "max_concurrent_jobs": 4,  # Increased for better throughput
        "memory_limit_gb": 12.0,   # Increased system memory usage
        "gpu_memory_limit_gb": 6.0, # Use 75% of 8GB VRAM
        "batch_processing": {
            "enabled": True,
            "optimal_batch_size": 8,  # Larger batches for efficiency
            "dynamic_batching": True,
            "memory_adaptive": True
        }
    },
    
    "phase1_integration": {
        "tja_parser_module": "src.parsing.custom_tja_parser",
        "validation_enabled": True,
        "metadata_extraction": True,
        "encoding_detection": True,
        "strict_validation": False  # Allow minor format variations
    },
    
    "phase2_integration": {
        "audio_analyzer_module": "src.pipeline.audio_analyzer",
        "feature_extraction_config": {
            "sample_rate": 44100,
            "hop_length": 882,  # ~50fps
            "n_fft": 2048,
            "n_mels": 128,
            "n_mfcc": 13,
            "n_chroma": 12,
            "feature_dims": 201
        },
        "batch_processing": True,
        "hardware_optimization": True
    },
    
    "phase3_integration": {
        "model_config": PHASE_3_MODEL_CONFIG["architecture"],
        "training_config": PHASE_3_TRAINING_CONFIG,
        "model_checkpoint_path": "outputs/phase3_training/best_model.pt",
        "fallback_model_path": "models/tja_generator_v1.pt",
        "inference_batch_size": 4,  # Increased for better GPU utilization
        "generation_parameters": {
            "temperature": 1.0,
            "top_k": 50,
            "top_p": 0.9,
            "max_length": 400,  # Keep compatible with trained model
            "min_length": 50    # Keep compatible with trained model
        },
        "memory_optimization": {
            "use_gradient_checkpointing": True,
            "mixed_precision": False,  # Disabled due to compatibility issues
            "dynamic_batching": True,
            "max_gpu_memory_fraction": 0.75  # Use 75% of 8GB = 6GB
        }
    },
    
    "quality_assessment": {
        "enabled": True,
        "metrics": [
            "musical_accuracy",
            "difficulty_appropriateness", 
            "pattern_coherence",
            "rhythmic_consistency",
            "playability_score",
            "note_density_analysis",
            "timing_precision"
        ],
        "thresholds": {
            "minimum_quality_score": 0.6,
            "maximum_note_density": 0.8,
            "minimum_note_density": 0.1,
            "timing_tolerance_ms": 50
        }
    },
    
    "api": {
        "host": "localhost",
        "port": 8000,
        "debug": False,
        "cors_enabled": True,
        "rate_limiting": {
            "enabled": True,
            "requests_per_minute": 10,
            "burst_size": 5
        },
        "authentication": {
            "enabled": False,  # Disable for demo
            "api_key_required": False
        },
        "file_upload": {
            "max_file_size_mb": 50,
            "allowed_extensions": [".mp3", ".wav", ".ogg", ".flac"],
            "temp_storage_hours": 24
        }
    },
    
    "cli": {
        "default_output_format": "tja",
        "verbose_logging": True,
        "progress_bars": True,
        "batch_processing": True,
        "resume_on_failure": True,
        "backup_original_files": False
    },
    
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file_logging": True,
        "console_logging": True,
        "log_rotation": True,
        "max_log_size_mb": 100,
        "backup_count": 5,
        "performance_logging": True
    },
    
    "monitoring": {
        "enabled": True,
        "metrics_collection": True,
        "resource_monitoring": True,
        "error_tracking": True,
        "performance_profiling": False,  # Disable for production
        "health_checks": True,
        "alerts": {
            "memory_threshold_percent": 85,
            "gpu_memory_threshold_percent": 85,
            "processing_time_threshold_seconds": 300
        }
    },
    
    "deployment": {
        "environment": "production",
        "auto_scaling": False,
        "load_balancing": False,
        "health_check_endpoint": "/health",
        "metrics_endpoint": "/metrics",
        "graceful_shutdown": True,
        "startup_validation": True
    },
    
    "testing": {
        "unit_tests_enabled": True,
        "integration_tests_enabled": True,
        "performance_tests_enabled": True,
        "test_data_dir": "data/test",
        "benchmark_audio_files": [
            "test_short.wav",
            "test_medium.wav",
            "test_long.wav"
        ]
    },

    "file_formats": {
        "tja": {
            "encoding": "utf-8",
            "line_ending": "\n",
            "header_required": True,
            "metadata_validation": True
        },
        "audio": {
            "supported_formats": [".mp3", ".wav", ".ogg", ".flac", ".m4a"],
            "max_duration_seconds": 300,
            "min_duration_seconds": 10,
            "sample_rate_target": 44100
        }
    }
}

# Environment-specific overrides
def load_environment_config() -> Dict[str, Any]:
    """Load environment-specific configuration overrides"""
    env = os.getenv("TJA_ENV", "production").lower()
    
    env_configs = {
        "development": {
            "logging": {"level": "DEBUG"},
            "api": {"debug": True},
            "monitoring": {"performance_profiling": True},
            "pipeline": {"temp_file_cleanup": False}
        },
        "testing": {
            "logging": {"level": "DEBUG"},
            "pipeline": {"max_concurrent_jobs": 1},
            "quality_assessment": {"enabled": False}
        },
        "production": {
            "logging": {"level": "INFO"},
            "api": {"debug": False},
            "monitoring": {"performance_profiling": False}
        }
    }
    
    return env_configs.get(env, {})

def get_config() -> Dict[str, Any]:
    """Get complete configuration with environment overrides"""
    config = PHASE_4_CONFIG.copy()
    env_config = load_environment_config()
    
    # Deep merge environment config
    def deep_merge(base: Dict, override: Dict) -> Dict:
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    return deep_merge(config, env_config)

# Export the final configuration
PHASE_4_CONFIG = get_config()
