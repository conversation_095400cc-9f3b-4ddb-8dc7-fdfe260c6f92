"""
Phase 4: TJA Generator Integration and Deployment Pipeline

This module provides the complete end-to-end TJA generation system that integrates
all previous phases into a production-ready pipeline.

Components:
- TJAGenerationPipeline: Main orchestrator for end-to-end generation
- TJAGenerationAPI: REST API interface for web services
- TJAGenerationCLI: Command-line interface for batch processing
- QualityAssessment: Comprehensive evaluation metrics
- DeploymentManager: Production deployment utilities
"""

from .pipeline import TJAGenerationPipeline
from .cli import TJAGeneration<PERSON><PERSON>
from .quality_assessment import QualityAssessment, TJAQualityMetrics
from .deployment import DeploymentManager
from .config import PHASE_4_CONFIG

# Optional API import
try:
    from .api import TJAGenerationAPI
except ImportError:
    TJAGenerationAPI = None

__version__ = "1.0.0"
__all__ = [
    "TJAGenerationPipeline",
    "TJAGenerationAPI", 
    "TJAGenerationCLI",
    "QualityAssessment",
    "TJAQualityMetrics",
    "DeploymentManager",
    "PHASE_4_CONFIG"
]
