"""
Base Classes for TJA Generator System

Provides standardized base classes implementing SOLID principles,
consistent error handling, logging, and resource management patterns.
"""

import abc
import logging
import time
from pathlib import Path
from dataclasses import dataclass
from contextlib import contextmanager

from .memory_monitor import MemoryMonitor
from .resource_manager import ResourceManager


@dataclass
class ProcessingResult:
    """Standardized processing result structure"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    processing_time_seconds: float = 0.0
    memory_usage_mb: float = 0.0
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class ValidationResult:
    """Standardized validation result structure"""
    is_valid: bool
    quality_score: float = 0.0
    errors: List[str] = None
    warnings: List[str] = None
    metrics: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.metrics is None:
            self.metrics = {}


class BaseProcessor(abc.ABC):
    """
    Abstract base class for all processing components
    
    Implements SOLID principles:
    - Single Responsibility: Each processor has one clear purpose
    - Open/Closed: Extensible through inheritance
    - Liskov Substitution: All processors can be used interchangeably
    - Interface Segregation: Minimal required interface
    - Dependency Inversion: Depends on abstractions, not concretions
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, 
                 logger_name: Optional[str] = None):
        self.config = config or {}
        self.logger = self._setup_logger(logger_name or self.__class__.__name__)
        self.memory_monitor = MemoryMonitor()
        self.resource_manager = ResourceManager()
        
        # Processing statistics
        self._stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }
        
        self.logger.info(f"Initialized {self.__class__.__name__}")
    
    def _setup_logger(self, name: str) -> logging.Logger:
        """Setup standardized logger"""
        logger = logging.getLogger(name)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @contextmanager
    def _processing_context(self, operation_name: str):
        """Context manager for processing operations with monitoring"""
        start_time = time.time()
        start_memory = self.memory_monitor.get_current_usage()
        
        try:
            self.logger.debug(f"Starting {operation_name}")
            yield
            
            # Update success statistics
            self._stats["successful_processed"] += 1
            
        except Exception as e:
            self.logger.error(f"Error in {operation_name}: {e}")
            self._stats["failed_processed"] += 1
            raise
            
        finally:
            # Update timing statistics
            processing_time = time.time() - start_time
            self._stats["total_processed"] += 1
            self._stats["total_processing_time"] += processing_time
            self._stats["average_processing_time"] = (
                self._stats["total_processing_time"] / self._stats["total_processed"]
            )
            
            end_memory = self.memory_monitor.get_current_usage()
            memory_delta = end_memory - start_memory
            
            self.logger.debug(
                f"Completed {operation_name} in {processing_time:.3f}s, "
                f"memory delta: {memory_delta:.1f}MB"
            )
    
    @abc.abstractmethod
    def process(self, input_data: Any) -> ProcessingResult:
        """Abstract method for processing input data"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return self._stats.copy()
    
    def reset_statistics(self):
        """Reset processing statistics"""
        self._stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }


class BaseValidator(abc.ABC):
    """
    Abstract base class for all validation components
    
    Provides consistent validation interface and error handling
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abc.abstractmethod
    def validate(self, data: Any) -> ValidationResult:
        """Abstract method for validating data"""
        pass
    
    def _create_validation_result(self, is_valid: bool, 
                                quality_score: float = 0.0,
                                errors: Optional[List[str]] = None,
                                warnings: Optional[List[str]] = None,
                                metrics: Optional[Dict[str, float]] = None) -> ValidationResult:
        """Helper method to create standardized validation results"""
        return ValidationResult(
            is_valid=is_valid,
            quality_score=quality_score,
            errors=errors or [],
            warnings=warnings or [],
            metrics=metrics or {}
        )


class BaseConfigManager:
    """
    Base configuration manager with standardized path handling
    
    Implements consistent configuration loading and path resolution
    """
    
    def __init__(self, workspace_root: Union[str, Path]):
        self.workspace_root = Path(workspace_root).resolve()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def resolve_path(self, path: Union[str, Path], 
                    relative_to_workspace: bool = True) -> Path:
        """
        Resolve path consistently using forward slashes and workspace root
        
        Args:
            path: Path to resolve
            relative_to_workspace: If True, resolve relative to workspace root
            
        Returns:
            Resolved Path object with forward slash normalization
        """
        path_obj = Path(path)
        
        if relative_to_workspace and not path_obj.is_absolute():
            resolved = self.workspace_root / path_obj
        else:
            resolved = path_obj.resolve()
        
        # Normalize to forward slashes for consistency
        return Path(str(resolved).replace('\\', '/'))
    
    def ensure_directory_exists(self, directory: Union[str, Path]) -> Path:
        """Ensure directory exists, create if necessary"""
        dir_path = self.resolve_path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    def load_config_file(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """Load configuration from file with error handling"""
        config_file = self.resolve_path(config_path)
        
        if not config_file.exists():
            self.logger.warning(f"Config file not found: {config_file}")
            return {}
        
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading config file {config_file}: {e}")
            return {}
