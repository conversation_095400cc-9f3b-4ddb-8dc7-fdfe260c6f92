"""
Model Module - Phase 3: Neural Network Architecture for TJA Generation

Deep learning models for generating high-quality Taiko no Tatsujin charts from audio features.
Optimized for RTX 3070 with 8GB VRAM constraints.
"""

from .tja_generator import TJAGeneratorModel
from .audio_encoder import AudioFeatureEncoder
from .sequence_decoder import SequenceDecoder
from .attention_modules import CrossModalAttention, TemporalAttention
from .pattern_library import Pat<PERSON><PERSON>ibrary
from .loss_functions import TJAGenerationLoss

__all__ = [
    'TJAGeneratorModel',
    'AudioFeatureEncoder', 
    'SequenceDecoder',
    'CrossModalAttention',
    'TemporalAttention',
    'PatternLibrary',
    'TJAGenerationLoss'
]

# Phase 3 model configuration optimized for RTX 3070 (Memory Optimized)
PHASE_3_MODEL_CONFIG = {
    "architecture": {
        "model_type": "transformer_seq2seq",
        "audio_feature_dims": 201,          # Input from Phase 2
        "hidden_dims": 128,                 # Keep compatible with trained model
        "num_attention_heads": 4,           # Keep compatible with trained model
        "num_encoder_layers": 3,            # Keep compatible with trained model
        "num_decoder_layers": 3,            # Keep compatible with trained model
        "dropout": 0.1,
        "max_sequence_length": 400          # Keep compatible with trained model
    },
    "note_types": 8,                    # TJA note types (0-7)
    "difficulty_levels": 3,             # Oni levels 8-10
    "temporal_resolution": 50.0,        # 50 FPS
    "pattern_context_dims": 32,         # Keep compatible with trained model
    "hardware_optimization": {
        "gpu_model": "RTX 3070",
        "max_vram_gb": 5.5,                # More conservative safety margin
        "mixed_precision": True,            # FP16 training
        "gradient_checkpointing": True,     # Memory efficiency
        "batch_size": 1,                   # Reduced from 4 for memory
        "gradient_accumulation_steps": 8    # Increased to maintain effective batch size
    },
    "training": {
        "learning_rate": 1e-4,
        "warmup_steps": 1000,
        "max_epochs": 100,
        "early_stopping_patience": 10,
        "validation_split": 0.15,
        "test_split": 0.15
    }
}
