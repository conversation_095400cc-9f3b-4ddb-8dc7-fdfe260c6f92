"""
Pattern Library

TJA pattern library for encoding common rhythmic patterns and note sequences.
Provides pattern embeddings and pattern-guided generation capabilities.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import json
import numpy as np


class TJAPatternEncoder(nn.Module):
    """Encoder for TJA note patterns into dense embeddings"""
    
    def __init__(self, pattern_vocab_size: int = 1000, embedding_dim: int = 64,
                 max_pattern_length: int = 32):
        super().__init__()
        
        self.pattern_vocab_size = pattern_vocab_size
        self.embedding_dim = embedding_dim
        self.max_pattern_length = max_pattern_length
        
        # Ensure embedding dimensions are at least 1
        embed_quarter = max(1, embedding_dim // 4)

        # Note type embeddings (0=blank, 1=don, 2=ka, 3=don_big, 4=ka_big, etc.)
        self.note_embedding = nn.Embedding(8, embed_quarter)

        # Position embeddings for pattern positions
        self.position_embedding = nn.Embedding(max_pattern_length, embed_quarter)

        # Timing embeddings (quantized timing positions)
        self.timing_embedding = nn.Embedding(16, embed_quarter)  # 16th note resolution

        # Difficulty embeddings
        self.difficulty_embedding = nn.Embedding(3, embed_quarter)  # Oni levels 8-10
        
        # Pattern encoder (input is 4 * embed_quarter)
        combined_embedding_dim = embed_quarter * 4  # note + pos + timing + diff
        self.pattern_encoder = nn.LSTM(
            input_size=combined_embedding_dim,
            hidden_size=embedding_dim,
            num_layers=1,  # Reduced from 2 for memory efficiency
            batch_first=True,
            dropout=0.0    # Disabled dropout for single layer
        )
        
        # Pattern classification head
        self.pattern_classifier = nn.Linear(embedding_dim, pattern_vocab_size)
        
    def encode_pattern(self, note_sequence: torch.Tensor, 
                      timing_sequence: torch.Tensor,
                      difficulty: torch.Tensor) -> torch.Tensor:
        """
        Encode a TJA pattern into dense embedding
        
        Args:
            note_sequence: [batch, pattern_length] note types (0-7)
            timing_sequence: [batch, pattern_length] quantized timing (0-15)
            difficulty: [batch] difficulty level (0-2 for Oni 8-10)
            
        Returns:
            pattern_embedding: [batch, embedding_dim] pattern representation
        """
        batch_size, pattern_length = note_sequence.shape
        
        # Create position indices
        positions = torch.arange(pattern_length, device=note_sequence.device)
        positions = positions.unsqueeze(0).expand(batch_size, -1)
        
        # Get embeddings
        note_emb = self.note_embedding(note_sequence.clamp(0, 7))
        pos_emb = self.position_embedding(positions.clamp(0, self.max_pattern_length - 1))
        timing_emb = self.timing_embedding(timing_sequence.clamp(0, 15))
        diff_emb = self.difficulty_embedding(difficulty.clamp(0, 2))
        
        # Expand difficulty embedding to match sequence length
        diff_emb = diff_emb.unsqueeze(1).expand(-1, pattern_length, -1)
        
        # Combine embeddings
        combined_emb = torch.cat([note_emb, pos_emb, timing_emb, diff_emb], dim=-1)
        
        # Encode with LSTM
        lstm_out, (hidden, cell) = self.pattern_encoder(combined_emb)
        
        # Use final hidden state as pattern embedding
        pattern_embedding = hidden[-1]  # [batch, embedding_dim]
        
        return pattern_embedding
    
    def classify_pattern(self, pattern_embedding: torch.Tensor) -> torch.Tensor:
        """
        Classify pattern embedding into pattern vocabulary
        
        Args:
            pattern_embedding: [batch, embedding_dim] pattern representation
            
        Returns:
            pattern_logits: [batch, pattern_vocab_size] classification logits
        """
        return self.pattern_classifier(pattern_embedding)


class PatternLibrary(nn.Module):
    """
    TJA pattern library with common rhythmic patterns and generation guidance
    
    Maintains a library of common TJA patterns extracted from training data
    and provides pattern-guided generation capabilities.
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        self.pattern_vocab_size = config.get("pattern_vocab_size", 500)  # Reduced from 1000
        self.embedding_dim = config.get("pattern_context_dims", 16)      # Match config
        self.max_pattern_length = config.get("max_pattern_length", 16)   # Reduced from 32
        
        # Pattern encoder
        self.pattern_encoder = TJAPatternEncoder(
            self.pattern_vocab_size,
            self.embedding_dim,
            self.max_pattern_length
        )
        
        # Pattern memory bank
        self.register_buffer(
            "pattern_memory", 
            torch.randn(self.pattern_vocab_size, self.embedding_dim)
        )
        
        # Pattern frequency weights (for sampling)
        self.register_buffer(
            "pattern_frequencies",
            torch.ones(self.pattern_vocab_size)
        )
        
        # Pattern difficulty mapping
        self.register_buffer(
            "pattern_difficulties",
            torch.zeros(self.pattern_vocab_size, dtype=torch.long)
        )
        
        # Pattern retrieval (adjust heads for smaller embedding)
        num_heads = min(4, self.embedding_dim // 4)  # Ensure divisible
        if num_heads == 0:
            num_heads = 1
        self.pattern_retrieval = nn.MultiheadAttention(
            embed_dim=self.embedding_dim,
            num_heads=num_heads,
            batch_first=True
        )
        
        # Pattern fusion (will be dynamically sized)
        self.pattern_fusion = None
        
        # Common TJA patterns (will be loaded from data)
        self.common_patterns = {}
        self._initialize_common_patterns()
        
    def _initialize_common_patterns(self):
        """Initialize common TJA patterns"""
        # Basic patterns (these would be extracted from training data)
        self.common_patterns = {
            "simple_don": [1, 0, 1, 0],                    # Simple don pattern
            "simple_ka": [2, 0, 2, 0],                     # Simple ka pattern
            "don_ka_alternating": [1, 2, 1, 2],            # Alternating don-ka
            "big_don": [3, 0, 0, 0],                       # Big don with rest
            "big_ka": [4, 0, 0, 0],                        # Big ka with rest
            "rapid_don": [1, 1, 1, 1],                     # Rapid don hits
            "rapid_ka": [2, 2, 2, 2],                      # Rapid ka hits
            "mixed_basic": [1, 2, 3, 2],                   # Mixed basic pattern
            "drumroll_start": [5, 0, 0, 0],                # Drumroll start
            "drumroll_end": [6, 0, 0, 0],                  # Drumroll end
            "complex_mixed": [1, 2, 1, 3, 2, 1, 4, 2],     # Complex mixed pattern
        }
        
        # Convert to tensors and store
        self.pattern_tensors = {}
        for name, pattern in self.common_patterns.items():
            self.pattern_tensors[name] = torch.tensor(pattern, dtype=torch.long)
    
    def load_patterns_from_data(self, pattern_data_path: str):
        """Load patterns extracted from training data"""
        try:
            with open(pattern_data_path, 'r') as f:
                pattern_data = json.load(f)
            
            # Update pattern library with real data
            if "common_patterns" in pattern_data:
                self.common_patterns.update(pattern_data["common_patterns"])
            
            if "pattern_frequencies" in pattern_data:
                frequencies = torch.tensor(pattern_data["pattern_frequencies"])
                self.pattern_frequencies[:len(frequencies)] = frequencies
            
            if "pattern_difficulties" in pattern_data:
                difficulties = torch.tensor(pattern_data["pattern_difficulties"])
                self.pattern_difficulties[:len(difficulties)] = difficulties
                
        except FileNotFoundError:
            print(f"Pattern data file not found: {pattern_data_path}")
            print("Using default patterns")
    
    def encode_sequence_patterns(self, note_sequence: torch.Tensor,
                                timing_sequence: torch.Tensor,
                                difficulty: torch.Tensor,
                                pattern_length: int = 8) -> torch.Tensor:
        """
        Encode a sequence into pattern embeddings using sliding window
        
        Args:
            note_sequence: [batch, seq_len] note sequence
            timing_sequence: [batch, seq_len] timing sequence  
            difficulty: [batch] difficulty level
            pattern_length: Length of patterns to extract
            
        Returns:
            pattern_embeddings: [batch, num_patterns, embedding_dim]
        """
        batch_size, seq_len = note_sequence.shape
        
        if seq_len < pattern_length:
            # Pad sequence if too short
            pad_length = pattern_length - seq_len
            note_sequence = F.pad(note_sequence, (0, pad_length), value=0)
            timing_sequence = F.pad(timing_sequence, (0, pad_length), value=0)
            seq_len = pattern_length
        
        # Extract overlapping patterns
        num_patterns = seq_len - pattern_length + 1
        pattern_embeddings = []
        
        for i in range(num_patterns):
            pattern_notes = note_sequence[:, i:i+pattern_length]
            pattern_timings = timing_sequence[:, i:i+pattern_length]
            
            pattern_emb = self.pattern_encoder.encode_pattern(
                pattern_notes, pattern_timings, difficulty
            )
            pattern_embeddings.append(pattern_emb)
        
        # Stack patterns
        pattern_embeddings = torch.stack(pattern_embeddings, dim=1)
        
        return pattern_embeddings
    
    def retrieve_similar_patterns(self, query_embedding: torch.Tensor,
                                 top_k: int = 5) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Retrieve similar patterns from pattern memory
        
        Args:
            query_embedding: [batch, embedding_dim] query pattern
            top_k: Number of similar patterns to retrieve
            
        Returns:
            similar_patterns: [batch, top_k, embedding_dim] similar pattern embeddings
            similarity_scores: [batch, top_k] similarity scores
        """
        batch_size = query_embedding.shape[0]
        
        # Compute similarity with all patterns in memory
        query_norm = F.normalize(query_embedding, dim=-1)
        memory_norm = F.normalize(self.pattern_memory, dim=-1)
        
        # Cosine similarity
        similarities = torch.matmul(query_norm, memory_norm.t())  # [batch, vocab_size]
        
        # Get top-k similar patterns
        top_similarities, top_indices = torch.topk(similarities, top_k, dim=-1)
        
        # Retrieve pattern embeddings
        similar_patterns = self.pattern_memory[top_indices]  # [batch, top_k, embedding_dim]
        
        return similar_patterns, top_similarities
    
    def pattern_guided_attention(self, audio_features: torch.Tensor,
                                pattern_context: torch.Tensor) -> torch.Tensor:
        """
        Apply pattern-guided attention to audio features (simplified for memory efficiency)

        Args:
            audio_features: [batch, seq_len, feature_dim] audio features
            pattern_context: [batch, num_patterns, embedding_dim] pattern context

        Returns:
            guided_features: [batch, seq_len, feature_dim] pattern-guided features
        """
        batch_size, seq_len, feature_dim = audio_features.shape

        # Skip pattern guidance if pattern context is empty or incompatible
        if pattern_context.shape[1] == 0 or pattern_context.shape[2] != self.embedding_dim:
            return audio_features

        # Simple pattern influence: average pattern context and add as bias
        pattern_avg = pattern_context.mean(dim=1, keepdim=True)  # [batch, 1, embedding_dim]
        pattern_avg = pattern_avg.expand(-1, seq_len, -1)        # [batch, seq_len, embedding_dim]

        # Create simple projection if dimensions don't match
        if feature_dim != self.embedding_dim:
            # Use a simple linear transformation
            if not hasattr(self, 'simple_projection'):
                self.simple_projection = nn.Linear(self.embedding_dim, feature_dim).to(audio_features.device)
            pattern_influence = self.simple_projection(pattern_avg)
        else:
            pattern_influence = pattern_avg

        # Add pattern influence with small weight
        guided_features = audio_features + 0.1 * pattern_influence

        return guided_features
    
    def sample_pattern_sequence(self, length: int, difficulty: int = 1,
                               temperature: float = 1.0) -> torch.Tensor:
        """
        Sample a pattern sequence for generation guidance
        
        Args:
            length: Length of sequence to generate
            difficulty: Difficulty level (0-2)
            temperature: Sampling temperature
            
        Returns:
            pattern_sequence: [length] sampled pattern indices
        """
        # Filter patterns by difficulty
        difficulty_mask = (self.pattern_difficulties == difficulty)
        valid_patterns = torch.where(difficulty_mask)[0]
        
        if len(valid_patterns) == 0:
            valid_patterns = torch.arange(self.pattern_vocab_size)
        
        # Get frequencies for valid patterns
        valid_frequencies = self.pattern_frequencies[valid_patterns]
        
        # Apply temperature
        if temperature > 0:
            probs = F.softmax(valid_frequencies / temperature, dim=0)
        else:
            # Greedy selection
            probs = torch.zeros_like(valid_frequencies)
            probs[torch.argmax(valid_frequencies)] = 1.0
        
        # Sample pattern sequence
        pattern_indices = torch.multinomial(probs, length, replacement=True)
        pattern_sequence = valid_patterns[pattern_indices]
        
        return pattern_sequence
    
    def get_pattern_embedding(self, pattern_index: int) -> torch.Tensor:
        """Get embedding for a specific pattern"""
        return self.pattern_memory[pattern_index]
    
    def update_pattern_memory(self, pattern_embeddings: torch.Tensor,
                            pattern_indices: torch.Tensor):
        """Update pattern memory with new patterns"""
        # Update pattern embeddings using exponential moving average
        alpha = 0.1  # Update rate
        
        for i, idx in enumerate(pattern_indices):
            if idx < self.pattern_vocab_size:
                self.pattern_memory[idx] = (
                    (1 - alpha) * self.pattern_memory[idx] + 
                    alpha * pattern_embeddings[i]
                )
    
    def get_pattern_statistics(self) -> Dict[str, torch.Tensor]:
        """Get pattern library statistics"""
        return {
            "pattern_frequencies": self.pattern_frequencies,
            "pattern_difficulties": self.pattern_difficulties,
            "pattern_memory_norm": torch.norm(self.pattern_memory, dim=-1),
            "total_patterns": torch.tensor(self.pattern_vocab_size)
        }
