"""
TJA Generator - Standardized Data Schemas

Optimized and standardized data structures for inter-phase communication
with comprehensive validation and documentation.
"""

from .output_schemas import (
    StandardizedOutput,
    PhaseResult,
    ProcessingMetrics,
    ValidationMetrics,
    HardwareMetrics,
    AudioFeatureSchema,
    TJASequenceSchema,
    ModelOutputSchema,
    InferenceResultSchema
)

from .validation_schemas import (
    SchemaValidator,
    OutputValidator,
    validate_phase_output,
    create_minimal_test_output
)

__all__ = [
    'StandardizedOutput',
    'PhaseResult', 
    'ProcessingMetrics',
    'ValidationMetrics',
    'HardwareMetrics',
    'AudioFeatureSchema',
    'TJASequenceSchema',
    'ModelOutputSchema',
    'InferenceResultSchema',
    'SchemaValidator',
    'OutputValidator',
    'validate_phase_output',
    'create_minimal_test_output'
]
