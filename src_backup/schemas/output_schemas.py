"""
Standardized Output Data Schemas

Optimized data structures for consistent inter-phase communication
with comprehensive validation and minimal test output generation.
"""

import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import json


@dataclass
class ProcessingMetrics:
    """Standardized processing performance metrics"""
    processing_time_seconds: float
    memory_usage_mb: float
    cpu_utilization_percent: float = 0.0
    gpu_utilization_percent: float = 0.0
    gpu_memory_usage_mb: float = 0.0
    throughput_items_per_second: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        return {
            "processing_time_seconds": self.processing_time_seconds,
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_utilization_percent": self.cpu_utilization_percent,
            "gpu_utilization_percent": self.gpu_utilization_percent,
            "gpu_memory_usage_mb": self.gpu_memory_usage_mb,
            "throughput_items_per_second": self.throughput_items_per_second
        }


@dataclass
class ValidationMetrics:
    """Standardized validation quality metrics"""
    is_valid: bool
    quality_score: float  # 0.0 to 1.0
    confidence_score: float = 1.0  # 0.0 to 1.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    detailed_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "is_valid": self.is_valid,
            "quality_score": self.quality_score,
            "confidence_score": self.confidence_score,
            "errors": self.errors,
            "warnings": self.warnings,
            "detailed_metrics": self.detailed_metrics
        }


@dataclass
class HardwareMetrics:
    """Hardware utilization and optimization metrics"""
    target_hardware: str = "RTX 3070"
    optimization_enabled: bool = True
    batch_size_used: int = 0
    parallel_workers: int = 0
    memory_efficiency_percent: float = 0.0
    hardware_acceleration: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "target_hardware": self.target_hardware,
            "optimization_enabled": self.optimization_enabled,
            "batch_size_used": self.batch_size_used,
            "parallel_workers": self.parallel_workers,
            "memory_efficiency_percent": self.memory_efficiency_percent,
            "hardware_acceleration": self.hardware_acceleration
        }


@dataclass
class StandardizedOutput:
    """Base standardized output structure for all phases"""
    phase_number: int
    phase_name: str
    success: bool
    timestamp: str = field(default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()))
    version: str = "2.0.0"
    
    # Core metrics
    processing_metrics: ProcessingMetrics = None
    validation_metrics: ValidationMetrics = None
    hardware_metrics: HardwareMetrics = None
    
    # Phase-specific data
    data: Dict[str, Any] = field(default_factory=dict)
    
    # Output files and paths
    output_files: List[str] = field(default_factory=list)
    output_directory: Optional[str] = None
    
    # Inter-phase communication
    next_phase_ready: bool = False
    handoff_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "phase_metadata": {
                "phase_number": self.phase_number,
                "phase_name": self.phase_name,
                "success": self.success,
                "timestamp": self.timestamp,
                "version": self.version,
                "next_phase_ready": self.next_phase_ready
            },
            "metrics": {
                "processing": self.processing_metrics.to_dict() if self.processing_metrics else {},
                "validation": self.validation_metrics.to_dict() if self.validation_metrics else {},
                "hardware": self.hardware_metrics.to_dict() if self.hardware_metrics else {}
            },
            "data": self.data,
            "outputs": {
                "files": self.output_files,
                "directory": self.output_directory
            },
            "handoff": self.handoff_data
        }
    
    def save_to_file(self, file_path: Union[str, Path]) -> None:
        """Save standardized output to JSON file"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)


@dataclass
class PhaseResult(StandardizedOutput):
    """Enhanced result structure with phase-specific optimizations"""
    
    # Statistics for reporting
    items_processed: int = 0
    items_successful: int = 0
    success_rate: float = 0.0
    
    # Error tracking
    errors: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_error(self, error_type: str, message: str, context: Dict[str, Any] = None):
        """Add error with standardized format"""
        self.errors.append({
            "type": error_type,
            "message": message,
            "context": context or {},
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        })
    
    def calculate_success_rate(self):
        """Calculate and update success rate"""
        if self.items_processed > 0:
            self.success_rate = self.items_successful / self.items_processed
        else:
            self.success_rate = 0.0


# Phase-specific schema definitions

@dataclass
class AudioFeatureSchema:
    """Schema for Phase 2 audio feature outputs"""
    feature_tensor_shape: List[int]  # [T, 201]
    sample_rate: int
    duration_seconds: float
    feature_types: List[str]
    temporal_resolution_ms: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "feature_tensor_shape": self.feature_tensor_shape,
            "sample_rate": self.sample_rate,
            "duration_seconds": self.duration_seconds,
            "feature_types": self.feature_types,
            "temporal_resolution_ms": self.temporal_resolution_ms
        }


@dataclass
class TJASequenceSchema:
    """Schema for Phase 3 TJA sequence outputs"""
    sequence_length: int
    note_types: List[int]  # 0-7 for different note types
    difficulty_level: int
    bpm: float
    total_notes: int
    pattern_complexity: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "sequence_length": self.sequence_length,
            "note_types": self.note_types,
            "difficulty_level": self.difficulty_level,
            "bpm": self.bpm,
            "total_notes": self.total_notes,
            "pattern_complexity": self.pattern_complexity
        }


@dataclass
class ModelOutputSchema:
    """Schema for Phase 4/5 model training outputs"""
    model_architecture: str
    model_parameters: int
    training_epochs: int
    final_loss: float
    validation_accuracy: float
    model_size_mb: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "model_architecture": self.model_architecture,
            "model_parameters": self.model_parameters,
            "training_epochs": self.training_epochs,
            "final_loss": self.final_loss,
            "validation_accuracy": self.validation_accuracy,
            "model_size_mb": self.model_size_mb
        }


@dataclass
class InferenceResultSchema:
    """Schema for Phase 6 inference outputs"""
    input_audio_path: str
    output_tja_path: str
    generated_difficulties: List[str]
    inference_time_seconds: float
    quality_assessment: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "input_audio_path": self.input_audio_path,
            "output_tja_path": self.output_tja_path,
            "generated_difficulties": self.generated_difficulties,
            "inference_time_seconds": self.inference_time_seconds,
            "quality_assessment": self.quality_assessment
        }


def create_phase_result(phase_number: int, phase_name: str, success: bool = True) -> PhaseResult:
    """Factory function to create standardized phase results"""
    return PhaseResult(
        phase_number=phase_number,
        phase_name=phase_name,
        success=success,
        processing_metrics=ProcessingMetrics(0.0, 0.0),
        validation_metrics=ValidationMetrics(True, 1.0),
        hardware_metrics=HardwareMetrics()
    )


def create_minimal_output_sample(phase_number: int) -> Dict[str, Any]:
    """Create minimal test output for validation purposes"""
    base_sample = {
        "phase_metadata": {
            "phase_number": phase_number,
            "phase_name": f"Phase {phase_number} Test",
            "success": True,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "version": "2.0.0",
            "next_phase_ready": True
        },
        "metrics": {
            "processing": {
                "processing_time_seconds": 0.1,
                "memory_usage_mb": 50.0,
                "throughput_items_per_second": 10.0
            },
            "validation": {
                "is_valid": True,
                "quality_score": 0.95,
                "confidence_score": 0.9,
                "errors": [],
                "warnings": []
            },
            "hardware": {
                "target_hardware": "RTX 3070",
                "optimization_enabled": True,
                "hardware_acceleration": True
            }
        },
        "outputs": {
            "files": [f"test_output_phase{phase_number}.json"],
            "directory": f"outputs/phase{phase_number}/test"
        }
    }
    
    # Add phase-specific test data
    if phase_number == 1:
        base_sample["data"] = {
            "songs_processed": 2,
            "songs_successful": 2,
            "phase_2_eligible": 2
        }
    elif phase_number == 2:
        base_sample["data"] = {
            "features_extracted": 2,
            "feature_tensor_shape": [1000, 201],
            "temporal_alignment_accuracy": 0.98
        }
    elif phase_number == 3:
        base_sample["data"] = {
            "sequences_generated": 2,
            "total_notes": 500,
            "pattern_complexity": 0.75
        }
    
    return base_sample
