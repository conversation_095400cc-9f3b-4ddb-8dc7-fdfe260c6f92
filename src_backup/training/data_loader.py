"""
Data Loader

PyTorch data loading pipeline for TJA generation training.
Loads Phase 2 audio features and Phase 1 TJA notation data.
"""

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging


class TJADataset(Dataset):
    """
    Dataset for TJA generation training
    
    Loads audio features from Phase 2 and TJA notation data from Phase 1
    for sequence-to-sequence training.
    """
    
    def __init__(self, catalog_path: str, phase1_catalog_path: str,
                 max_audio_length: int = 500, max_sequence_length: int = 250,  # Reduced for memory
                 difficulty_filter: Optional[List[str]] = None):
        """
        Initialize TJA dataset
        
        Args:
            catalog_path: Path to Phase 3 input catalog (Phase 2 outputs)
            phase1_catalog_path: Path to Phase 1 catalog (TJA notation data)
            max_audio_length: Maximum audio feature sequence length
            max_sequence_length: Maximum TJA sequence length
            difficulty_filter: List of difficulties to include (e.g., ['Oni'])
        """
        self.catalog_path = catalog_path
        self.phase1_catalog_path = phase1_catalog_path
        self.max_audio_length = max_audio_length
        self.max_sequence_length = max_sequence_length
        self.difficulty_filter = difficulty_filter or ['Oni']
        
        self.logger = logging.getLogger(__name__)
        
        # Load catalogs
        self.phase3_catalog = self._load_phase3_catalog()
        self.phase1_catalog = self._load_phase1_catalog()
        
        # Create dataset samples
        self.samples = self._create_samples()
        
        self.logger.info(f"TJADataset initialized with {len(self.samples)} samples")
    
    def _load_phase3_catalog(self) -> Dict[str, Any]:
        """Load Phase 3 input catalog (Phase 2 outputs)"""
        try:
            with open(self.catalog_path, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
            
            self.logger.info(f"Loaded Phase 3 catalog: {len(catalog.get('songs', []))} songs")
            return catalog
            
        except Exception as e:
            self.logger.error(f"Error loading Phase 3 catalog: {e}")
            raise
    
    def _load_phase1_catalog(self) -> Dict[str, Any]:
        """Load Phase 1 catalog (TJA notation data)"""
        try:
            with open(self.phase1_catalog_path, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
            
            self.logger.info(f"Loaded Phase 1 catalog: {len(catalog.get('songs', []))} songs")
            return catalog
            
        except Exception as e:
            self.logger.error(f"Error loading Phase 1 catalog: {e}")
            raise
    
    def _create_samples(self) -> List[Dict[str, Any]]:
        """Create training samples by matching Phase 2 and Phase 1 data"""
        samples = []
        
        # Create lookup for Phase 1 data by song_id
        phase1_lookup = {song["song_id"]: song for song in self.phase1_catalog["songs"]}
        
        for phase3_song in self.phase3_catalog["songs"]:
            song_id = phase3_song["song_id"]
            
            # Find corresponding Phase 1 data
            if song_id not in phase1_lookup:
                continue
            
            phase1_song = phase1_lookup[song_id]
            
            # Check if song is Phase 3 ready
            if not phase3_song.get("phase3_ready", False):
                continue
            
            # Extract samples for each difficulty
            difficulties = phase1_song.get("difficulties", {})
            
            for difficulty_name, difficulty_data in difficulties.items():
                # Filter by difficulty (be more flexible with naming)
                if not any(filter_diff in difficulty_name for filter_diff in self.difficulty_filter):
                    continue
                
                # Check if difficulty has valid data
                if not difficulty_data.get("validation", {}).get("valid", False):
                    continue
                
                # Create sample
                sample = {
                    "song_id": song_id,
                    "difficulty_name": difficulty_name,
                    "feature_tensor_path": phase3_song["feature_tensor_path"],
                    "audio_metadata": phase3_song["audio_metadata"],
                    "feature_metadata": phase3_song["feature_metadata"],
                    "notation_data": difficulty_data["notation_data"],
                    "reference_metadata": phase1_song["reference_metadata"],
                    "difficulty_level": self._map_difficulty_to_level(difficulty_name)
                }
                
                samples.append(sample)
        
        self.logger.info(f"Created {len(samples)} training samples from {len(self.phase3_catalog['songs'])} songs")
        return samples
    
    def _map_difficulty_to_level(self, difficulty_name: str) -> int:
        """Map difficulty name to numeric level"""
        # Map Oni difficulties to 0-2 range
        if "Oni" in difficulty_name:
            # Extract level from difficulty name (e.g., "Oni_8" -> 0, "Oni_9" -> 1, "Oni_10" -> 2)
            if "_8" in difficulty_name:
                return 0
            elif "_9" in difficulty_name:
                return 1
            elif "_10" in difficulty_name:
                return 2
            else:
                return 1  # Default to middle difficulty
        else:
            return 1  # Default for other difficulties
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a training sample"""
        sample = self.samples[idx]
        
        try:
            # Load audio features
            audio_features = self._load_audio_features(sample["feature_tensor_path"])
            
            # Extract TJA sequence
            note_sequence, timing_sequence = self._extract_tja_sequence(sample["notation_data"])
            
            # Create sample dictionary
            return {
                "audio_features": audio_features,
                "note_sequence": note_sequence,
                "timing_sequence": timing_sequence,
                "difficulty": torch.tensor(sample["difficulty_level"], dtype=torch.long),
                "song_id": sample["song_id"],
                "difficulty_name": sample["difficulty_name"]
            }
            
        except Exception as e:
            self.logger.error(f"Error loading sample {idx}: {e}")
            # Return a dummy sample to avoid breaking training
            return self._create_dummy_sample()
    
    def _load_audio_features(self, feature_path: str) -> torch.Tensor:
        """Load audio features from saved tensor file"""
        try:
            # Load the saved feature tensor
            feature_data = torch.load(feature_path, map_location='cpu')
            
            if isinstance(feature_data, dict):
                features = feature_data["features"]
            else:
                features = feature_data
            
            # Ensure correct shape [time, 201]
            if features.dim() == 3:
                features = features.squeeze(0)
            
            # Truncate or pad to max length (memory optimized)
            if features.shape[0] > self.max_audio_length:
                features = features[:self.max_audio_length]
            elif features.shape[0] < self.max_audio_length:
                # Use smaller padding to save memory
                pad_length = self.max_audio_length - features.shape[0]
                features = F.pad(features, (0, 0, 0, pad_length), value=0.0)
            
            return features.float()
            
        except Exception as e:
            self.logger.error(f"Error loading audio features from {feature_path}: {e}")
            # Return dummy features
            return torch.zeros(self.max_audio_length, 201, dtype=torch.float32)
    
    def _extract_tja_sequence(self, notation_data: Dict[str, Any]) -> Tuple[torch.Tensor, torch.Tensor]:
        """Extract TJA note and timing sequences from notation data"""
        try:
            note_sequences = notation_data.get("note_sequences", [])
            
            if not note_sequences:
                # Return empty sequences
                return (torch.zeros(self.max_sequence_length, dtype=torch.long),
                       torch.zeros(self.max_sequence_length, dtype=torch.long))
            
            # Extract notes and timings
            notes = []
            timings = []
            
            for note in note_sequences:
                if isinstance(note, dict):
                    note_type = self._map_note_type(note.get("type", "blank"))
                    timing_ms = note.get("timing_ms", 0.0)
                    
                    notes.append(note_type)
                    timings.append(self._quantize_timing(timing_ms))
            
            # Convert to tensors
            note_tensor = torch.tensor(notes, dtype=torch.long)
            timing_tensor = torch.tensor(timings, dtype=torch.long)
            
            # Truncate or pad to max length
            if len(note_tensor) > self.max_sequence_length:
                note_tensor = note_tensor[:self.max_sequence_length]
                timing_tensor = timing_tensor[:self.max_sequence_length]
            elif len(note_tensor) < self.max_sequence_length:
                pad_length = self.max_sequence_length - len(note_tensor)
                note_tensor = F.pad(note_tensor, (0, pad_length), value=0)
                timing_tensor = F.pad(timing_tensor, (0, pad_length), value=0)
            
            return note_tensor, timing_tensor
            
        except Exception as e:
            self.logger.error(f"Error extracting TJA sequence: {e}")
            # Return dummy sequences
            return (torch.zeros(self.max_sequence_length, dtype=torch.long),
                   torch.zeros(self.max_sequence_length, dtype=torch.long))
    
    def _map_note_type(self, note_type: str) -> int:
        """Map TJA note type string to integer"""
        note_mapping = {
            "blank": 0,
            "don": 1,
            "ka": 2,
            "don_big": 3,
            "ka_big": 4,
            "drumroll": 5,
            "drumroll_big": 5,
            "end_drumroll": 6,
            "special": 7
        }
        
        return note_mapping.get(note_type, 0)
    
    def _quantize_timing(self, timing_ms: float) -> int:
        """Quantize timing to discrete bins"""
        # Quantize to 16th note resolution (assuming 120 BPM base)
        # 120 BPM = 500ms per beat, 125ms per 16th note
        sixteenth_note_ms = 125.0
        
        # Quantize timing to nearest 16th note
        quantized = int(round(timing_ms / sixteenth_note_ms)) % 64  # 64 subdivisions per measure
        
        return quantized
    
    def _create_dummy_sample(self) -> Dict[str, torch.Tensor]:
        """Create a dummy sample for error cases"""
        return {
            "audio_features": torch.zeros(self.max_audio_length, 201, dtype=torch.float32),
            "note_sequence": torch.zeros(self.max_sequence_length, dtype=torch.long),
            "timing_sequence": torch.zeros(self.max_sequence_length, dtype=torch.long),
            "difficulty": torch.tensor(1, dtype=torch.long),
            "song_id": "dummy",
            "difficulty_name": "dummy"
        }


def collate_fn(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """Custom collate function for TJA data"""
    # Stack tensors
    audio_features = torch.stack([item["audio_features"] for item in batch])
    note_sequences = torch.stack([item["note_sequence"] for item in batch])
    timing_sequences = torch.stack([item["timing_sequence"] for item in batch])
    difficulties = torch.stack([item["difficulty"] for item in batch])
    
    # Create attention masks (True for valid positions)
    audio_mask = (audio_features.sum(dim=-1) != 0).float()  # Non-zero audio features
    sequence_mask = (note_sequences != 0).float()  # Non-padding tokens
    
    return {
        "audio_features": audio_features,
        "note_sequence": note_sequences,
        "timing_sequence": timing_sequences,
        "difficulty": difficulties,
        "audio_mask": audio_mask,
        "sequence_mask": sequence_mask,
        "song_ids": [item["song_id"] for item in batch],
        "difficulty_names": [item["difficulty_name"] for item in batch]
    }


class TJADataLoader:
    """Data loader manager for TJA training"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def create_data_loaders(self, catalog_path: str, phase1_catalog_path: str) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        Create train, validation, and test data loaders
        
        Args:
            catalog_path: Path to Phase 3 input catalog
            phase1_catalog_path: Path to Phase 1 catalog
            
        Returns:
            Tuple of (train_loader, val_loader, test_loader)
        """
        # Create full dataset
        full_dataset = TJADataset(
            catalog_path=catalog_path,
            phase1_catalog_path=phase1_catalog_path,
            max_audio_length=self.config["data"]["max_sequence_length"],
            max_sequence_length=self.config["data"]["max_sequence_length"],
            difficulty_filter=['Oni']  # Focus on Oni difficulties
        )
        
        # Calculate split sizes
        total_size = len(full_dataset)
        train_size = int(total_size * self.config["data"]["train_split"])
        val_size = int(total_size * self.config["data"]["val_split"])
        test_size = total_size - train_size - val_size
        
        # Split dataset
        train_dataset, val_dataset, test_dataset = random_split(
            full_dataset, [train_size, val_size, test_size],
            generator=torch.Generator().manual_seed(42)  # Reproducible splits
        )
        
        self.logger.info(f"Dataset splits: train={train_size}, val={val_size}, test={test_size}")
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config["data"]["batch_size"],
            shuffle=self.config["data"]["shuffle"],
            num_workers=self.config["data"]["num_workers"],
            pin_memory=self.config["data"]["pin_memory"],
            collate_fn=collate_fn,
            drop_last=True  # Ensure consistent batch sizes
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config["data"]["batch_size"],
            shuffle=False,
            num_workers=self.config["data"]["num_workers"],
            pin_memory=self.config["data"]["pin_memory"],
            collate_fn=collate_fn,
            drop_last=False
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config["data"]["batch_size"],
            shuffle=False,
            num_workers=self.config["data"]["num_workers"],
            pin_memory=self.config["data"]["pin_memory"],
            collate_fn=collate_fn,
            drop_last=False
        )
        
        return train_loader, val_loader, test_loader
