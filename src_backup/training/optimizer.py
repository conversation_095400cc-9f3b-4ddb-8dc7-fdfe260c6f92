"""
Optimizer and Scheduler

Optimized training configuration for RTX 3070 with mixed precision and
gradient accumulation for effective large batch training.
"""

import torch
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR
from typing import Dict, Any, Optional


def create_optimizer(model: torch.nn.Module, config: Dict[str, Any]) -> torch.optim.Optimizer:
    """
    Create optimizer with configuration optimized for TJA generation
    
    Args:
        model: The model to optimize
        config: Optimization configuration
        
    Returns:
        Configured optimizer
    """
    optimizer_type = config.get("optimizer", "adamw").lower()
    learning_rate = config.get("learning_rate", 1e-4)
    weight_decay = config.get("weight_decay", 0.01)
    
    # Separate parameters for different learning rates
    no_decay = ["bias", "LayerNorm.weight", "layer_norm.weight", "norm.weight"]

    # Get all parameters once to avoid duplicates
    all_params = list(model.named_parameters())

    # Create parameter groups without overlaps
    decay_params = []
    no_decay_params = []

    for n, p in all_params:
        if p.requires_grad:
            if any(nd in n for nd in no_decay):
                no_decay_params.append(p)
            else:
                decay_params.append(p)

    optimizer_grouped_parameters = []

    if decay_params:
        optimizer_grouped_parameters.append({
            "params": decay_params,
            "weight_decay": weight_decay,
            "lr": learning_rate
        })

    if no_decay_params:
        optimizer_grouped_parameters.append({
            "params": no_decay_params,
            "weight_decay": 0.0,
            "lr": learning_rate
        })
    
    if optimizer_type == "adamw":
        optimizer = optim.AdamW(
            optimizer_grouped_parameters,
            lr=learning_rate,
            betas=(config.get("beta1", 0.9), config.get("beta2", 0.999)),
            eps=config.get("eps", 1e-8),
            weight_decay=weight_decay
        )
    elif optimizer_type == "adam":
        optimizer = optim.Adam(
            optimizer_grouped_parameters,
            lr=learning_rate,
            betas=(config.get("beta1", 0.9), config.get("beta2", 0.999)),
            eps=config.get("eps", 1e-8),
            weight_decay=weight_decay
        )
    elif optimizer_type == "sgd":
        optimizer = optim.SGD(
            optimizer_grouped_parameters,
            lr=learning_rate,
            momentum=config.get("momentum", 0.9),
            weight_decay=weight_decay,
            nesterov=config.get("nesterov", True)
        )
    else:
        raise ValueError(f"Unsupported optimizer type: {optimizer_type}")
    
    return optimizer


def create_scheduler(optimizer: torch.optim.Optimizer, 
                    config: Dict[str, Any],
                    num_training_steps: Optional[int] = None) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
    """
    Create learning rate scheduler
    
    Args:
        optimizer: The optimizer to schedule
        config: Scheduler configuration
        num_training_steps: Total number of training steps
        
    Returns:
        Configured scheduler or None
    """
    scheduler_type = config.get("scheduler", "cosine_with_warmup").lower()
    
    if scheduler_type == "none":
        return None
    
    warmup_steps = config.get("warmup_steps", 1000)
    max_steps = config.get("max_steps", num_training_steps)
    
    if max_steps is None:
        max_steps = 50000  # Default
    
    if scheduler_type == "cosine_with_warmup":
        # Warmup followed by cosine annealing
        warmup_scheduler = LinearLR(
            optimizer,
            start_factor=0.01,  # Start at 1% of base LR
            end_factor=1.0,     # End at 100% of base LR
            total_iters=warmup_steps
        )
        
        cosine_scheduler = CosineAnnealingLR(
            optimizer,
            T_max=max_steps - warmup_steps,
            eta_min=config.get("min_lr", 1e-6)
        )
        
        scheduler = SequentialLR(
            optimizer,
            schedulers=[warmup_scheduler, cosine_scheduler],
            milestones=[warmup_steps]
        )
        
    elif scheduler_type == "cosine":
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=max_steps,
            eta_min=config.get("min_lr", 1e-6)
        )
        
    elif scheduler_type == "linear_warmup":
        scheduler = LinearLR(
            optimizer,
            start_factor=0.01,
            end_factor=1.0,
            total_iters=warmup_steps
        )
        
    elif scheduler_type == "exponential":
        scheduler = optim.lr_scheduler.ExponentialLR(
            optimizer,
            gamma=config.get("gamma", 0.95)
        )
        
    elif scheduler_type == "step":
        scheduler = optim.lr_scheduler.StepLR(
            optimizer,
            step_size=config.get("step_size", 10000),
            gamma=config.get("gamma", 0.5)
        )
        
    elif scheduler_type == "plateau":
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=config.get("factor", 0.5),
            patience=config.get("patience", 5),
            min_lr=config.get("min_lr", 1e-6)
        )
        
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")
    
    return scheduler


class GradientAccumulator:
    """
    Gradient accumulation helper for effective large batch training on RTX 3070
    """
    
    def __init__(self, accumulation_steps: int = 4):
        self.accumulation_steps = accumulation_steps
        self.current_step = 0
        self.accumulated_loss = 0.0
        
    def should_update(self) -> bool:
        """Check if gradients should be updated"""
        return (self.current_step + 1) % self.accumulation_steps == 0
    
    def accumulate_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """Accumulate loss and return scaled loss for backward"""
        scaled_loss = loss / self.accumulation_steps
        self.accumulated_loss += loss.item()
        return scaled_loss
    
    def step(self) -> float:
        """Step the accumulator and return average loss"""
        self.current_step += 1
        
        if self.should_update():
            avg_loss = self.accumulated_loss / self.accumulation_steps
            self.accumulated_loss = 0.0
            return avg_loss
        
        return None
    
    def reset(self):
        """Reset accumulator state"""
        self.current_step = 0
        self.accumulated_loss = 0.0


class MixedPrecisionTrainer:
    """
    Mixed precision training helper for RTX 3070 optimization
    """
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled and torch.cuda.is_available()
        
        if self.enabled:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
    
    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """Scale loss for mixed precision training"""
        if self.enabled:
            return self.scaler.scale(loss)
        return loss
    
    def backward(self, loss: torch.Tensor):
        """Backward pass with gradient scaling"""
        if self.enabled:
            self.scaler.scale(loss).backward()
        else:
            loss.backward()
    
    def step(self, optimizer: torch.optim.Optimizer):
        """Optimizer step with gradient unscaling"""
        if self.enabled:
            self.scaler.step(optimizer)
            self.scaler.update()
        else:
            optimizer.step()
    
    def autocast(self):
        """Context manager for automatic mixed precision"""
        if self.enabled:
            return torch.cuda.amp.autocast()
        else:
            # Return a dummy context manager that does nothing
            from contextlib import nullcontext
            return nullcontext()


class GradientClipper:
    """
    Gradient clipping utility for stable training
    """
    
    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        self.max_norm = max_norm
        self.norm_type = norm_type
    
    def clip_gradients(self, model: torch.nn.Module) -> float:
        """
        Clip gradients and return gradient norm
        
        Args:
            model: Model to clip gradients for
            
        Returns:
            Gradient norm before clipping
        """
        if self.max_norm > 0:
            grad_norm = torch.nn.utils.clip_grad_norm_(
                model.parameters(), 
                self.max_norm, 
                norm_type=self.norm_type
            )
            return grad_norm.item()
        else:
            # Just compute gradient norm without clipping
            total_norm = 0.0
            for p in model.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(self.norm_type)
                    total_norm += param_norm.item() ** self.norm_type
            
            return total_norm ** (1.0 / self.norm_type)


def get_parameter_count(model: torch.nn.Module) -> Dict[str, int]:
    """Get detailed parameter count for model"""
    total_params = 0
    trainable_params = 0
    
    param_details = {}
    
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf module
            module_params = sum(p.numel() for p in module.parameters())
            module_trainable = sum(p.numel() for p in module.parameters() if p.requires_grad)
            
            if module_params > 0:
                param_details[name] = {
                    "total": module_params,
                    "trainable": module_trainable
                }
                
                total_params += module_params
                trainable_params += module_trainable
    
    return {
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "non_trainable_parameters": total_params - trainable_params,
        "parameter_details": param_details,
        "model_size_mb": total_params * 4 / (1024 * 1024)  # Assuming float32
    }


def estimate_memory_usage(model: torch.nn.Module, batch_size: int, 
                         sequence_length: int, feature_dim: int = 201) -> Dict[str, float]:
    """
    Estimate GPU memory usage for training
    
    Args:
        model: The model
        batch_size: Training batch size
        sequence_length: Input sequence length
        feature_dim: Feature dimension
        
    Returns:
        Memory usage estimates in GB
    """
    # Model parameters
    param_count = sum(p.numel() for p in model.parameters())
    model_memory = param_count * 4 / (1024**3)  # 4 bytes per float32
    
    # Gradients (same size as parameters)
    gradient_memory = model_memory
    
    # Optimizer states (AdamW: 2x parameters for momentum and variance)
    optimizer_memory = model_memory * 2
    
    # Input tensors
    input_memory = batch_size * sequence_length * feature_dim * 4 / (1024**3)
    
    # Activations (rough estimate: 4x input size for transformer)
    activation_memory = input_memory * 4
    
    # Mixed precision overhead
    mixed_precision_overhead = model_memory * 0.5  # FP16 copy
    
    total_memory = (
        model_memory + gradient_memory + optimizer_memory + 
        input_memory + activation_memory + mixed_precision_overhead
    )
    
    return {
        "model_parameters": model_memory,
        "gradients": gradient_memory,
        "optimizer_states": optimizer_memory,
        "input_tensors": input_memory,
        "activations": activation_memory,
        "mixed_precision_overhead": mixed_precision_overhead,
        "total_estimated": total_memory,
        "safety_margin": total_memory * 1.2  # 20% safety margin
    }
