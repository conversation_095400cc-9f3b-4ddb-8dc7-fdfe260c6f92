"""
Comprehensive Validation Framework

Production-ready multi-layered validation system implementing Phase 6 RFP specifications.
Provides format validation, musical coherence analysis, difficulty assessment,
timing validation, and integration with Phase 4 quality metrics.

Validation Layers:
1. Format Validation - TJA specification compliance
2. Musical Coherence - Pattern analysis and musical structure
3. Difficulty Assessment - Target difficulty matching
4. Timing Validation - Beat alignment and tempo consistency
5. Quality Integration - Phase 4 quality metrics integration
"""

import numpy as np
import time
import logging
from dataclasses import dataclass
import json
from pathlib import Path

from .tja_postprocessing import TJAChart, TJANote
try:
    from ..phase4.quality_assessment import QualityAssessment
    from ..phase4.config import PHASE_4_CONFIG
    PHASE4_AVAILABLE = True
except ImportError:
    PHASE4_AVAILABLE = False
    logging.warning("Phase 4 quality assessment not available")


@dataclass
class ValidationResult:
    """Container for validation results"""
    category: str
    score: float
    passed: bool
    details: Dict[str, Any]
    issues: List[str]
    recommendations: List[str]
    execution_time: float = 0.0
    confidence: float = 1.0


@dataclass
class ComprehensiveValidationReport:
    """Complete validation report with all metrics"""
    overall_score: float
    overall_passed: bool
    validation_time: float
    component_results: Dict[str, ValidationResult]
    summary: Dict[str, Any]
    recommendations: List[str]
    quality_metrics: Optional[Dict[str, Any]] = None
    performance_impact: Dict[str, float] = None


class MusicalCoherenceValidator:
    """
    Musical coherence validation
    
    Validates that generated charts follow musical patterns
    and maintain coherence with the audio content.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Load reference patterns if available
        self.reference_patterns = self._load_reference_patterns()
    
    def _load_reference_patterns(self) -> Dict[str, Any]:
        """Load reference musical patterns"""
        patterns_path = self.config.get("reference_patterns_path")
        
        if patterns_path and Path(patterns_path).exists():
            try:
                with open(patterns_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load reference patterns: {e}")
        
        # Default patterns
        return {
            "common_sequences": [
                [1, 2, 1, 2],  # Don-Ka-Don-Ka
                [1, 1, 2, 2],  # Don-Don-Ka-Ka
                [1, 2, 2, 1],  # Don-Ka-Ka-Don
            ],
            "forbidden_sequences": [
                [1, 1, 1, 1, 1],  # Too many consecutive dons
                [2, 2, 2, 2, 2],  # Too many consecutive kas
            ]
        }
    
    def validate(self, chart: TJAChart, audio_metadata: Dict[str, Any]) -> ValidationResult:
        """Validate musical coherence"""
        issues = []
        recommendations = []
        details = {}
        
        try:
            # Extract note sequence
            note_sequence = [note.note_type for note in chart.notes if note.note_type != 0]
            
            # Pattern analysis
            pattern_score = self._analyze_patterns(note_sequence, issues, recommendations)
            details["pattern_score"] = pattern_score
            
            # Rhythm consistency
            rhythm_score = self._analyze_rhythm_consistency(chart.notes, issues, recommendations)
            details["rhythm_score"] = rhythm_score
            
            # Musical structure
            structure_score = self._analyze_musical_structure(chart, issues, recommendations)
            details["structure_score"] = structure_score
            
            # Phrase coherence
            phrase_score = self._analyze_phrase_coherence(chart.notes, issues, recommendations)
            details["phrase_score"] = phrase_score
            
            # Overall score
            overall_score = np.mean([pattern_score, rhythm_score, structure_score, phrase_score])
            details["overall_score"] = overall_score
            
            passed = overall_score >= 0.7
            
            self.logger.debug(f"Musical coherence validation: {overall_score:.3f}")
            
            return ValidationResult(
                category="musical_coherence",
                score=overall_score,
                passed=passed,
                details=details,
                issues=issues,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Musical coherence validation failed: {e}")
            return ValidationResult(
                category="musical_coherence",
                score=0.0,
                passed=False,
                details={"error": str(e)},
                issues=[f"Validation failed: {str(e)}"],
                recommendations=["Check chart format and content"]
            )
    
    def _analyze_patterns(self, note_sequence: List[int], issues: List[str], recommendations: List[str]) -> float:
        """Analyze note patterns"""
        if len(note_sequence) < 4:
            issues.append("Chart too short for pattern analysis")
            return 0.5
        
        score = 1.0
        
        # Check for forbidden patterns
        forbidden_patterns = self.reference_patterns.get("forbidden_sequences", [])
        for pattern in forbidden_patterns:
            if self._contains_subsequence(note_sequence, pattern):
                issues.append(f"Contains forbidden pattern: {pattern}")
                score -= 0.2
        
        # Check pattern variety
        pattern_variety = self._calculate_pattern_variety(note_sequence)
        if pattern_variety < 0.3:
            issues.append("Low pattern variety")
            recommendations.append("Add more diverse note patterns")
            score -= 0.1
        
        # Check for excessive repetition
        repetition_score = self._check_repetition(note_sequence)
        if repetition_score < 0.5:
            issues.append("Excessive pattern repetition")
            score -= 0.2
        
        return max(0.0, score)
    
    def _analyze_rhythm_consistency(self, notes: List[TJANote], issues: List[str], recommendations: List[str]) -> float:
        """Analyze rhythm consistency"""
        if len(notes) < 2:
            return 1.0
        
        # Calculate inter-note intervals
        intervals = []
        for i in range(1, len(notes)):
            interval = notes[i].time - notes[i-1].time
            intervals.append(interval)
        
        if not intervals:
            return 1.0
        
        # Check for consistent rhythmic patterns
        interval_variance = np.var(intervals)
        mean_interval = np.mean(intervals)
        
        # Normalize variance by mean
        normalized_variance = interval_variance / (mean_interval ** 2) if mean_interval > 0 else 0
        
        # Score based on rhythmic consistency
        consistency_score = max(0.0, 1.0 - normalized_variance)
        
        if consistency_score < 0.6:
            issues.append("Inconsistent rhythm patterns")
            recommendations.append("Improve rhythmic consistency")
        
        return consistency_score
    
    def _analyze_musical_structure(self, chart: TJAChart, issues: List[str], recommendations: List[str]) -> float:
        """Analyze overall musical structure"""
        notes = chart.notes
        if len(notes) < 8:
            issues.append("Chart too short for structure analysis")
            return 0.5
        
        score = 1.0
        
        # Check for proper intro/outro
        intro_notes = notes[:min(8, len(notes)//4)]
        outro_notes = notes[-min(8, len(notes)//4):]
        
        # Intro should be relatively simple
        intro_complexity = self._calculate_complexity(intro_notes)
        if intro_complexity > 0.8:
            issues.append("Intro too complex")
            score -= 0.1
        
        # Check for climax (most complex section)
        climax_found = self._find_climax(notes)
        if not climax_found:
            recommendations.append("Consider adding a climactic section")
            score -= 0.05
        
        # Check measure alignment
        measure_alignment = self._check_measure_alignment(notes)
        if measure_alignment < 0.8:
            issues.append("Poor measure alignment")
            score -= 0.1
        
        return max(0.0, score)
    
    def _analyze_phrase_coherence(self, notes: List[TJANote], issues: List[str], recommendations: List[str]) -> float:
        """Analyze phrase-level coherence"""
        if len(notes) < 16:
            return 1.0
        
        # Divide into phrases (4-measure segments)
        phrases = self._divide_into_phrases(notes)
        
        if len(phrases) < 2:
            return 1.0
        
        # Analyze phrase relationships
        coherence_scores = []
        
        for i in range(1, len(phrases)):
            # Compare consecutive phrases
            similarity = self._calculate_phrase_similarity(phrases[i-1], phrases[i])
            coherence_scores.append(similarity)
        
        # Good coherence means some similarity but not too much
        avg_coherence = np.mean(coherence_scores)
        
        # Optimal coherence is around 0.3-0.7
        if avg_coherence < 0.2:
            issues.append("Phrases too disconnected")
            recommendations.append("Add connecting elements between phrases")
        elif avg_coherence > 0.8:
            issues.append("Phrases too similar")
            recommendations.append("Add more variation between phrases")
        
        # Score based on how close to optimal range
        optimal_score = 1.0 - abs(avg_coherence - 0.5) * 2
        return max(0.0, optimal_score)
    
    def _contains_subsequence(self, sequence: List[int], subsequence: List[int]) -> bool:
        """Check if sequence contains subsequence"""
        if len(subsequence) > len(sequence):
            return False
        
        for i in range(len(sequence) - len(subsequence) + 1):
            if sequence[i:i+len(subsequence)] == subsequence:
                return True
        
        return False
    
    def _calculate_pattern_variety(self, sequence: List[int]) -> float:
        """Calculate pattern variety score"""
        if len(sequence) < 4:
            return 1.0
        
        # Extract 4-note patterns
        patterns = set()
        for i in range(len(sequence) - 3):
            pattern = tuple(sequence[i:i+4])
            patterns.add(pattern)
        
        # Variety is ratio of unique patterns to total possible positions
        max_patterns = len(sequence) - 3
        variety = len(patterns) / max_patterns if max_patterns > 0 else 1.0
        
        return min(1.0, variety)
    
    def _check_repetition(self, sequence: List[int]) -> float:
        """Check for excessive repetition"""
        if len(sequence) < 8:
            return 1.0
        
        # Look for repeated segments
        max_repetition = 0
        
        for length in range(2, len(sequence) // 2):
            for start in range(len(sequence) - length * 2):
                segment = sequence[start:start+length]
                
                # Count consecutive repetitions
                repetitions = 1
                pos = start + length
                
                while pos + length <= len(sequence):
                    if sequence[pos:pos+length] == segment:
                        repetitions += 1
                        pos += length
                    else:
                        break
                
                max_repetition = max(max_repetition, repetitions)
        
        # Score inversely related to max repetition
        score = max(0.0, 1.0 - (max_repetition - 2) * 0.2)
        return score
    
    def _calculate_complexity(self, notes: List[TJANote]) -> float:
        """Calculate complexity of a note sequence"""
        if not notes:
            return 0.0
        
        # Factors: note density, type variety, timing irregularity
        note_types = [note.note_type for note in notes if note.note_type != 0]
        
        if not note_types:
            return 0.0
        
        # Type variety
        unique_types = len(set(note_types))
        type_variety = unique_types / 5.0  # Normalize by max types
        
        # Timing irregularity
        if len(notes) > 1:
            intervals = [notes[i].time - notes[i-1].time for i in range(1, len(notes))]
            timing_variance = np.var(intervals) if intervals else 0
            timing_irregularity = min(1.0, timing_variance * 10)
        else:
            timing_irregularity = 0.0
        
        # Note density (notes per second)
        if len(notes) > 1:
            duration = notes[-1].time - notes[0].time
            density = len(notes) / duration if duration > 0 else 0
            normalized_density = min(1.0, density / 5.0)  # Normalize by 5 notes/sec
        else:
            normalized_density = 0.0
        
        # Combine factors
        complexity = (type_variety + timing_irregularity + normalized_density) / 3.0
        return complexity
    
    def _find_climax(self, notes: List[TJANote]) -> bool:
        """Find if there's a climactic section"""
        if len(notes) < 16:
            return False
        
        # Divide into sections and calculate complexity
        section_size = len(notes) // 4
        sections = []
        
        for i in range(0, len(notes), section_size):
            section = notes[i:i+section_size]
            complexity = self._calculate_complexity(section)
            sections.append(complexity)
        
        if not sections:
            return False
        
        # Check if there's a clear peak
        max_complexity = max(sections)
        avg_complexity = np.mean(sections)
        
        # Climax should be significantly higher than average
        return max_complexity > avg_complexity * 1.3
    
    def _check_measure_alignment(self, notes: List[TJANote]) -> float:
        """Check how well notes align with measure boundaries"""
        if not notes:
            return 1.0
        
        # Count notes that fall on strong beats (measure starts, beat 1, 3)
        strong_beat_notes = 0
        total_notes = 0
        
        for note in notes:
            if note.note_type != 0:
                total_notes += 1
                beat_position = note.position % 4
                
                # Strong beats: 0, 2 (beats 1 and 3)
                if abs(beat_position - round(beat_position)) < 0.1:  # Close to beat
                    rounded_beat = round(beat_position) % 4
                    if rounded_beat in [0, 2]:
                        strong_beat_notes += 1
        
        if total_notes == 0:
            return 1.0
        
        alignment_ratio = strong_beat_notes / total_notes
        return alignment_ratio
    
    def _divide_into_phrases(self, notes: List[TJANote]) -> List[List[TJANote]]:
        """Divide notes into musical phrases"""
        if not notes:
            return []
        
        phrases = []
        current_phrase = []
        
        for note in notes:
            current_phrase.append(note)
            
            # End phrase at measure boundaries (every 4 beats)
            if note.position % 4 < 0.1:  # Close to measure start
                if len(current_phrase) >= 4:  # Minimum phrase length
                    phrases.append(current_phrase)
                    current_phrase = []
        
        # Add remaining notes as final phrase
        if current_phrase:
            phrases.append(current_phrase)
        
        return phrases
    
    def _calculate_phrase_similarity(self, phrase1: List[TJANote], phrase2: List[TJANote]) -> float:
        """Calculate similarity between two phrases"""
        if not phrase1 or not phrase2:
            return 0.0
        
        # Extract note types
        types1 = [note.note_type for note in phrase1 if note.note_type != 0]
        types2 = [note.note_type for note in phrase2 if note.note_type != 0]
        
        if not types1 or not types2:
            return 0.0
        
        # Calculate Jaccard similarity
        set1 = set(types1)
        set2 = set(types2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        jaccard_similarity = intersection / union if union > 0 else 0.0
        
        # Also consider sequence similarity for short phrases
        if len(types1) <= 8 and len(types2) <= 8:
            # Simple sequence similarity
            min_len = min(len(types1), len(types2))
            matches = sum(1 for i in range(min_len) if types1[i] == types2[i])
            sequence_similarity = matches / min_len if min_len > 0 else 0.0
            
            # Combine similarities
            return (jaccard_similarity + sequence_similarity) / 2.0
        
        return jaccard_similarity


class DifficultyValidator:
    """
    Difficulty validation
    
    Validates that generated charts match the target difficulty level
    and maintain appropriate challenge progression.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Load difficulty benchmarks
        self.difficulty_benchmarks = self._load_difficulty_benchmarks()
    
    def _load_difficulty_benchmarks(self) -> Dict[str, Any]:
        """Load difficulty benchmarks"""
        benchmarks_path = self.config.get("difficulty_benchmarks_path")
        
        if benchmarks_path and Path(benchmarks_path).exists():
            try:
                with open(benchmarks_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load difficulty benchmarks: {e}")
        
        # Default benchmarks
        return {
            8: {"notes_per_second": [1.5, 2.5], "complexity": [0.3, 0.5], "pattern_difficulty": [0.4, 0.6]},
            9: {"notes_per_second": [2.0, 3.5], "complexity": [0.4, 0.7], "pattern_difficulty": [0.5, 0.8]},
            10: {"notes_per_second": [3.0, 5.0], "complexity": [0.6, 0.9], "pattern_difficulty": [0.7, 1.0]}
        }
    
    def validate(self, chart: TJAChart, audio_metadata: Dict[str, Any]) -> ValidationResult:
        """Validate difficulty appropriateness"""
        issues = []
        recommendations = []
        details = {}
        
        try:
            target_difficulty = chart.difficulty_level
            benchmarks = self.difficulty_benchmarks.get(target_difficulty, {})
            
            if not benchmarks:
                issues.append(f"No benchmarks available for difficulty {target_difficulty}")
                return ValidationResult(
                    category="difficulty_validation",
                    score=0.5,
                    passed=False,
                    details={"error": "No benchmarks"},
                    issues=issues,
                    recommendations=["Add difficulty benchmarks"]
                )
            
            # Calculate metrics
            notes_per_second = self._calculate_notes_per_second(chart)
            complexity_score = self._calculate_complexity_score(chart)
            pattern_difficulty = self._calculate_pattern_difficulty(chart)
            
            details.update({
                "notes_per_second": notes_per_second,
                "complexity_score": complexity_score,
                "pattern_difficulty": pattern_difficulty,
                "target_difficulty": target_difficulty
            })
            
            # Validate against benchmarks
            nps_score = self._validate_metric(
                notes_per_second, benchmarks.get("notes_per_second", [0, 10]),
                "Notes per second", issues, recommendations
            )
            
            complexity_score_val = self._validate_metric(
                complexity_score, benchmarks.get("complexity", [0, 1]),
                "Complexity", issues, recommendations
            )
            
            pattern_score = self._validate_metric(
                pattern_difficulty, benchmarks.get("pattern_difficulty", [0, 1]),
                "Pattern difficulty", issues, recommendations
            )
            
            # Overall difficulty score
            overall_score = np.mean([nps_score, complexity_score_val, pattern_score])
            details["overall_score"] = overall_score
            
            passed = overall_score >= 0.7
            
            self.logger.debug(f"Difficulty validation: {overall_score:.3f}")
            
            return ValidationResult(
                category="difficulty_validation",
                score=overall_score,
                passed=passed,
                details=details,
                issues=issues,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Difficulty validation failed: {e}")
            return ValidationResult(
                category="difficulty_validation",
                score=0.0,
                passed=False,
                details={"error": str(e)},
                issues=[f"Validation failed: {str(e)}"],
                recommendations=["Check chart format and content"]
            )
    
    def _calculate_notes_per_second(self, chart: TJAChart) -> float:
        """Calculate notes per second"""
        notes = [note for note in chart.notes if note.note_type != 0]
        
        if len(notes) < 2:
            return 0.0
        
        duration = notes[-1].time - notes[0].time
        return len(notes) / duration if duration > 0 else 0.0
    
    def _calculate_complexity_score(self, chart: TJAChart) -> float:
        """Calculate overall complexity score"""
        notes = [note for note in chart.notes if note.note_type != 0]
        
        if not notes:
            return 0.0
        
        # Note type variety
        note_types = [note.note_type for note in notes]
        unique_types = len(set(note_types))
        type_variety = min(1.0, unique_types / 5.0)
        
        # Timing complexity
        if len(notes) > 1:
            intervals = [notes[i].time - notes[i-1].time for i in range(1, len(notes))]
            timing_variance = np.var(intervals)
            timing_complexity = min(1.0, timing_variance * 20)
        else:
            timing_complexity = 0.0
        
        # Pattern complexity
        pattern_complexity = self._calculate_pattern_complexity(note_types)
        
        # Combine factors
        complexity = (type_variety + timing_complexity + pattern_complexity) / 3.0
        return complexity
    
    def _calculate_pattern_difficulty(self, chart: TJAChart) -> float:
        """Calculate pattern-based difficulty"""
        notes = [note for note in chart.notes if note.note_type != 0]
        note_types = [note.note_type for note in notes]
        
        if len(note_types) < 4:
            return 0.0
        
        difficulty_score = 0.0
        
        # Check for difficult patterns
        difficult_patterns = [
            [1, 2, 1, 2, 1, 2],  # Fast alternating
            [3, 4, 3, 4],        # Big note alternating
            [1, 1, 2, 2, 1, 1],  # Complex grouping
        ]
        
        for pattern in difficult_patterns:
            if self._contains_pattern(note_types, pattern):
                difficulty_score += 0.2
        
        # Check for rapid sequences
        for i in range(len(notes) - 3):
            sequence = notes[i:i+4]
            time_span = sequence[-1].time - sequence[0].time
            
            if time_span < 0.5:  # 4 notes in less than 0.5 seconds
                difficulty_score += 0.1
        
        return min(1.0, difficulty_score)
    
    def _calculate_pattern_complexity(self, note_types: List[int]) -> float:
        """Calculate pattern complexity"""
        if len(note_types) < 4:
            return 0.0
        
        # Extract 4-note patterns
        patterns = []
        for i in range(len(note_types) - 3):
            pattern = tuple(note_types[i:i+4])
            patterns.append(pattern)
        
        # Calculate entropy (measure of randomness/complexity)
        from collections import Counter
        pattern_counts = Counter(patterns)
        total_patterns = len(patterns)
        
        entropy = 0.0
        for count in pattern_counts.values():
            probability = count / total_patterns
            entropy -= probability * np.log2(probability)
        
        # Normalize entropy
        max_entropy = np.log2(min(len(patterns), 256))  # Max possible entropy
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0
        
        return normalized_entropy
    
    def _contains_pattern(self, sequence: List[int], pattern: List[int]) -> bool:
        """Check if sequence contains pattern"""
        if len(pattern) > len(sequence):
            return False
        
        for i in range(len(sequence) - len(pattern) + 1):
            if sequence[i:i+len(pattern)] == pattern:
                return True
        
        return False
    
    def _validate_metric(self, value: float, target_range: List[float], 
                        metric_name: str, issues: List[str], recommendations: List[str]) -> float:
        """Validate a metric against target range"""
        min_val, max_val = target_range
        
        if value < min_val:
            issues.append(f"{metric_name} too low: {value:.2f} < {min_val:.2f}")
            recommendations.append(f"Increase {metric_name.lower()}")
            return max(0.0, 1.0 - (min_val - value) / min_val)
        elif value > max_val:
            issues.append(f"{metric_name} too high: {value:.2f} > {max_val:.2f}")
            recommendations.append(f"Decrease {metric_name.lower()}")
            return max(0.0, 1.0 - (value - max_val) / max_val)
        else:
            return 1.0


class TimingValidator:
    """
    Timing validation
    
    Validates timing accuracy and consistency of generated charts.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def validate(self, chart: TJAChart, audio_metadata: Dict[str, Any]) -> ValidationResult:
        """Validate timing accuracy"""
        issues = []
        recommendations = []
        details = {}
        
        try:
            notes = chart.notes
            
            if len(notes) < 2:
                return ValidationResult(
                    category="timing_validation",
                    score=1.0,
                    passed=True,
                    details={"note_count": len(notes)},
                    issues=[],
                    recommendations=[]
                )
            
            # Timing consistency
            consistency_score = self._check_timing_consistency(notes, issues, recommendations)
            details["consistency_score"] = consistency_score
            
            # Beat alignment
            alignment_score = self._check_beat_alignment(notes, chart.bpm, issues, recommendations)
            details["alignment_score"] = alignment_score
            
            # Tempo stability
            tempo_score = self._check_tempo_stability(notes, chart.bpm, issues, recommendations)
            details["tempo_score"] = tempo_score
            
            # Overall timing score
            overall_score = np.mean([consistency_score, alignment_score, tempo_score])
            details["overall_score"] = overall_score
            
            passed = overall_score >= 0.7
            
            self.logger.debug(f"Timing validation: {overall_score:.3f}")
            
            return ValidationResult(
                category="timing_validation",
                score=overall_score,
                passed=passed,
                details=details,
                issues=issues,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Timing validation failed: {e}")
            return ValidationResult(
                category="timing_validation",
                score=0.0,
                passed=False,
                details={"error": str(e)},
                issues=[f"Validation failed: {str(e)}"],
                recommendations=["Check chart timing data"]
            )
    
    def _check_timing_consistency(self, notes: List[TJANote], 
                                 issues: List[str], recommendations: List[str]) -> float:
        """Check timing consistency"""
        if len(notes) < 3:
            return 1.0
        
        # Calculate inter-note intervals
        intervals = []
        for i in range(1, len(notes)):
            if notes[i].note_type != 0 and notes[i-1].note_type != 0:
                interval = notes[i].time - notes[i-1].time
                intervals.append(interval)
        
        if len(intervals) < 2:
            return 1.0
        
        # Check for consistent intervals
        interval_std = np.std(intervals)
        interval_mean = np.mean(intervals)
        
        # Coefficient of variation
        cv = interval_std / interval_mean if interval_mean > 0 else 0
        
        # Good consistency has low coefficient of variation
        consistency_score = max(0.0, 1.0 - cv * 2)
        
        if consistency_score < 0.6:
            issues.append("Inconsistent note timing")
            recommendations.append("Improve timing consistency")
        
        return consistency_score
    
    def _check_beat_alignment(self, notes: List[TJANote], bpm: float,
                             issues: List[str], recommendations: List[str]) -> float:
        """Check alignment with beat grid"""
        beat_duration = 60.0 / bpm
        alignment_errors = []
        
        for note in notes:
            if note.note_type != 0:
                # Calculate expected beat position
                beat_position = (note.time - notes[0].time) / beat_duration
                
                # Find closest beat
                closest_beat = round(beat_position)
                error = abs(beat_position - closest_beat)
                alignment_errors.append(error)
        
        if not alignment_errors:
            return 1.0
        
        # Calculate alignment score
        mean_error = np.mean(alignment_errors)
        max_acceptable_error = 0.1  # 10% of a beat
        
        alignment_score = max(0.0, 1.0 - mean_error / max_acceptable_error)
        
        if alignment_score < 0.7:
            issues.append(f"Poor beat alignment (error: {mean_error:.3f})")
            recommendations.append("Improve beat grid alignment")
        
        return alignment_score
    
    def _check_tempo_stability(self, notes: List[TJANote], bpm: float,
                              issues: List[str], recommendations: List[str]) -> float:
        """Check tempo stability"""
        if len(notes) < 4:
            return 1.0
        
        # Calculate local tempos
        local_tempos = []
        window_size = 4
        
        for i in range(len(notes) - window_size + 1):
            window_notes = notes[i:i+window_size]
            active_notes = [n for n in window_notes if n.note_type != 0]
            
            if len(active_notes) >= 2:
                duration = active_notes[-1].time - active_notes[0].time
                if duration > 0:
                    local_tempo = (len(active_notes) - 1) / duration * 60  # Notes per minute
                    local_tempos.append(local_tempo)
        
        if len(local_tempos) < 2:
            return 1.0
        
        # Check tempo stability
        tempo_std = np.std(local_tempos)
        tempo_mean = np.mean(local_tempos)
        
        # Coefficient of variation for tempo
        tempo_cv = tempo_std / tempo_mean if tempo_mean > 0 else 0
        
        # Good stability has low tempo variation
        stability_score = max(0.0, 1.0 - tempo_cv * 0.5)
        
        if stability_score < 0.6:
            issues.append("Unstable tempo")
            recommendations.append("Maintain consistent tempo")
        
        return stability_score


class TJAValidator:
    """
    Comprehensive TJA validation system
    
    Integrates all validation components to provide complete
    chart validation with detailed feedback and scoring.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize validators
        self.musical_validator = MusicalCoherenceValidator(config)
        self.difficulty_validator = DifficultyValidator(config)
        self.timing_validator = TimingValidator(config)
        
        # Initialize Phase 4 quality assessment
        try:
            self.quality_assessor = QualityAssessment(PHASE_4_CONFIG["quality_assessment"])
        except Exception as e:
            self.logger.warning(f"Failed to initialize Phase 4 quality assessment: {e}")
            self.quality_assessor = None
        
        # Validation weights
        self.validation_weights = config.get("validation_weights", {
            "format_validation": 0.2,
            "musical_validation": 0.3,
            "difficulty_validation": 0.3,
            "timing_validation": 0.2
        })
        
        self.logger.info("TJA validator initialized")
    
    def validate_generated_chart(self, chart: TJAChart, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive validation of generated chart
        
        Args:
            chart: Generated TJA chart
            metadata: Chart metadata and context
            
        Returns:
            Comprehensive validation results
        """
        validation_start = time.time()
        
        try:
            validation_results = {
                "overall_score": 0.0,
                "overall_passed": False,
                "validation_time": 0.0,
                "component_results": {},
                "summary": {
                    "total_issues": 0,
                    "total_recommendations": 0,
                    "critical_issues": [],
                    "improvement_suggestions": []
                }
            }
            
            # Format validation (using TJAFormatValidator from postprocessing)
            from .tja_postprocessing import TJAFormatValidator
            format_validator = TJAFormatValidator()
            format_result = format_validator.validate_chart(chart)
            
            validation_results["component_results"]["format_validation"] = {
                "score": format_result["format_score"],
                "passed": format_result["valid"],
                "issues": format_result["errors"] + format_result["warnings"],
                "details": format_result
            }
            
            # Musical coherence validation
            if self.config.get("enable_musical_validation", True):
                musical_result = self.musical_validator.validate(chart, metadata)
                validation_results["component_results"]["musical_validation"] = {
                    "score": musical_result.score,
                    "passed": musical_result.passed,
                    "issues": musical_result.issues,
                    "recommendations": musical_result.recommendations,
                    "details": musical_result.details
                }
            
            # Difficulty validation
            if self.config.get("enable_difficulty_validation", True):
                difficulty_result = self.difficulty_validator.validate(chart, metadata)
                validation_results["component_results"]["difficulty_validation"] = {
                    "score": difficulty_result.score,
                    "passed": difficulty_result.passed,
                    "issues": difficulty_result.issues,
                    "recommendations": difficulty_result.recommendations,
                    "details": difficulty_result.details
                }
            
            # Timing validation
            if self.config.get("enable_timing_validation", True):
                timing_result = self.timing_validator.validate(chart, metadata)
                validation_results["component_results"]["timing_validation"] = {
                    "score": timing_result.score,
                    "passed": timing_result.passed,
                    "issues": timing_result.issues,
                    "recommendations": timing_result.recommendations,
                    "details": timing_result.details
                }
            
            # Phase 4 quality assessment integration
            if self.quality_assessor:
                try:
                    note_sequence = np.array([note.note_type for note in chart.notes])
                    quality_metrics = self.quality_assessor.evaluate_sequence(
                        note_sequence, difficulty_level=chart.difficulty_level
                    )
                    
                    validation_results["component_results"]["phase4_quality"] = {
                        "score": quality_metrics.overall_score,
                        "passed": quality_metrics.overall_score >= 0.7,
                        "details": {
                            "overall_score": quality_metrics.overall_score,
                            "musical_accuracy": quality_metrics.musical_accuracy,
                            "difficulty_appropriateness": quality_metrics.difficulty_appropriateness,
                            "pattern_coherence": quality_metrics.pattern_coherence,
                            "timing_precision": quality_metrics.timing_precision,
                            "playability": quality_metrics.playability,
                            "density_balance": quality_metrics.density_balance,
                            "structural_integrity": quality_metrics.structural_integrity
                        }
                    }
                except Exception as e:
                    self.logger.warning(f"Phase 4 quality assessment failed: {e}")
            
            # Calculate overall score
            overall_score = self._calculate_overall_score(validation_results["component_results"])
            validation_results["overall_score"] = overall_score
            validation_results["overall_passed"] = overall_score >= self.config.get("minimum_quality_score", 0.7)
            
            # Generate summary
            validation_results["summary"] = self._generate_validation_summary(validation_results["component_results"])
            
            validation_results["validation_time"] = time.time() - validation_start
            
            self.logger.info(f"Chart validation completed: {overall_score:.3f} ({validation_results['validation_time']:.2f}s)")
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Chart validation failed: {e}")
            return {
                "overall_score": 0.0,
                "overall_passed": False,
                "validation_time": time.time() - validation_start,
                "error": str(e),
                "component_results": {},
                "summary": {
                    "total_issues": 1,
                    "critical_issues": [f"Validation failed: {str(e)}"],
                    "improvement_suggestions": ["Check chart format and validation system"]
                }
            }
    
    def _calculate_overall_score(self, component_results: Dict[str, Any]) -> float:
        """Calculate weighted overall validation score"""
        weighted_scores = []
        
        for component, weight in self.validation_weights.items():
            if component in component_results:
                score = component_results[component].get("score", 0.0)
                weighted_scores.append(score * weight)
        
        return sum(weighted_scores) if weighted_scores else 0.0
    
    def _generate_validation_summary(self, component_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate validation summary"""
        all_issues = []
        all_recommendations = []
        critical_issues = []
        
        for component, result in component_results.items():
            issues = result.get("issues", [])
            recommendations = result.get("recommendations", [])
            
            all_issues.extend(issues)
            all_recommendations.extend(recommendations)
            
            # Critical issues are from failed components
            if not result.get("passed", True):
                critical_issues.extend(issues)
        
        return {
            "total_issues": len(all_issues),
            "total_recommendations": len(all_recommendations),
            "critical_issues": critical_issues,
            "improvement_suggestions": list(set(all_recommendations))  # Remove duplicates
        }
