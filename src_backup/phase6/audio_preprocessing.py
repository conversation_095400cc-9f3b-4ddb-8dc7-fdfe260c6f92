"""
Audio Preprocessing Pipeline

Advanced audio preprocessing for TJA inference with spectral, rhythmic,
and temporal feature extraction optimized for real-time performance.
"""

import numpy as np
import torch
import librosa
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# Optional imports with fallbacks
try:
    import librosa
    HAS_LIBROSA = True
except ImportError:
    HAS_LIBROSA = False

try:
    from scipy import signal
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False


@dataclass
class AudioFeatures:
    """Container for extracted audio features"""
    spectral_features: np.ndarray
    rhythmic_features: np.ndarray
    temporal_features: np.ndarray
    timing_grid: Dict[str, Any]
    metadata: Dict[str, Any]


class SpectralFeatureExtractor:
    """
    Spectral feature extraction for TJA generation
    
    Extracts mel-spectrograms, MFCCs, and other spectral features
    optimized for rhythm chart generation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sample_rate = config.get("sample_rate", 44100)
        self.n_mels = config.get("spectral_features", 80)
        self.n_fft = config.get("n_fft", 2048)
        self.hop_length = config.get("hop_length", 512)
        self.logger = logging.getLogger(__name__)
        
        if not HAS_LIBROSA:
            raise ImportError("librosa required for audio processing. Install with: pip install librosa")
    
    def extract_features(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """Extract spectral features from audio"""
        try:
            # Resample if necessary
            if sr != self.sample_rate:
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
            
            # Extract mel-spectrogram
            mel_spec = librosa.feature.melspectrogram(
                y=audio,
                sr=self.sample_rate,
                n_mels=self.n_mels,
                n_fft=self.n_fft,
                hop_length=self.hop_length,
                power=2.0
            )
            
            # Convert to log scale
            log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
            
            # Normalize
            log_mel_spec = (log_mel_spec - np.mean(log_mel_spec)) / (np.std(log_mel_spec) + 1e-8)
            
            # Transpose to (time, frequency) format
            spectral_features = log_mel_spec.T
            
            self.logger.debug(f"Extracted spectral features: {spectral_features.shape}")
            return spectral_features
            
        except Exception as e:
            self.logger.error(f"Spectral feature extraction failed: {e}")
            raise


class RhythmicFeatureExtractor:
    """
    Rhythmic feature extraction for TJA generation
    
    Extracts onset detection, tempo tracking, and beat-related features
    essential for rhythm chart generation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sample_rate = config.get("sample_rate", 44100)
        self.hop_length = config.get("hop_length", 512)
        self.n_rhythmic = config.get("rhythmic_features", 60)
        self.logger = logging.getLogger(__name__)
        
        if not HAS_LIBROSA:
            raise ImportError("librosa required for rhythmic analysis")
    
    def extract_features(self, audio: np.ndarray, sr: int, bpm: float) -> np.ndarray:
        """Extract rhythmic features from audio"""
        try:
            # Resample if necessary
            if sr != self.sample_rate:
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
            
            # Onset detection
            onset_frames = librosa.onset.onset_detect(
                y=audio,
                sr=self.sample_rate,
                hop_length=self.hop_length,
                units='frames'
            )
            
            # Create onset strength signal
            onset_strength = librosa.onset.onset_strength(
                y=audio,
                sr=self.sample_rate,
                hop_length=self.hop_length
            )
            
            # Tempo tracking
            tempo, beats = librosa.beat.beat_track(
                y=audio,
                sr=self.sample_rate,
                hop_length=self.hop_length,
                bpm=bpm,
                units='frames'
            )
            
            # Create beat-aligned features
            n_frames = len(onset_strength)
            rhythmic_features = np.zeros((n_frames, self.n_rhythmic))
            
            # Feature 1-20: Onset strength and derivatives
            rhythmic_features[:, 0] = onset_strength
            if len(onset_strength) > 1:
                rhythmic_features[1:, 1] = np.diff(onset_strength)
            if len(onset_strength) > 2:
                rhythmic_features[2:, 2] = np.diff(onset_strength, n=2)
            
            # Feature 21-40: Beat tracking features
            beat_strength = np.zeros(n_frames)
            for beat_frame in beats:
                if 0 <= beat_frame < n_frames:
                    beat_strength[beat_frame] = 1.0
            
            # Apply Gaussian smoothing to beat strength
            if HAS_SCIPY:
                beat_strength_smooth = signal.gaussian_filter1d(beat_strength, sigma=2.0)
                rhythmic_features[:, 20] = beat_strength_smooth
            else:
                rhythmic_features[:, 20] = beat_strength
            
            # Feature 41-60: Spectral flux and rhythmic patterns
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            
            spectral_rolloff = librosa.feature.spectral_rolloff(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            
            zero_crossing_rate = librosa.feature.zero_crossing_rate(
                y=audio, hop_length=self.hop_length
            )[0]
            
            # Pad or truncate to match frames
            min_len = min(n_frames, len(spectral_centroids), len(spectral_rolloff), len(zero_crossing_rate))
            
            rhythmic_features[:min_len, 40] = spectral_centroids[:min_len]
            rhythmic_features[:min_len, 41] = spectral_rolloff[:min_len]
            rhythmic_features[:min_len, 42] = zero_crossing_rate[:min_len]
            
            # Fill remaining features with derived metrics
            for i in range(43, self.n_rhythmic):
                if i < min_len:
                    # Create additional rhythmic features from combinations
                    feature_idx = (i - 43) % 3
                    if feature_idx == 0:
                        rhythmic_features[:min_len, i] = onset_strength[:min_len] * spectral_centroids[:min_len]
                    elif feature_idx == 1:
                        rhythmic_features[:min_len, i] = beat_strength[:min_len] * spectral_rolloff[:min_len]
                    else:
                        rhythmic_features[:min_len, i] = onset_strength[:min_len] * zero_crossing_rate[:min_len]
            
            # Normalize features
            rhythmic_features = (rhythmic_features - np.mean(rhythmic_features, axis=0)) / (np.std(rhythmic_features, axis=0) + 1e-8)
            
            self.logger.debug(f"Extracted rhythmic features: {rhythmic_features.shape}")
            return rhythmic_features
            
        except Exception as e:
            self.logger.error(f"Rhythmic feature extraction failed: {e}")
            raise


class TemporalFeatureExtractor:
    """
    Temporal feature extraction for TJA generation
    
    Extracts time-domain features and temporal patterns
    relevant for rhythm chart generation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sample_rate = config.get("sample_rate", 44100)
        self.hop_length = config.get("hop_length", 512)
        self.n_temporal = config.get("temporal_features", 61)
        self.logger = logging.getLogger(__name__)
    
    def extract_features(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """Extract temporal features from audio"""
        try:
            # Resample if necessary
            if sr != self.sample_rate:
                if HAS_LIBROSA:
                    audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
                else:
                    # Simple resampling fallback
                    resample_ratio = self.sample_rate / sr
                    new_length = int(len(audio) * resample_ratio)
                    audio = np.interp(
                        np.linspace(0, len(audio) - 1, new_length),
                        np.arange(len(audio)),
                        audio
                    )
            
            # Calculate frame parameters
            n_frames = 1 + (len(audio) - self.hop_length) // self.hop_length
            temporal_features = np.zeros((n_frames, self.n_temporal))
            
            # Extract frame-based features
            for i in range(n_frames):
                start_sample = i * self.hop_length
                end_sample = start_sample + self.hop_length
                frame = audio[start_sample:end_sample]
                
                if len(frame) < self.hop_length:
                    frame = np.pad(frame, (0, self.hop_length - len(frame)), mode='constant')
                
                # Basic temporal features
                temporal_features[i, 0] = np.mean(frame)  # Mean amplitude
                temporal_features[i, 1] = np.std(frame)   # Standard deviation
                temporal_features[i, 2] = np.max(np.abs(frame))  # Peak amplitude
                temporal_features[i, 3] = np.mean(np.abs(frame))  # RMS
                
                # Zero crossing rate
                zero_crossings = np.sum(np.diff(np.sign(frame)) != 0)
                temporal_features[i, 4] = zero_crossings / len(frame)
                
                # Autocorrelation features
                if len(frame) > 1:
                    autocorr = np.correlate(frame, frame, mode='full')
                    autocorr = autocorr[len(autocorr)//2:]
                    if len(autocorr) > 10:
                        temporal_features[i, 5] = np.max(autocorr[1:11])  # Max autocorr in first 10 lags
                
                # Spectral features (if librosa available)
                if HAS_LIBROSA and len(frame) > 0:
                    # Spectral centroid
                    spec_centroid = librosa.feature.spectral_centroid(
                        y=frame, sr=self.sample_rate
                    )
                    temporal_features[i, 6] = np.mean(spec_centroid) if len(spec_centroid[0]) > 0 else 0
                    
                    # Spectral bandwidth
                    spec_bandwidth = librosa.feature.spectral_bandwidth(
                        y=frame, sr=self.sample_rate
                    )
                    temporal_features[i, 7] = np.mean(spec_bandwidth) if len(spec_bandwidth[0]) > 0 else 0
                
                # Fill remaining features with derived metrics
                for j in range(8, min(self.n_temporal, 61)):
                    if j < 20:
                        # Energy-based features
                        temporal_features[i, j] = np.sum(frame ** 2) / len(frame)
                    elif j < 40:
                        # Frequency domain approximations
                        fft_frame = np.fft.fft(frame)
                        temporal_features[i, j] = np.mean(np.abs(fft_frame[:len(fft_frame)//2]))
                    else:
                        # Statistical features
                        temporal_features[i, j] = np.percentile(np.abs(frame), (j-40) * 2.5)
            
            # Normalize features
            temporal_features = (temporal_features - np.mean(temporal_features, axis=0)) / (np.std(temporal_features, axis=0) + 1e-8)
            
            self.logger.debug(f"Extracted temporal features: {temporal_features.shape}")
            return temporal_features
            
        except Exception as e:
            self.logger.error(f"Temporal feature extraction failed: {e}")
            raise


class AudioPreprocessor:
    """
    Complete audio preprocessing pipeline
    
    Integrates spectral, rhythmic, and temporal feature extraction
    to create the 201-dimensional feature vectors for TJA generation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sample_rate = config.get("sample_rate", 44100)
        self.frame_rate = config.get("frame_rate", 50)  # 50 FPS for TJA timing
        self.normalize_audio = config.get("normalize_audio", True)
        self.apply_preemphasis = config.get("apply_preemphasis", True)
        
        # Calculate hop length for desired frame rate
        self.hop_length = int(self.sample_rate / self.frame_rate)
        
        # Update config with calculated hop length
        self.config["hop_length"] = self.hop_length
        
        # Initialize feature extractors
        self.spectral_extractor = SpectralFeatureExtractor(self.config)
        self.rhythmic_extractor = RhythmicFeatureExtractor(self.config)
        self.temporal_extractor = TemporalFeatureExtractor(self.config)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Audio preprocessor initialized (frame_rate={self.frame_rate}, hop_length={self.hop_length})")
    
    def process_audio(self, audio_path: str, bpm: float, offset: float) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Process audio file and extract features
        
        Args:
            audio_path: Path to audio file
            bpm: Beats per minute
            offset: Chart offset in seconds
            
        Returns:
            Tuple of (feature_tensor, timing_grid)
        """
        try:
            # Load audio
            audio, sr = self._load_audio(audio_path)
            
            # Apply preprocessing
            audio = self._preprocess_audio(audio)
            
            # Extract features
            spectral_features = self.spectral_extractor.extract_features(audio, sr)
            rhythmic_features = self.rhythmic_extractor.extract_features(audio, sr, bpm)
            temporal_features = self.temporal_extractor.extract_features(audio, sr)
            
            # Combine features
            combined_features = self._combine_features(
                spectral_features, rhythmic_features, temporal_features
            )
            
            # Create timing grid
            timing_grid = self._create_timing_grid(len(combined_features), bpm, offset)
            
            # Convert to tensor
            feature_tensor = torch.from_numpy(combined_features).float()
            
            self.logger.info(f"Audio processing completed: {feature_tensor.shape}")
            
            return feature_tensor, timing_grid
            
        except Exception as e:
            self.logger.error(f"Audio processing failed: {e}")
            raise
    
    def _load_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """Load audio file"""
        audio_path = Path(audio_path)
        
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        if HAS_LIBROSA:
            audio, sr = librosa.load(str(audio_path), sr=None, mono=True)
        else:
            # Fallback audio loading (very basic)
            raise ImportError("librosa required for audio loading")
        
        self.logger.debug(f"Loaded audio: {len(audio)} samples at {sr} Hz")
        return audio, sr
    
    def _preprocess_audio(self, audio: np.ndarray) -> np.ndarray:
        """Apply audio preprocessing"""
        # Normalize audio
        if self.normalize_audio:
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                audio = audio / max_val
        
        # Apply preemphasis
        if self.apply_preemphasis:
            audio = np.append(audio[0], audio[1:] - 0.97 * audio[:-1])
        
        return audio
    
    def _combine_features(self, 
                         spectral: np.ndarray,
                         rhythmic: np.ndarray, 
                         temporal: np.ndarray) -> np.ndarray:
        """Combine all feature types into 201-dimensional vectors"""
        
        # Ensure all features have the same number of frames
        min_frames = min(len(spectral), len(rhythmic), len(temporal))
        
        spectral = spectral[:min_frames]
        rhythmic = rhythmic[:min_frames]
        temporal = temporal[:min_frames]
        
        # Combine features: 80 + 60 + 61 = 201 dimensions
        combined = np.concatenate([spectral, rhythmic, temporal], axis=1)
        
        # Ensure exactly 201 dimensions
        if combined.shape[1] > 201:
            combined = combined[:, :201]
        elif combined.shape[1] < 201:
            padding = np.zeros((combined.shape[0], 201 - combined.shape[1]))
            combined = np.concatenate([combined, padding], axis=1)
        
        self.logger.debug(f"Combined features shape: {combined.shape}")
        return combined
    
    def _create_timing_grid(self, n_frames: int, bpm: float, offset: float) -> Dict[str, Any]:
        """Create timing grid for TJA generation"""
        
        # Calculate time per frame
        time_per_frame = 1.0 / self.frame_rate
        
        # Create time array
        times = np.arange(n_frames) * time_per_frame + offset
        
        # Calculate beat positions
        beat_duration = 60.0 / bpm
        beat_positions = times / beat_duration
        
        # Calculate measure positions (assuming 4/4 time)
        measure_positions = beat_positions / 4.0
        
        timing_grid = {
            "times": times,
            "beat_positions": beat_positions,
            "measure_positions": measure_positions,
            "bpm": bpm,
            "offset": offset,
            "frame_rate": self.frame_rate,
            "n_frames": n_frames
        }
        
        return timing_grid
