"""
TJA Processor

High-level interface for processing TJA files with metadata/notation separation.
Integrates parsing, separation, and validation for training data preparation.
"""

from pathlib import Path
import logging

from ..parsing.custom_tja_parser import CustomT<PERSON><PERSON>arser, resolve_audio_file_from_wave_field


class TJAProcessor:
    """High-level TJA processing with separation and validation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metadata_separator = MetadataSeparator()
        self.notation_validator = NotationValidator()
    
    def process_tja_file_for_training(self, tja_path: str) -> Dict:
        """
        Process TJA file with strict separation of metadata and notation data
        Focus on extracting pure musical notation patterns for AI training
        CRITICAL: Uses WAVE field for audio file association
        
        Args:
            tja_path: Path to the TJA file
            
        Returns:
            Dictionary with processing results
        """
        try:
            # Initialize custom parser with proper encoding detection
            parser = CustomTJAParser(tja_path)
            
            # Get separated metadata
            reference_metadata = parser.get_reference_metadata()
            notation_metadata = parser.get_notation_metadata()
            
            # CRITICAL: Resolve audio file using WAVE field (specification-compliant)
            wave_field = reference_metadata.get('wave', '')
            audio_pairing_result = resolve_audio_file_from_wave_field(tja_path, wave_field)
            
            # Process each difficulty course
            notation_data = {}
            for course_id, course_data in parser.metadata.course_data.items():
                difficulty_name = parser.DIFFS.get(course_id, f"unknown_{course_id}")
                
                # Extract pure notation sequences and timing commands
                notes, commands = parser.parse_course_notation(course_id)
                
                # Separate reference vs notation course data
                course_reference = {
                    "level": course_data.get("LEVEL", 0),  # Display stars only
                    "balloon": course_data.get("BALLOON", ""),  # Balloon hit counts
                    "scoreinit": course_data.get("SCOREINIT", 300),  # Scoring
                    "scorediff": course_data.get("SCOREDIFF", 100)   # Scoring
                }
                
                course_notation = {
                    "note_sequences": self._extract_pure_note_sequences(notes),
                    "timing_commands": self._extract_timing_commands(commands),
                    "measure_structure": self._extract_measure_structure(notes),
                    "pattern_features": self._extract_rhythmic_patterns(notes),
                    "note_count": len([n for n in notes if n.type != 0]),
                    "measure_count": max([n.measure for n in notes]) + 1 if notes else 0
                }
                
                notation_data[difficulty_name] = {
                    "reference": course_reference,
                    "notation": course_notation
                }
            
            # Create parser result for separation
            parser_result = {
                "reference_metadata": reference_metadata,
                "notation_metadata": notation_metadata,
                "notation_data": notation_data,
                "audio_pairing": audio_pairing_result
            }
            
            # Perform metadata/notation separation
            separation_result = self.metadata_separator.separate_tja_data(parser_result)
            
            # Validate notation data quality
            validation_result = self.notation_validator.validate_notation_data(
                separation_result.notation_data
            )
            
            return {
                "success": True,
                "reference_metadata": separation_result.reference_metadata,
                "notation_metadata": separation_result.notation_metadata,
                "notation_data": separation_result.notation_data,
                "audio_pairing": audio_pairing_result,
                "separation_result": {
                    "quality_score": separation_result.separation_quality_score,
                    "contamination_detected": separation_result.contamination_detected,
                    "warnings": separation_result.warnings
                },
                "validation_result": {
                    "valid": validation_result.valid,
                    "training_ready": validation_result.training_ready,
                    "quality_score": validation_result.quality_score,
                    "warnings": validation_result.warnings,
                    "errors": validation_result.errors,
                    "metrics": validation_result.metrics
                },
                "parser_version": "notation_focused_v1",
                "file_path": str(tja_path)
            }
            
        except Exception as e:
            self.logger.error(f"Error processing {tja_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "parser_version": "notation_focused_v1",
                "file_path": str(tja_path)
            }
    
    def _extract_pure_note_sequences(self, notes: List) -> List[Dict]:
        """Extract pure note sequences without metadata contamination"""
        sequences = []
        for note in notes:
            if hasattr(note, 'type') and note.type != 0:  # Skip blank notes
                sequences.append({
                    "type": self._normalize_note_type(note.type),
                    "position": round(note.position, 6),
                    "measure": note.measure,
                    "timing_ms": round(note.timing_ms, 3),
                    "branch": getattr(note, 'branch', 'normal')
                })
        return sequences
    
    def _extract_timing_commands(self, commands: List) -> List[Dict]:
        """Extract timing-related commands that affect musical structure"""
        timing_commands = []
        timing_command_types = {'BPMCHANGE', 'MEASURE', 'DELAY', 'SCROLL', 'GOGOSTART', 'GOGOEND'}
        
        for command in commands:
            if hasattr(command, 'type') and command.type in timing_command_types:
                timing_commands.append({
                    "type": command.type,
                    "value": command.value,
                    "timing_ms": round(command.timing_ms, 3),
                    "measure": command.measure,
                    "branch": getattr(command, 'branch', 'normal')
                })
        return timing_commands
    
    def _extract_measure_structure(self, notes: List) -> Dict:
        """Extract measure-based structural information"""
        if not notes:
            return {"total_measures": 0, "notes_per_measure": {}, "average_density": 0.0}
        
        notes_per_measure = {}
        for note in notes:
            if hasattr(note, 'type') and note.type != 0:  # Count only actual notes
                measure = note.measure
                notes_per_measure[measure] = notes_per_measure.get(measure, 0) + 1
        
        average_density = (sum(notes_per_measure.values()) / len(notes_per_measure) 
                          if notes_per_measure else 0.0)
        
        return {
            "total_measures": max([n.measure for n in notes]) + 1 if notes else 0,
            "notes_per_measure": notes_per_measure,
            "average_density": round(average_density, 2)
        }
    
    def _extract_rhythmic_patterns(self, notes: List) -> Dict:
        """Extract rhythmic pattern features for training"""
        if not notes:
            return {
                "patterns": [],
                "common_patterns": [],
                "note_type_distribution": {},
                "complexity_score": 0.0
            }
        
        # Filter out blank notes
        actual_notes = [n for n in notes if hasattr(n, 'type') and n.type != 0]
        
        if not actual_notes:
            return {
                "patterns": [],
                "common_patterns": [],
                "note_type_distribution": {},
                "complexity_score": 0.0
            }
        
        # Note type distribution
        note_types = [self._normalize_note_type(n.type) for n in actual_notes]
        type_counts = {}
        for note_type in note_types:
            type_counts[note_type] = type_counts.get(note_type, 0) + 1
        
        # Calculate note intervals for complexity
        note_intervals = []
        for i in range(1, len(actual_notes)):
            interval = actual_notes[i].timing_ms - actual_notes[i-1].timing_ms
            if interval > 0:  # Valid interval
                note_intervals.append(interval)
        
        # Calculate complexity score
        complexity_score = 0.0
        if note_intervals:
            import statistics
            mean_interval = statistics.mean(note_intervals)
            if mean_interval > 0:
                std_interval = statistics.stdev(note_intervals) if len(note_intervals) > 1 else 0
                complexity_score = std_interval / mean_interval
        
        # Extract simple patterns (4-note sequences)
        patterns = []
        for i in range(len(note_types) - 3):
            pattern = note_types[i:i+4]
            patterns.append({
                "pattern": pattern,
                "start_measure": actual_notes[i].measure,
                "start_timing": actual_notes[i].timing_ms
            })
        
        return {
            "note_type_distribution": type_counts,
            "common_patterns": patterns[:50],  # Limit for performance (renamed for validator compatibility)
            "four_note_patterns": patterns[:50],  # Keep original name too
            "total_patterns": len(patterns),
            "complexity_score": round(complexity_score, 4),
            "interval_statistics": {
                "mean": round(statistics.mean(note_intervals), 3) if note_intervals else 0,
                "std": round(statistics.stdev(note_intervals), 3) if len(note_intervals) > 1 else 0,
                "count": len(note_intervals)
            }
        }
    
    def _normalize_note_type(self, note_type) -> str:
        """Normalize note types to consistent string format"""
        note_mapping = {
            0: "blank", 1: "don", 2: "ka", 3: "don_big", 4: "ka_big",
            5: "drumroll", 6: "drumroll_big", 7: "balloon", 8: "end_roll",
            9: "kusudama", "A": "don_both", "B": "ka_both", "F": "adlib"
        }
        return note_mapping.get(note_type, str(note_type))
    
    def validate_audio_pairing(self, tja_files: List[str]) -> Dict:
        """
        Validate audio file pairing for all TJA files using WAVE field
        
        Args:
            tja_files: List of TJA file paths
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            "total_files": len(tja_files),
            "wave_field_resolved": 0,
            "filename_fallback_used": 0,
            "missing_audio": 0,
            "wave_field_empty": 0,
            "pairing_errors": [],
            "success_rate": 0.0
        }
        
        for tja_path in tja_files:
            try:
                # Parse TJA to get WAVE field
                parser = CustomTJAParser(tja_path)
                wave_field = parser.get_reference_metadata().get('wave', '')
                
                # Resolve audio file using WAVE field
                pairing_result = resolve_audio_file_from_wave_field(tja_path, wave_field)
                
                # Update statistics
                if pairing_result["resolved_successfully"]:
                    if pairing_result.get("fallback_used", False):
                        validation_results["filename_fallback_used"] += 1
                    else:
                        validation_results["wave_field_resolved"] += 1
                else:
                    if pairing_result.get("method") == "no_audio_specified":
                        validation_results["wave_field_empty"] += 1
                    else:
                        validation_results["missing_audio"] += 1
                    
                    validation_results["pairing_errors"].append({
                        "tja_path": tja_path,
                        "wave_field": wave_field,
                        "error": pairing_result.get("error", "Unknown error")
                    })
                    
            except Exception as e:
                validation_results["pairing_errors"].append({
                    "tja_path": tja_path,
                    "wave_field": "parse_error",
                    "error": f"Failed to parse TJA: {str(e)}"
                })
                validation_results["missing_audio"] += 1
        
        # Calculate success rate
        successful_pairings = (validation_results["wave_field_resolved"] + 
                             validation_results["filename_fallback_used"])
        validation_results["success_rate"] = (successful_pairings / validation_results["total_files"] 
                                            if validation_results["total_files"] > 0 else 0.0)
        
        return validation_results
