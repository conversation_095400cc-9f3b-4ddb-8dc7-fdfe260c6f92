"""
Standardized Path Management System

Provides consistent path handling, resolution, and naming conventions
across all phases of the TJA Generator system.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum

# Removed circular import - will initialize config manager separately if needed


class PathType(Enum):
    """Enumeration of path types for consistent handling"""
    DATA_RAW = "data_raw"
    DATA_PROCESSED = "data_processed"
    AUDIO_FEATURES = "audio_features"
    MODELS = "models"
    OUTPUTS = "outputs"
    LOGS = "logs"
    CACHE = "cache"
    TEMP = "temp"
    CONFIG = "config"


@dataclass
class StandardizedPaths:
    """Standardized path structure for the entire system"""
    
    # Root directories
    workspace_root: Path = field(default_factory=lambda: Path.cwd())
    
    # Core directories
    data_root: Path = field(init=False)
    models_root: Path = field(init=False)
    outputs_root: Path = field(init=False)
    logs_root: Path = field(init=False)
    cache_root: Path = field(init=False)
    temp_root: Path = field(init=False)
    config_root: Path = field(init=False)
    
    # Data subdirectories
    data_raw: Path = field(init=False)
    data_processed: Path = field(init=False)
    audio_features: Path = field(init=False)
    training_data: Path = field(init=False)
    
    # Phase-specific output directories
    phase1_outputs: Path = field(init=False)
    phase2_outputs: Path = field(init=False)
    phase3_outputs: Path = field(init=False)
    phase4_outputs: Path = field(init=False)
    phase5_outputs: Path = field(init=False)
    phase6_outputs: Path = field(init=False)
    
    def __post_init__(self):
        """Initialize all paths relative to workspace root"""
        # Normalize workspace root to use forward slashes
        self.workspace_root = Path(str(self.workspace_root).replace('\\', '/'))
        
        # Core directories
        self.data_root = self.workspace_root / "data"
        self.models_root = self.workspace_root / "models"
        self.outputs_root = self.workspace_root / "outputs"
        self.logs_root = self.workspace_root / "logs"
        self.cache_root = self.workspace_root / "cache"
        self.temp_root = self.workspace_root / "temp"
        self.config_root = self.workspace_root / "config"
        
        # Data subdirectories
        self.data_raw = self.data_root / "raw"
        self.data_processed = self.data_root / "processed"
        self.audio_features = self.data_processed / "audio_features"
        self.training_data = self.data_processed / "training"
        
        # Phase-specific outputs
        self.phase1_outputs = self.outputs_root / "phase1"
        self.phase2_outputs = self.outputs_root / "phase2"
        self.phase3_outputs = self.outputs_root / "phase3"
        self.phase4_outputs = self.outputs_root / "phase4"
        self.phase5_outputs = self.outputs_root / "phase5"
        self.phase6_outputs = self.outputs_root / "phase6"
    
    def get_path(self, path_type: PathType) -> Path:
        """Get standardized path by type"""
        path_mapping = {
            PathType.DATA_RAW: self.data_raw,
            PathType.DATA_PROCESSED: self.data_processed,
            PathType.AUDIO_FEATURES: self.audio_features,
            PathType.MODELS: self.models_root,
            PathType.OUTPUTS: self.outputs_root,
            PathType.LOGS: self.logs_root,
            PathType.CACHE: self.cache_root,
            PathType.TEMP: self.temp_root,
            PathType.CONFIG: self.config_root
        }
        return path_mapping.get(path_type, self.workspace_root)
    
    def get_phase_output_path(self, phase_number: int) -> Path:
        """Get output path for specific phase"""
        phase_paths = {
            1: self.phase1_outputs,
            2: self.phase2_outputs,
            3: self.phase3_outputs,
            4: self.phase4_outputs,
            5: self.phase5_outputs,
            6: self.phase6_outputs
        }
        return phase_paths.get(phase_number, self.outputs_root)


class PathManager:
    """
    Centralized path management system
    
    Provides consistent path resolution, normalization, and validation
    across all phases of the TJA Generator system.
    """
    
    def __init__(self, workspace_root: Optional[Union[str, Path]] = None):
        self.logger = logging.getLogger(f"{__name__}.PathManager")
        
        # Initialize workspace root
        if workspace_root:
            self.workspace_root = Path(workspace_root).resolve()
        else:
            self.workspace_root = Path.cwd()
        
        # Initialize standardized paths
        self.paths = StandardizedPaths(workspace_root=self.workspace_root)
        
        # Note: Config manager removed to avoid circular imports
        
        self.logger.info(f"PathManager initialized with workspace: {self.workspace_root}")
    
    def resolve_path(self, path: Union[str, Path], 
                    relative_to: Optional[Union[str, Path, PathType]] = None,
                    ensure_forward_slashes: bool = True) -> Path:
        """
        Resolve path with consistent normalization
        
        Args:
            path: Path to resolve
            relative_to: Base path or PathType to resolve relative to
            ensure_forward_slashes: Whether to normalize to forward slashes
            
        Returns:
            Resolved and normalized Path object
        """
        # Convert to Path object
        path_obj = Path(path)
        
        # Determine base path
        if relative_to is None:
            base_path = self.workspace_root
        elif isinstance(relative_to, PathType):
            base_path = self.paths.get_path(relative_to)
        else:
            base_path = Path(relative_to)
        
        # Resolve path
        if path_obj.is_absolute():
            resolved = path_obj
        else:
            resolved = base_path / path_obj
        
        # Normalize path
        resolved = resolved.resolve()
        
        # Ensure forward slashes if requested
        if ensure_forward_slashes:
            resolved = Path(str(resolved).replace('\\', '/'))
        
        return resolved
    
    def get_standardized_path(self, path_type: PathType, 
                            subpath: Optional[str] = None) -> Path:
        """
        Get standardized path for specific type
        
        Args:
            path_type: Type of path to get
            subpath: Optional subpath to append
            
        Returns:
            Standardized path
        """
        base_path = self.paths.get_path(path_type)
        
        if subpath:
            return base_path / subpath
        
        return base_path
    
    def ensure_directory_exists(self, path: Union[str, Path, PathType],
                               subpath: Optional[str] = None) -> Path:
        """
        Ensure directory exists, create if necessary
        
        Args:
            path: Directory path or PathType
            subpath: Optional subpath to append
            
        Returns:
            Path to created directory
        """
        if isinstance(path, PathType):
            dir_path = self.get_standardized_path(path, subpath)
        else:
            dir_path = self.resolve_path(path)
            if subpath:
                dir_path = dir_path / subpath
        
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Ensured directory exists: {dir_path}")
            return dir_path
        except Exception as e:
            self.logger.error(f"Failed to create directory {dir_path}: {e}")
            raise
    
    def get_relative_path(self, path: Union[str, Path], 
                         relative_to: Optional[Union[str, Path, PathType]] = None) -> Path:
        """
        Get path relative to base path
        
        Args:
            path: Path to make relative
            relative_to: Base path or PathType
            
        Returns:
            Relative path
        """
        abs_path = self.resolve_path(path)
        
        if relative_to is None:
            base_path = self.workspace_root
        elif isinstance(relative_to, PathType):
            base_path = self.paths.get_path(relative_to)
        else:
            base_path = self.resolve_path(relative_to)
        
        try:
            return abs_path.relative_to(base_path)
        except ValueError:
            # Paths are not relative, return absolute path
            return abs_path
    
    def standardize_file_name(self, filename: str,
                            remove_spaces: bool = True,
                            remove_special_chars: bool = True,
                            max_length: int = 255) -> str:
        """
        Standardize filename according to conventions

        Args:
            filename: Original filename
            remove_spaces: Whether to replace spaces with underscores
            remove_special_chars: Whether to remove special characters
            max_length: Maximum filename length

        Returns:
            Standardized filename
        """
        # Get name and extension
        path_obj = Path(filename)
        name = path_obj.stem
        extension = path_obj.suffix

        # Remove or replace spaces
        if remove_spaces:
            name = name.replace(' ', '_')

        # Remove special characters if requested
        if remove_special_chars:
            import re
            # Keep only alphanumeric, underscore, hyphen, and dot
            name = re.sub(r'[^a-zA-Z0-9_\-.]', '', name)

        # Truncate if too long (accounting for extension)
        max_name_length = max_length - len(extension)
        if len(name) > max_name_length:
            name = name[:max_name_length]

        return name + extension

    def normalize_path_separators(self, path: Union[str, Path],
                                 force_forward: bool = True) -> str:
        """
        Normalize path separators consistently

        Args:
            path: Path to normalize
            force_forward: Whether to force forward slashes

        Returns:
            Normalized path string
        """
        path_str = str(path)

        if force_forward:
            # Convert all backslashes to forward slashes
            return path_str.replace('\\', '/')
        else:
            # Use OS-appropriate separators
            return str(Path(path_str))

    def get_relative_to_workspace(self, path: Union[str, Path]) -> str:
        """
        Get path relative to workspace root with forward slashes

        Args:
            path: Path to make relative

        Returns:
            Relative path string with forward slashes
        """
        abs_path = self.resolve_path(path)
        try:
            rel_path = abs_path.relative_to(self.workspace_root)
            return self.normalize_path_separators(rel_path, force_forward=True)
        except ValueError:
            # Path is not under workspace, return absolute path
            return self.normalize_path_separators(abs_path, force_forward=True)
        
        # Remove special characters
        if remove_special_chars:
            # Keep only alphanumeric, underscore, hyphen, and dot
            allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-.')
            name = ''.join(c for c in name if c in allowed_chars)
        
        # Ensure reasonable length
        if len(name) > max_length - len(extension):
            name = name[:max_length - len(extension)]
        
        return name + extension
    
    def get_file_pattern_paths(self, pattern: str, 
                              base_path: Optional[Union[str, Path, PathType]] = None,
                              recursive: bool = False) -> List[Path]:
        """
        Get list of files matching pattern
        
        Args:
            pattern: File pattern (e.g., "*.tja", "**/*.mp3")
            base_path: Base directory to search in
            recursive: Whether to search recursively
            
        Returns:
            List of matching file paths
        """
        if base_path is None:
            search_path = self.workspace_root
        elif isinstance(base_path, PathType):
            search_path = self.paths.get_path(base_path)
        else:
            search_path = self.resolve_path(base_path)
        
        if not search_path.exists():
            self.logger.warning(f"Search path does not exist: {search_path}")
            return []
        
        try:
            if recursive or '**' in pattern:
                files = list(search_path.rglob(pattern))
            else:
                files = list(search_path.glob(pattern))
            
            # Filter to only files and normalize paths
            result = []
            for file_path in files:
                if file_path.is_file():
                    normalized = self.resolve_path(file_path)
                    result.append(normalized)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error searching for pattern {pattern} in {search_path}: {e}")
            return []
    
    def create_backup_path(self, original_path: Union[str, Path], 
                          backup_suffix: str = "_backup") -> Path:
        """
        Create backup path for file
        
        Args:
            original_path: Original file path
            backup_suffix: Suffix to add to backup file
            
        Returns:
            Backup file path
        """
        path_obj = self.resolve_path(original_path)
        
        # Create backup filename
        backup_name = f"{path_obj.stem}{backup_suffix}{path_obj.suffix}"
        backup_path = path_obj.parent / backup_name
        
        return backup_path
    
    def get_temp_file_path(self, prefix: str = "temp", 
                          suffix: str = "", 
                          extension: str = ".tmp") -> Path:
        """
        Get temporary file path
        
        Args:
            prefix: Filename prefix
            suffix: Filename suffix
            extension: File extension
            
        Returns:
            Temporary file path
        """
        import time
        import random
        
        # Ensure temp directory exists
        temp_dir = self.ensure_directory_exists(PathType.TEMP)
        
        # Create unique filename
        timestamp = int(time.time())
        random_id = random.randint(1000, 9999)
        filename = f"{prefix}_{timestamp}_{random_id}{suffix}{extension}"
        
        return temp_dir / filename
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up old temporary files
        
        Args:
            max_age_hours: Maximum age of files to keep
            
        Returns:
            Number of files cleaned up
        """
        temp_dir = self.paths.get_path(PathType.TEMP)
        
        if not temp_dir.exists():
            return 0
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        try:
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
                        self.logger.debug(f"Cleaned up temp file: {file_path}")
            
            self.logger.info(f"Cleaned up {cleaned_count} temporary files")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up temp files: {e}")
            return 0
    
    def get_path_info(self) -> Dict[str, Any]:
        """Get information about all standardized paths"""
        return {
            "workspace_root": self.normalize_path_separators(self.workspace_root),
            "paths": {
                "data_raw": self.normalize_path_separators(self.paths.data_raw),
                "data_processed": self.normalize_path_separators(self.paths.data_processed),
                "audio_features": self.normalize_path_separators(self.paths.audio_features),
                "models": self.normalize_path_separators(self.paths.models_root),
                "outputs": self.normalize_path_separators(self.paths.outputs_root),
                "logs": self.normalize_path_separators(self.paths.logs_root),
                "cache": self.normalize_path_separators(self.paths.cache_root),
                "temp": self.normalize_path_separators(self.paths.temp_root),
                "config": self.normalize_path_separators(self.paths.config_root)
            },
            "phase_outputs": {
                f"phase{i}": self.normalize_path_separators(self.paths.get_phase_output_path(i))
                for i in range(1, 7)
            }
        }

    def standardize_all_paths_in_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively standardize all path strings in a configuration dictionary

        Args:
            config: Configuration dictionary that may contain paths

        Returns:
            Configuration with standardized paths
        """
        def standardize_value(value):
            if isinstance(value, str):
                # Check if this looks like a path
                if ('/' in value or '\\' in value or
                    value.endswith(('.json', '.txt', '.log', '.tja', '.pt', '.pkl'))):
                    return self.normalize_path_separators(value)
                return value
            elif isinstance(value, dict):
                return {k: standardize_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [standardize_value(item) for item in value]
            else:
                return value

        return standardize_value(config)

    def migrate_legacy_paths(self, legacy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate legacy path configurations to standardized format

        Args:
            legacy_config: Legacy configuration with old path formats

        Returns:
            Migrated configuration with standardized paths
        """
        # Common legacy path mappings
        path_migrations = {
            'data_dir': 'data_raw',
            'output_dir': 'outputs',
            'model_dir': 'models',
            'log_dir': 'logs',
            'cache_dir': 'cache',
            'temp_dir': 'temp'
        }

        migrated = legacy_config.copy()

        for old_key, new_key in path_migrations.items():
            if old_key in migrated:
                # Convert to standardized path
                old_path = migrated.pop(old_key)
                standardized_path = self.normalize_path_separators(old_path)
                migrated[new_key] = standardized_path
                self.logger.info(f"Migrated path: {old_key} -> {new_key}: {standardized_path}")

        return self.standardize_all_paths_in_config(migrated)
