"""
Unified Audio Processing System

Consolidates all audio processing functionality from multiple phases into a single,
consistent system implementing SOLID principles and eliminating redundancy.
"""

import torch
import torchaudio
import librosa
import numpy as np
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod

from ..utils.base_classes import BaseProcessor, ProcessingResult
from ..config.unified_config_manager import UnifiedConfigManager


@dataclass
class AudioProcessingConfig:
    """Unified configuration for all audio processing operations"""
    # Core audio settings
    sample_rate: int = 44100
    frame_rate: float = 50.0
    hop_length: int = 882  # 44100/50
    n_fft: int = 2048
    
    # Feature dimensions
    target_feature_dims: int = 201
    spectral_dims: int = 153  # 128 mel + 13 mfcc + 12 chroma
    rhythmic_dims: int = 32
    temporal_dims: int = 16
    
    # Audio preprocessing
    normalize_audio: bool = True
    trim_silence: bool = True
    apply_preemphasis: bool = True
    
    # Spectral settings
    n_mels: int = 128
    f_min: float = 80.0
    f_max: float = 8000.0
    n_mfcc: int = 13
    n_chroma: int = 12
    
    # Rhythmic settings
    onset_method: str = "superflux"
    tempo_min: float = 60.0
    tempo_max: float = 300.0
    
    # Quality thresholds
    min_duration_seconds: float = 30.0
    max_duration_seconds: float = 600.0


class AudioFeatureExtractorInterface(ABC):
    """Interface for audio feature extractors following Interface Segregation Principle"""
    
    @abstractmethod
    def extract_features(self, audio: Union[np.ndarray, torch.Tensor], 
                        sample_rate: int) -> torch.Tensor:
        """Extract features from audio signal"""
        pass
    
    @abstractmethod
    def get_feature_dimensions(self) -> int:
        """Get the number of feature dimensions"""
        pass


class SpectralFeatureExtractor(AudioFeatureExtractorInterface):
    """Consolidated spectral feature extraction"""
    
    def __init__(self, config: AudioProcessingConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.SpectralFeatureExtractor")
        
        # Initialize transforms
        self.mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=config.sample_rate,
            n_fft=config.n_fft,
            hop_length=config.hop_length,
            n_mels=config.n_mels,
            f_min=config.f_min,
            f_max=config.f_max
        )
        
        self.mfcc_transform = torchaudio.transforms.MFCC(
            sample_rate=config.sample_rate,
            n_mfcc=config.n_mfcc,
            melkwargs={
                "n_fft": config.n_fft,
                "hop_length": config.hop_length,
                "n_mels": config.n_mels,
                "f_min": config.f_min,
                "f_max": config.f_max
            }
        )
    
    def extract_features(self, audio: Union[np.ndarray, torch.Tensor], 
                        sample_rate: int) -> torch.Tensor:
        """Extract spectral features (mel, mfcc, chroma)"""
        if isinstance(audio, np.ndarray):
            audio = torch.from_numpy(audio).float()
        
        if audio.dim() == 1:
            audio = audio.unsqueeze(0)
        
        # Extract mel spectrogram
        mel_spec = self.mel_transform(audio)
        mel_spec = torch.log(mel_spec + 1e-8)  # Log scale
        
        # Extract MFCC
        mfcc = self.mfcc_transform(audio)
        
        # Extract chroma (using librosa for now)
        audio_np = audio.squeeze().numpy()
        chroma = librosa.feature.chroma_stft(
            y=audio_np, 
            sr=sample_rate,
            hop_length=self.config.hop_length,
            n_fft=self.config.n_fft
        )
        chroma = torch.from_numpy(chroma).float()
        
        # Combine features [time, features]
        mel_spec = mel_spec.squeeze(0).transpose(0, 1)  # [time, n_mels]
        mfcc = mfcc.squeeze(0).transpose(0, 1)  # [time, n_mfcc]
        chroma = chroma.transpose(0, 1)  # [time, n_chroma]
        
        # Ensure same time dimension
        min_time = min(mel_spec.shape[0], mfcc.shape[0], chroma.shape[0])
        mel_spec = mel_spec[:min_time]
        mfcc = mfcc[:min_time]
        chroma = chroma[:min_time]
        
        # Concatenate features
        spectral_features = torch.cat([mel_spec, mfcc, chroma], dim=1)
        
        return spectral_features
    
    def get_feature_dimensions(self) -> int:
        return self.config.n_mels + self.config.n_mfcc + self.config.n_chroma


class RhythmicFeatureExtractor(AudioFeatureExtractorInterface):
    """Consolidated rhythmic feature extraction"""
    
    def __init__(self, config: AudioProcessingConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.RhythmicFeatureExtractor")
    
    def extract_features(self, audio: Union[np.ndarray, torch.Tensor], 
                        sample_rate: int) -> torch.Tensor:
        """Extract rhythmic features (onset, tempo, beat)"""
        if isinstance(audio, torch.Tensor):
            audio = audio.numpy()
        
        if audio.ndim > 1:
            audio = audio.squeeze()
        
        # Extract onset strength
        onset_strength = librosa.onset.onset_strength(
            y=audio,
            sr=sample_rate,
            hop_length=self.config.hop_length
        )
        
        # Extract tempo and beats
        tempo, beats = librosa.beat.beat_track(
            onset_envelope=onset_strength,
            sr=sample_rate,
            hop_length=self.config.hop_length,
            units='frames'
        )
        
        # Extract spectral flux
        stft = librosa.stft(audio, hop_length=self.config.hop_length, n_fft=self.config.n_fft)
        spectral_flux = np.sum(np.diff(np.abs(stft), axis=1) > 0, axis=0)
        
        # Create fixed-size feature vector
        n_frames = len(onset_strength)
        rhythmic_features = np.zeros((n_frames, self.config.rhythmic_dims))
        
        # Fill features
        rhythmic_features[:, 0] = onset_strength
        
        # Tempo features (broadcast to all frames)
        rhythmic_features[:, 1] = tempo / 200.0  # Normalize tempo
        
        # Beat features
        beat_mask = np.zeros(n_frames)
        if len(beats) > 0:
            beat_mask[beats[beats < n_frames]] = 1.0
        rhythmic_features[:, 2] = beat_mask
        
        # Spectral flux
        if len(spectral_flux) >= n_frames:
            rhythmic_features[:, 3] = spectral_flux[:n_frames] / np.max(spectral_flux + 1e-8)
        else:
            rhythmic_features[:len(spectral_flux), 3] = spectral_flux / np.max(spectral_flux + 1e-8)
        
        # Fill remaining dimensions with derived features
        for i in range(4, self.config.rhythmic_dims):
            if i < len(onset_strength):
                rhythmic_features[:, i] = np.roll(onset_strength, i-4) * (0.9 ** (i-4))
        
        return torch.from_numpy(rhythmic_features).float()
    
    def get_feature_dimensions(self) -> int:
        return self.config.rhythmic_dims


class TemporalFeatureExtractor(AudioFeatureExtractorInterface):
    """Consolidated temporal feature extraction"""
    
    def __init__(self, config: AudioProcessingConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.TemporalFeatureExtractor")
    
    def extract_features(self, audio: Union[np.ndarray, torch.Tensor], 
                        sample_rate: int) -> torch.Tensor:
        """Extract temporal features (energy, zero-crossing, etc.)"""
        if isinstance(audio, torch.Tensor):
            audio = audio.numpy()
        
        if audio.ndim > 1:
            audio = audio.squeeze()
        
        # Frame the audio
        frame_length = self.config.hop_length
        n_frames = len(audio) // frame_length
        
        temporal_features = np.zeros((n_frames, self.config.temporal_dims))
        
        for i in range(n_frames):
            start = i * frame_length
            end = start + frame_length
            frame = audio[start:end]
            
            if len(frame) > 0:
                # Energy
                temporal_features[i, 0] = np.sum(frame ** 2)
                
                # Zero crossing rate
                temporal_features[i, 1] = np.sum(np.diff(np.sign(frame)) != 0) / len(frame)
                
                # RMS energy
                temporal_features[i, 2] = np.sqrt(np.mean(frame ** 2))
                
                # Spectral centroid (simplified)
                fft = np.fft.fft(frame)
                freqs = np.fft.fftfreq(len(frame), 1/sample_rate)
                magnitude = np.abs(fft)
                if np.sum(magnitude) > 0:
                    temporal_features[i, 3] = np.sum(freqs[:len(freqs)//2] * magnitude[:len(freqs)//2]) / np.sum(magnitude[:len(freqs)//2])
                
                # Fill remaining dimensions with derived features
                for j in range(4, self.config.temporal_dims):
                    if j < len(frame):
                        temporal_features[i, j] = frame[j] * (0.95 ** j)
        
        return torch.from_numpy(temporal_features).float()
    
    def get_feature_dimensions(self) -> int:
        return self.config.temporal_dims


class UnifiedAudioProcessor(BaseProcessor):
    """
    Unified audio processing system consolidating all audio functionality
    
    Replaces redundant audio processing modules across phases with a single,
    consistent implementation following SOLID principles.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "UnifiedAudioProcessor")
        
        # Initialize configuration
        self.config_manager = UnifiedConfigManager()
        self.audio_config = AudioProcessingConfig()
        
        # Override with provided config
        if config:
            for key, value in config.items():
                if hasattr(self.audio_config, key):
                    setattr(self.audio_config, key, value)
        
        # Initialize feature extractors
        self.spectral_extractor = SpectralFeatureExtractor(self.audio_config)
        self.rhythmic_extractor = RhythmicFeatureExtractor(self.audio_config)
        self.temporal_extractor = TemporalFeatureExtractor(self.audio_config)
        
        self.logger.info("UnifiedAudioProcessor initialized with consolidated feature extractors")
        self.logger.info(f"Target feature dimensions: {self.audio_config.target_feature_dims}")
    
    def process(self, input_data: Union[str, Dict[str, Any]]) -> ProcessingResult:
        """
        Process audio according to BaseProcessor interface
        
        Args:
            input_data: Audio file path or processing parameters
            
        Returns:
            ProcessingResult with extracted features
        """
        with self._processing_context("unified_audio_processing"):
            try:
                if isinstance(input_data, str):
                    # Single audio file processing
                    features = self.extract_features_from_file(input_data)
                else:
                    # Batch processing or custom parameters
                    features = self._process_audio_batch(input_data)
                
                return ProcessingResult(
                    success=True,
                    data=features,
                    processing_time_seconds=time.time() - self.start_time,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
                
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )

    def extract_features_from_file(self, audio_file_path: str) -> Dict[str, Any]:
        """
        Extract unified features from audio file

        Args:
            audio_file_path: Path to audio file

        Returns:
            Dictionary containing extracted features
        """
        audio_path = self.config_manager.resolve_path(audio_file_path)

        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        # Load audio
        audio, sample_rate = self._load_audio(str(audio_path))

        # Preprocess audio
        audio = self._preprocess_audio(audio)

        # Extract features from all extractors
        spectral_features = self.spectral_extractor.extract_features(audio, sample_rate)
        rhythmic_features = self.rhythmic_extractor.extract_features(audio, sample_rate)
        temporal_features = self.temporal_extractor.extract_features(audio, sample_rate)

        # Combine features to target dimensions
        combined_features = self._combine_features(
            spectral_features, rhythmic_features, temporal_features
        )

        return {
            "combined_features": combined_features,  # [T, 201] - main output
            "spectral_features": spectral_features,
            "rhythmic_features": rhythmic_features,
            "temporal_features": temporal_features,
            "audio_metadata": {
                "file_path": str(audio_path),
                "sample_rate": sample_rate,
                "duration_seconds": len(audio) / sample_rate,
                "n_frames": combined_features.shape[0]
            }
        }

    def _load_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """Load audio file with consistent format"""
        try:
            # Try torchaudio first
            waveform, sample_rate = torchaudio.load(audio_path)
            audio = waveform.numpy().squeeze()

            # Resample if necessary
            if sample_rate != self.audio_config.sample_rate:
                audio = librosa.resample(
                    audio,
                    orig_sr=sample_rate,
                    target_sr=self.audio_config.sample_rate
                )
                sample_rate = self.audio_config.sample_rate

            return audio, sample_rate

        except Exception:
            # Fallback to librosa
            audio, sample_rate = librosa.load(
                audio_path,
                sr=self.audio_config.sample_rate
            )
            return audio, sample_rate

    def _preprocess_audio(self, audio: np.ndarray) -> np.ndarray:
        """Apply consistent audio preprocessing"""
        # Normalize audio
        if self.audio_config.normalize_audio:
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                audio = audio / max_val

        # Trim silence
        if self.audio_config.trim_silence:
            audio, _ = librosa.effects.trim(audio, top_db=20)

        # Apply preemphasis
        if self.audio_config.apply_preemphasis:
            audio = np.append(audio[0], audio[1:] - 0.97 * audio[:-1])

        return audio

    def _combine_features(self, spectral: torch.Tensor,
                         rhythmic: torch.Tensor,
                         temporal: torch.Tensor) -> torch.Tensor:
        """Combine features to target dimensions [T, 201]"""
        # Ensure same time dimension
        min_time = min(spectral.shape[0], rhythmic.shape[0], temporal.shape[0])

        spectral = spectral[:min_time]
        rhythmic = rhythmic[:min_time]
        temporal = temporal[:min_time]

        # Concatenate features
        combined = torch.cat([spectral, rhythmic, temporal], dim=1)

        # Ensure exact target dimensions
        if combined.shape[1] != self.audio_config.target_feature_dims:
            if combined.shape[1] > self.audio_config.target_feature_dims:
                # Truncate
                combined = combined[:, :self.audio_config.target_feature_dims]
            else:
                # Pad with zeros
                padding = torch.zeros(
                    combined.shape[0],
                    self.audio_config.target_feature_dims - combined.shape[1]
                )
                combined = torch.cat([combined, padding], dim=1)

        return combined

    def _process_audio_batch(self, batch_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process batch of audio files"""
        audio_files = batch_data.get("audio_files", [])
        results = {}

        for audio_file in audio_files:
            try:
                features = self.extract_features_from_file(audio_file)
                results[audio_file] = features
            except Exception as e:
                self.logger.error(f"Failed to process {audio_file}: {e}")
                results[audio_file] = {"error": str(e)}

        return {
            "batch_results": results,
            "processed_count": len([r for r in results.values() if "error" not in r]),
            "failed_count": len([r for r in results.values() if "error" in r])
        }

    def get_supported_formats(self) -> List[str]:
        """Get list of supported audio formats"""
        return [".mp3", ".wav", ".ogg", ".flac", ".m4a", ".aac"]

    def validate_audio_file(self, audio_path: str) -> bool:
        """Validate audio file format and duration"""
        try:
            audio_file = Path(audio_path)

            # Check file exists
            if not audio_file.exists():
                return False

            # Check format
            if audio_file.suffix.lower() not in self.get_supported_formats():
                return False

            # Check duration
            audio, sr = self._load_audio(str(audio_file))
            duration = len(audio) / sr

            return (self.audio_config.min_duration_seconds <= duration <=
                   self.audio_config.max_duration_seconds)

        except Exception:
            return False
