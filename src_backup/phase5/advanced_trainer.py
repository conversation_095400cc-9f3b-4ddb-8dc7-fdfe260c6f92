"""
Advanced TJA Trainer - Phase 5

Enhanced training pipeline with curriculum learning, adaptive optimization,
and production-ready training strategies optimized for RTX 3070 hardware.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import time
import logging
from pathlib import Path
import numpy as np

# Optional imports
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    # Fallback tqdm implementation
    def tqdm(iterable, desc="", **kwargs):
        return iterable

try:
    import wandb
    HAS_WANDB = True
except ImportError:
    HAS_WANDB = False
    # Mock wandb for when it's not available
    class MockWandb:
        run = None
        def init(self, **kwargs): pass
        def log(self, data): pass
        def finish(self): pass
    wandb = MockWandb()

from .training_config import Phase5TrainingConfig, OptimizationConfig
from .training_strategies import CurriculumLearningStrategy, AdaptiveLossWeighting
from .data_augmentation import AdvancedTJAAugmentation
from .training_diagnostics import TrainingDiagnostics, PerformanceProfiler
from .checkpoint_manager import AdvancedCheckpointManager

from ..model.tja_generator import TJAGeneratorModel
from ..training.optimizer import create_optimizer, create_scheduler
from ..training.metrics import TJAMetrics, GenerationEvaluator
from ..phase4.quality_assessment import QualityAssessment


class AdvancedTJATrainer:
    """
    Advanced TJA trainer with Phase 5 optimization features
    
    Features:
    - Curriculum learning with progressive difficulty
    - Adaptive loss weighting based on training dynamics
    - Advanced data augmentation strategies
    - Real-time performance profiling and diagnostics
    - Integration with Phase 4 quality assessment
    - Production-ready checkpointing and model management
    """
    
    def __init__(self, 
                 model: TJAGeneratorModel,
                 config: Phase5TrainingConfig,
                 optimization_config: OptimizationConfig,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 output_dir: str = "outputs/phase5_training"):
        """
        Initialize advanced trainer
        
        Args:
            model: TJA generation model
            config: Phase 5 training configuration
            optimization_config: Advanced optimization configuration
            train_loader: Training data loader
            val_loader: Validation data loader
            output_dir: Output directory for checkpoints and logs
        """
        self.model = model
        self.config = config
        self.optimization_config = optimization_config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Device setup
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # Model compilation for PyTorch 2.0
        if config.compile_model and hasattr(torch, 'compile'):
            self.logger.info("Compiling model with PyTorch 2.0...")
            self.model = torch.compile(self.model, mode='reduce-overhead')
        
        # Training components
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        self.scaler = torch.cuda.amp.GradScaler() if config.mixed_precision else None
        
        # Advanced training strategies
        self.curriculum_strategy = CurriculumLearningStrategy(optimization_config)
        self.adaptive_loss = AdaptiveLossWeighting(config.loss_weights, optimization_config)
        self.augmentation = AdvancedTJAAugmentation(config) if config.use_data_augmentation else None
        
        # Diagnostics and profiling
        self.diagnostics = TrainingDiagnostics(config)
        self.profiler = PerformanceProfiler(config) if optimization_config.enable_profiling else None
        
        # Quality assessment integration
        from ..phase4.config import PHASE_4_CONFIG
        self.quality_assessor = QualityAssessment(PHASE_4_CONFIG["quality_assessment"])
        
        # Checkpoint management
        self.checkpoint_manager = AdvancedCheckpointManager(
            self.output_dir / "checkpoints",
            save_top_k=config.save_top_k_checkpoints
        )
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor()
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.best_quality_score = 0.0
        self.early_stopping_counter = 0
        
        # Metrics tracking
        self.metrics = TJAMetrics()
        self.evaluator = GenerationEvaluator(config.get_training_config()["evaluation"])
        
        # Initialize wandb if configured
        self._init_wandb()
        
        self.logger.info("Advanced TJA Trainer initialized successfully")
        self.logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        self.logger.info(f"Effective batch size: {config.get_effective_batch_size()}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging"""
        logger = logging.getLogger(f"phase5_trainer_{self.config.experiment_name}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_file = self.output_dir / f"training_{self.config.experiment_name}.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """Create optimized optimizer"""
        if self.config.use_fused_optimizer and torch.cuda.is_available():
            # Use fused AdamW for better performance
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay,
                fused=True
            )
        else:
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        
        return optimizer
    
    def _create_scheduler(self) -> torch.optim.lr_scheduler._LRScheduler:
        """Create learning rate scheduler"""
        if self.config.lr_scheduler == "cosine_with_warmup":
            from transformers import get_cosine_schedule_with_warmup
            scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=self.config.warmup_steps,
                num_training_steps=self.config.total_training_steps,
                num_cycles=0.5,
                last_epoch=-1
            )
        else:
            # Fallback to standard cosine annealing
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.total_training_steps,
                eta_min=self.config.learning_rate * self.config.min_lr_ratio
            )
        
        return scheduler
    
    def _init_wandb(self):
        """Initialize Weights & Biases logging"""
        if self.config.wandb_project:
            try:
                wandb.init(
                    project=self.config.wandb_project,
                    name=self.config.experiment_name,
                    tags=self.config.wandb_tags,
                    config={
                        "model_config": self.config.get_model_config(),
                        "training_config": self.config.get_training_config(),
                        "optimization_config": self.optimization_config.__dict__
                    }
                )
                self.logger.info("Wandb initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize wandb: {e}")
    
    def train(self) -> Dict[str, Any]:
        """
        Execute complete training pipeline with Phase 5 optimizations
        
        Returns:
            Dictionary with training results and metrics
        """
        self.logger.info("Starting Phase 5 advanced training...")
        
        # Log initial memory status
        self.memory_monitor.log_memory_status("Training start")
        
        training_start_time = time.time()
        
        try:
            # Training loop
            while self.global_step < self.config.total_training_steps:
                epoch_start_time = time.time()
                
                # Train one epoch
                train_metrics = self._train_epoch()
                
                # Validation
                if self.global_step % self.config.validation_frequency == 0:
                    val_metrics = self._validate()
                    
                    # Update best metrics
                    if val_metrics["loss"] < self.best_val_loss:
                        self.best_val_loss = val_metrics["loss"]
                        self.early_stopping_counter = 0
                        
                        # Save best model
                        self.checkpoint_manager.save_checkpoint(
                            model=self.model,
                            optimizer=self.optimizer,
                            scheduler=self.scheduler,
                            step=self.global_step,
                            metrics=val_metrics,
                            is_best=True
                        )
                    else:
                        self.early_stopping_counter += 1
                    
                    # Check early stopping
                    if self.early_stopping_counter >= self.config.early_stopping_patience:
                        self.logger.info(f"Early stopping triggered after {self.early_stopping_counter} validation cycles")
                        break
                
                # Regular checkpointing
                if self.global_step % self.config.checkpoint_frequency == 0:
                    self.checkpoint_manager.save_checkpoint(
                        model=self.model,
                        optimizer=self.optimizer,
                        scheduler=self.scheduler,
                        step=self.global_step,
                        metrics=train_metrics
                    )
                
                # Log epoch metrics
                epoch_time = time.time() - epoch_start_time
                self.logger.info(f"Epoch {self.epoch} completed in {epoch_time:.2f}s")
                
                self.epoch += 1
                
                # Check if training is complete
                if self.global_step >= self.config.total_training_steps:
                    break
            
            # Final evaluation
            final_metrics = self._final_evaluation()
            
            training_time = time.time() - training_start_time
            
            # Training summary
            training_results = {
                "success": True,
                "total_training_time": training_time,
                "total_steps": self.global_step,
                "total_epochs": self.epoch,
                "best_val_loss": self.best_val_loss,
                "best_quality_score": self.best_quality_score,
                "final_metrics": final_metrics,
                "checkpoint_path": str(self.checkpoint_manager.best_checkpoint_path)
            }
            
            self.logger.info("Phase 5 training completed successfully!")
            self.logger.info(f"Training time: {training_time:.2f} seconds")
            self.logger.info(f"Best validation loss: {self.best_val_loss:.4f}")
            self.logger.info(f"Best quality score: {self.best_quality_score:.4f}")
            
            return training_results
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            return {
                "success": False,
                "error": str(e),
                "global_step": self.global_step,
                "epoch": self.epoch
            }
        
        finally:
            # Cleanup
            if wandb.run:
                wandb.finish()
            
            # Final memory status
            self.memory_monitor.log_memory_status("Training end")
    
    def _train_epoch(self) -> Dict[str, float]:
        """Train one epoch with advanced strategies"""
        self.model.train()
        epoch_metrics = {"loss": 0.0, "steps": 0}
        
        # Get current curriculum stage
        curriculum_stage = self.curriculum_strategy.get_current_stage(self.global_step)
        
        # Update data loader if curriculum changed
        if self.curriculum_strategy.should_update_curriculum(self.global_step):
            self.logger.info(f"Updating curriculum to stage: {curriculum_stage['name']}")
            # Note: In production, you'd update the data loader here
        
        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.epoch}")
        
        for batch_idx, batch in enumerate(progress_bar):
            # Move batch to device
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # Apply data augmentation
            if self.augmentation:
                batch = self.augmentation.apply_augmentation(batch)
            
            # Forward pass with mixed precision
            with torch.cuda.amp.autocast(enabled=self.config.mixed_precision):
                outputs = self.model(
                    audio_features=batch["audio_features"],
                    target_sequence=batch["note_sequence"],
                    target_timing=batch["timing_sequence"],
                    difficulty=batch["difficulty"],
                    audio_mask=batch.get("audio_mask"),
                    return_loss=True
                )
                
                # Get adaptive loss weights
                loss_weights = self.adaptive_loss.get_current_weights(self.global_step)
                
                # Compute weighted loss
                total_loss = 0.0
                for loss_name, loss_value in outputs["losses"].items():
                    weight = loss_weights.get(loss_name, 1.0)
                    total_loss += weight * loss_value
                
                # Scale loss for gradient accumulation
                total_loss = total_loss / self.config.gradient_accumulation_steps
            
            # Backward pass
            if self.scaler:
                self.scaler.scale(total_loss).backward()
            else:
                total_loss.backward()
            
            # Gradient accumulation
            if (batch_idx + 1) % self.config.gradient_accumulation_steps == 0:
                # Gradient clipping
                if self.scaler:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
                    self.optimizer.step()
                
                self.scheduler.step()
                self.optimizer.zero_grad()
                
                self.global_step += 1
                
                # Update adaptive loss weights
                self.adaptive_loss.update_weights(outputs["losses"], self.global_step)
                
                # Logging
                if self.global_step % self.config.log_frequency == 0:
                    self._log_training_step(total_loss.item() * self.config.gradient_accumulation_steps, 
                                          outputs, loss_weights)
                
                # Performance profiling
                if self.profiler and self.global_step % self.optimization_config.profiling_frequency == 0:
                    self.profiler.profile_step(self.model, batch)
            
            # Update metrics
            epoch_metrics["loss"] += total_loss.item() * self.config.gradient_accumulation_steps
            epoch_metrics["steps"] += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                "loss": f"{total_loss.item():.4f}",
                "lr": f"{self.scheduler.get_last_lr()[0]:.2e}",
                "step": self.global_step
            })
            
            # Check if epoch should end early
            if self.global_step >= self.config.total_training_steps:
                break
        
        # Average epoch metrics
        if epoch_metrics["steps"] > 0:
            epoch_metrics["loss"] /= epoch_metrics["steps"]

        return epoch_metrics

    def _validate(self) -> Dict[str, float]:
        """Run validation with Phase 4 quality assessment"""
        self.model.eval()
        val_metrics = {"loss": 0.0, "quality_score": 0.0, "steps": 0}

        all_generated_sequences = []
        all_target_sequences = []

        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validation"):
                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                # Forward pass
                with torch.cuda.amp.autocast(enabled=self.config.mixed_precision):
                    outputs = self.model(
                        audio_features=batch["audio_features"],
                        target_sequence=batch["note_sequence"],
                        target_timing=batch["timing_sequence"],
                        difficulty=batch["difficulty"],
                        audio_mask=batch.get("audio_mask"),
                        return_loss=True
                    )

                    # Compute total validation loss
                    total_loss = sum(outputs["losses"].values())
                    val_metrics["loss"] += total_loss.item()

                # Generate sequences for quality assessment
                generated_outputs = self.model(
                    audio_features=batch["audio_features"],
                    difficulty=batch["difficulty"],
                    audio_mask=batch.get("audio_mask"),
                    return_loss=False
                )

                # Extract generated sequences
                generated_sequences = torch.argmax(generated_outputs["logits"], dim=-1)

                # Store for quality assessment
                all_generated_sequences.extend(generated_sequences.cpu().numpy())
                all_target_sequences.extend(batch["note_sequence"].cpu().numpy())

                val_metrics["steps"] += 1

        # Average validation loss
        if val_metrics["steps"] > 0:
            val_metrics["loss"] /= val_metrics["steps"]

        # Quality assessment using Phase 4 system
        quality_scores = []
        for gen_seq, target_seq in zip(all_generated_sequences[:10], all_target_sequences[:10]):  # Sample for efficiency
            try:
                quality_metrics = self.quality_assessor.evaluate_sequence(
                    gen_seq, difficulty_level=9  # Default to Oni 9
                )
                quality_scores.append(quality_metrics.overall_score)
            except Exception as e:
                self.logger.warning(f"Quality assessment failed: {e}")
                quality_scores.append(0.0)

        val_metrics["quality_score"] = np.mean(quality_scores) if quality_scores else 0.0

        # Update best quality score
        if val_metrics["quality_score"] > self.best_quality_score:
            self.best_quality_score = val_metrics["quality_score"]

        # Log validation metrics
        self.logger.info(f"Validation - Loss: {val_metrics['loss']:.4f}, Quality: {val_metrics['quality_score']:.4f}")

        # Wandb logging
        if wandb.run:
            wandb.log({
                "val/loss": val_metrics["loss"],
                "val/quality_score": val_metrics["quality_score"],
                "val/best_quality_score": self.best_quality_score,
                "step": self.global_step
            })

        return val_metrics

    def _final_evaluation(self) -> Dict[str, Any]:
        """Comprehensive final evaluation"""
        self.logger.info("Running final evaluation...")

        # Load best checkpoint
        best_checkpoint = self.checkpoint_manager.load_best_checkpoint()
        if best_checkpoint:
            self.model.load_state_dict(best_checkpoint["model_state_dict"])

        # Run validation
        final_val_metrics = self._validate()

        # Generate sample outputs for quality assessment
        sample_outputs = self._generate_sample_outputs()

        # Performance diagnostics
        diagnostics = self.diagnostics.get_final_diagnostics()

        final_metrics = {
            "validation_metrics": final_val_metrics,
            "sample_outputs": sample_outputs,
            "training_diagnostics": diagnostics,
            "model_size_mb": sum(p.numel() * 4 for p in self.model.parameters()) / (1024 * 1024),
            "total_parameters": sum(p.numel() for p in self.model.parameters())
        }

        return final_metrics

    def _generate_sample_outputs(self) -> List[Dict[str, Any]]:
        """Generate sample outputs for evaluation"""
        self.model.eval()
        sample_outputs = []

        with torch.no_grad():
            # Get a few validation samples
            val_iter = iter(self.val_loader)
            for i in range(min(3, len(self.val_loader))):
                try:
                    batch = next(val_iter)
                    batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                            for k, v in batch.items()}

                    # Generate output
                    outputs = self.model(
                        audio_features=batch["audio_features"][:1],  # Single sample
                        difficulty=batch["difficulty"][:1],
                        return_loss=False
                    )

                    generated_sequence = torch.argmax(outputs["logits"], dim=-1)[0]
                    target_sequence = batch["note_sequence"][0]

                    # Quality assessment
                    quality_metrics = self.quality_assessor.evaluate_sequence(
                        generated_sequence.cpu().numpy(),
                        difficulty_level=batch["difficulty"][0].item() + 8  # Convert to 8-10 range
                    )

                    sample_outputs.append({
                        "sample_id": i,
                        "generated_sequence": generated_sequence.cpu().tolist(),
                        "target_sequence": target_sequence.cpu().tolist(),
                        "quality_metrics": quality_metrics.to_dict(),
                        "song_id": batch["song_ids"][0] if "song_ids" in batch else f"sample_{i}"
                    })

                except Exception as e:
                    self.logger.warning(f"Failed to generate sample {i}: {e}")

        return sample_outputs

    def _log_training_step(self, loss: float, outputs: Dict[str, Any], loss_weights: Dict[str, float]):
        """Log training step metrics"""
        # Console logging
        if self.global_step % (self.config.log_frequency * 10) == 0:
            lr = self.scheduler.get_last_lr()[0]
            memory_stats = self.memory_monitor.get_memory_stats()

            self.logger.info(
                f"Step {self.global_step}: Loss={loss:.4f}, LR={lr:.2e}, "
                f"GPU={memory_stats.gpu_utilization_percent:.1f}%"
            )

        # Wandb logging
        if wandb.run:
            log_dict = {
                "train/loss": loss,
                "train/learning_rate": self.scheduler.get_last_lr()[0],
                "step": self.global_step
            }

            # Log individual losses
            for loss_name, loss_value in outputs["losses"].items():
                log_dict[f"train/loss_{loss_name}"] = loss_value.item()
                log_dict[f"train/weight_{loss_name}"] = loss_weights.get(loss_name, 1.0)

            # Memory metrics
            memory_stats = self.memory_monitor.get_memory_stats()
            log_dict.update({
                "system/gpu_memory_gb": memory_stats.gpu_reserved_gb,
                "system/gpu_utilization": memory_stats.gpu_utilization_percent,
                "system/system_memory": memory_stats.system_memory_percent
            })

            wandb.log(log_dict)

    def save_final_model(self, output_path: str):
        """Save final trained model for Phase 4 integration"""
        self.logger.info(f"Saving final model to {output_path}")

        # Load best checkpoint
        best_checkpoint = self.checkpoint_manager.load_best_checkpoint()
        if best_checkpoint:
            self.model.load_state_dict(best_checkpoint["model_state_dict"])

        # Save model in Phase 4 compatible format
        model_state = {
            "model_state_dict": self.model.state_dict(),
            "model_config": self.config.get_model_config(),
            "training_config": self.config.get_training_config(),
            "phase5_config": {
                "experiment_name": self.config.experiment_name,
                "best_val_loss": self.best_val_loss,
                "best_quality_score": self.best_quality_score,
                "total_steps": self.global_step
            },
            "version": "phase5_v1.0"
        }

        torch.save(model_state, output_path)
        self.logger.info("Final model saved successfully")
