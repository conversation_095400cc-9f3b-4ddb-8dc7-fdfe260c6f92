"""
Advanced Data Augmentation - Phase 5

Sophisticated data augmentation strategies for TJA generation training
that work with real audio features and note sequences.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from typing import Dict, List, Optional, Any, Tuple
import logging

from .training_config import Phase5TrainingConfig


class AdvancedTJAAugmentation:
    """
    Advanced data augmentation for TJA training data
    
    Applies various augmentation strategies to audio features while
    maintaining consistency with note sequences and timing information.
    """
    
    def __init__(self, config: Phase5TrainingConfig):
        self.config = config
        self.probability = config.augmentation_probability
        self.strategies = config.augmentation_strategies
        self.logger = logging.getLogger(__name__)
        
        # Initialize augmentation modules
        self.tempo_augmenter = TempoVariationAugmentation()
        self.pitch_augmenter = PitchShiftAugmentation()
        self.noise_augmenter = NoiseInjectionAugmentation()
        self.time_masker = TimeMaskingAugmentation()
        self.freq_masker = FrequencyMaskingAugmentation()
        self.mixup_augmenter = MixupAugmentation(alpha=config.mixup_alpha if hasattr(config, 'mixup_alpha') else 0.2)
        
        self.logger.info(f"Initialized advanced augmentation with strategies: {self.strategies}")
    
    def apply_augmentation(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Apply augmentation strategies to batch
        
        Args:
            batch: Training batch containing audio_features, note_sequence, etc.
            
        Returns:
            Augmented batch
        """
        if not self.config.use_data_augmentation or random.random() > self.probability:
            return batch
        
        augmented_batch = batch.copy()
        
        # Apply each strategy with individual probability
        for strategy in self.strategies:
            if random.random() < 0.5:  # 50% chance for each strategy
                augmented_batch = self._apply_strategy(strategy, augmented_batch)
        
        return augmented_batch
    
    def _apply_strategy(self, strategy: str, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply specific augmentation strategy"""
        try:
            if strategy == "tempo_variation":
                return self.tempo_augmenter.apply(batch)
            elif strategy == "pitch_shift":
                return self.pitch_augmenter.apply(batch)
            elif strategy == "noise_injection":
                return self.noise_augmenter.apply(batch)
            elif strategy == "time_masking":
                return self.time_masker.apply(batch)
            elif strategy == "frequency_masking":
                return self.freq_masker.apply(batch)
            elif strategy == "mixup":
                return self.mixup_augmenter.apply(batch)
            else:
                self.logger.warning(f"Unknown augmentation strategy: {strategy}")
                return batch
        except Exception as e:
            self.logger.warning(f"Augmentation strategy {strategy} failed: {e}")
            return batch


class TempoVariationAugmentation:
    """
    Tempo variation augmentation
    
    Simulates tempo changes by interpolating/decimating audio features
    and adjusting note timing accordingly.
    """
    
    def __init__(self, tempo_range: Tuple[float, float] = (0.8, 1.2)):
        self.tempo_range = tempo_range
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply tempo variation"""
        tempo_factor = random.uniform(*self.tempo_range)
        
        if abs(tempo_factor - 1.0) < 0.05:  # Skip if change is too small
            return batch
        
        audio_features = batch["audio_features"]
        batch_size, seq_len, feature_dim = audio_features.shape
        
        # Calculate new sequence length
        new_seq_len = int(seq_len / tempo_factor)
        new_seq_len = max(50, min(new_seq_len, seq_len * 2))  # Reasonable bounds
        
        # Interpolate audio features
        audio_features_resized = F.interpolate(
            audio_features.transpose(1, 2),  # (batch, features, time)
            size=new_seq_len,
            mode='linear',
            align_corners=False
        ).transpose(1, 2)  # Back to (batch, time, features)
        
        # Adjust note sequences if they exist
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = audio_features_resized
        
        if "note_sequence" in batch:
            note_sequence = batch["note_sequence"]
            # Interpolate note sequence (using nearest neighbor for discrete values)
            note_sequence_resized = F.interpolate(
                note_sequence.float().unsqueeze(1),  # Add channel dim
                size=new_seq_len,
                mode='nearest'
            ).squeeze(1).long()  # Remove channel dim and convert back to long
            
            augmented_batch["note_sequence"] = note_sequence_resized
        
        # Update timing information if present
        if "timing_sequence" in batch:
            timing_sequence = batch["timing_sequence"]
            timing_sequence_resized = F.interpolate(
                timing_sequence.float().unsqueeze(1),
                size=new_seq_len,
                mode='linear',
                align_corners=False
            ).squeeze(1)
            
            augmented_batch["timing_sequence"] = timing_sequence_resized
        
        # Update masks if present
        if "audio_mask" in batch:
            audio_mask = batch["audio_mask"]
            audio_mask_resized = F.interpolate(
                audio_mask.float().unsqueeze(1),
                size=new_seq_len,
                mode='nearest'
            ).squeeze(1).bool()
            
            augmented_batch["audio_mask"] = audio_mask_resized
        
        return augmented_batch


class PitchShiftAugmentation:
    """
    Pitch shift augmentation
    
    Applies pitch shifting to audio features by modifying frequency components.
    """
    
    def __init__(self, pitch_range: Tuple[float, float] = (-2.0, 2.0)):
        self.pitch_range = pitch_range  # Semitones
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply pitch shifting"""
        pitch_shift = random.uniform(*self.pitch_range)
        
        if abs(pitch_shift) < 0.1:  # Skip if change is too small
            return batch
        
        audio_features = batch["audio_features"]
        
        # Apply pitch shift to frequency-related features
        # Assuming first 128 dimensions are mel-spectrogram features
        mel_features = audio_features[:, :, :128]
        other_features = audio_features[:, :, 128:]
        
        # Simple pitch shift by circular shifting frequency bins
        shift_bins = int(pitch_shift * 2)  # Rough conversion
        if shift_bins != 0:
            mel_features_shifted = torch.roll(mel_features, shift_bins, dims=2)
            
            # Zero out wrapped regions to avoid artifacts
            if shift_bins > 0:
                mel_features_shifted[:, :, :shift_bins] = 0
            else:
                mel_features_shifted[:, :, shift_bins:] = 0
        else:
            mel_features_shifted = mel_features
        
        # Combine shifted mel features with unchanged other features
        augmented_features = torch.cat([mel_features_shifted, other_features], dim=2)
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = augmented_features
        
        return augmented_batch


class NoiseInjectionAugmentation:
    """
    Noise injection augmentation
    
    Adds various types of noise to audio features to improve robustness.
    """
    
    def __init__(self, noise_std: float = 0.01, noise_types: List[str] = ["gaussian", "uniform"]):
        self.noise_std = noise_std
        self.noise_types = noise_types
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply noise injection"""
        noise_type = random.choice(self.noise_types)
        audio_features = batch["audio_features"]
        
        if noise_type == "gaussian":
            noise = torch.randn_like(audio_features) * self.noise_std
        elif noise_type == "uniform":
            noise = (torch.rand_like(audio_features) - 0.5) * 2 * self.noise_std
        else:
            return batch
        
        # Apply noise with some probability per feature
        noise_mask = torch.rand_like(audio_features) < 0.1  # 10% of features get noise
        noise = noise * noise_mask
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = audio_features + noise
        
        return augmented_batch


class TimeMaskingAugmentation:
    """
    Time masking augmentation (SpecAugment style)
    
    Masks random time segments in audio features.
    """
    
    def __init__(self, max_mask_length: int = 20, num_masks: int = 2):
        self.max_mask_length = max_mask_length
        self.num_masks = num_masks
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply time masking"""
        audio_features = batch["audio_features"]
        batch_size, seq_len, feature_dim = audio_features.shape
        
        augmented_features = audio_features.clone()
        
        for _ in range(self.num_masks):
            # Random mask length
            mask_length = random.randint(1, min(self.max_mask_length, seq_len // 4))
            
            # Random start position
            start_pos = random.randint(0, max(0, seq_len - mask_length))
            
            # Apply mask (set to zero or mean value)
            mask_value = augmented_features.mean()
            augmented_features[:, start_pos:start_pos + mask_length, :] = mask_value
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = augmented_features
        
        return augmented_batch


class FrequencyMaskingAugmentation:
    """
    Frequency masking augmentation (SpecAugment style)
    
    Masks random frequency bands in audio features.
    """
    
    def __init__(self, max_mask_width: int = 10, num_masks: int = 2):
        self.max_mask_width = max_mask_width
        self.num_masks = num_masks
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply frequency masking"""
        audio_features = batch["audio_features"]
        batch_size, seq_len, feature_dim = audio_features.shape
        
        augmented_features = audio_features.clone()
        
        # Only apply to frequency-related features (first 128 dimensions assumed to be mel)
        mel_features = augmented_features[:, :, :128]
        
        for _ in range(self.num_masks):
            # Random mask width
            mask_width = random.randint(1, min(self.max_mask_width, 128 // 4))
            
            # Random start frequency
            start_freq = random.randint(0, max(0, 128 - mask_width))
            
            # Apply mask
            mask_value = mel_features.mean()
            mel_features[:, :, start_freq:start_freq + mask_width] = mask_value
        
        # Update the mel features in the full feature tensor
        augmented_features[:, :, :128] = mel_features
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = augmented_features
        
        return augmented_batch


class MixupAugmentation:
    """
    Mixup augmentation for TJA training
    
    Mixes audio features and creates soft targets for note sequences.
    """
    
    def __init__(self, alpha: float = 0.2):
        self.alpha = alpha
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply mixup augmentation"""
        if self.alpha <= 0:
            return batch
        
        batch_size = batch["audio_features"].size(0)
        if batch_size < 2:
            return batch
        
        # Sample mixing coefficient
        lam = np.random.beta(self.alpha, self.alpha)
        
        # Random permutation for mixing
        indices = torch.randperm(batch_size)
        
        # Mix audio features
        mixed_audio = lam * batch["audio_features"] + (1 - lam) * batch["audio_features"][indices]
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = mixed_audio
        
        # Store mixup information for loss computation
        augmented_batch["mixup_lambda"] = lam
        augmented_batch["mixup_indices"] = indices
        
        # Create soft targets for note sequences if present
        if "note_sequence" in batch:
            note_sequence = batch["note_sequence"]
            num_classes = 8  # Number of note types
            
            # Convert to one-hot
            seq1_onehot = F.one_hot(note_sequence, num_classes).float()
            seq2_onehot = F.one_hot(note_sequence[indices], num_classes).float()
            
            # Mix one-hot representations
            mixed_targets = lam * seq1_onehot + (1 - lam) * seq2_onehot
            augmented_batch["mixed_note_targets"] = mixed_targets
        
        return augmented_batch


class CutMixAugmentation:
    """
    CutMix augmentation adapted for sequence data
    
    Replaces segments of sequences with segments from other samples.
    """
    
    def __init__(self, alpha: float = 1.0, cutmix_prob: float = 0.5):
        self.alpha = alpha
        self.cutmix_prob = cutmix_prob
    
    def apply(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply CutMix augmentation"""
        if random.random() > self.cutmix_prob:
            return batch
        
        batch_size = batch["audio_features"].size(0)
        if batch_size < 2:
            return batch
        
        audio_features = batch["audio_features"]
        seq_len = audio_features.size(1)
        
        # Sample mixing ratio
        lam = np.random.beta(self.alpha, self.alpha)
        
        # Calculate cut size
        cut_len = int(seq_len * (1 - lam))
        
        # Random cut position
        cut_start = random.randint(0, seq_len - cut_len)
        cut_end = cut_start + cut_len
        
        # Random permutation for mixing
        indices = torch.randperm(batch_size)
        
        # Apply CutMix to audio features
        mixed_audio = audio_features.clone()
        mixed_audio[:, cut_start:cut_end, :] = audio_features[indices, cut_start:cut_end, :]
        
        augmented_batch = batch.copy()
        augmented_batch["audio_features"] = mixed_audio
        
        # Store CutMix information
        augmented_batch["cutmix_lambda"] = lam
        augmented_batch["cutmix_indices"] = indices
        augmented_batch["cutmix_start"] = cut_start
        augmented_batch["cutmix_end"] = cut_end
        
        # Apply CutMix to note sequences if present
        if "note_sequence" in batch:
            note_sequence = batch["note_sequence"]
            mixed_notes = note_sequence.clone()
            mixed_notes[:, cut_start:cut_end] = note_sequence[indices, cut_start:cut_end]
            augmented_batch["note_sequence"] = mixed_notes
        
        return augmented_batch
