"""
Spectral Feature Processor

Extracts spectral features optimized for rhythm detection and TJA generation.
Includes Mel-spectrogram, MFCC, and Chroma features with GPU acceleration.
"""

import torch
import torchaudio.transforms as T
import librosa
import numpy as np
import logging
from typing import Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class SpectralConfig:
    """Configuration for spectral feature extraction"""
    sample_rate: int = 44100
    frame_rate: float = 50.0
    hop_length: int = 882  # 44100/50
    n_fft: int = 2048
    
    # Mel-spectrogram settings (optimized for drum content)
    n_mels: int = 128
    f_min: float = 80.0    # Low frequency for bass drums
    f_max: float = 8000.0  # High frequency for cymbals/hi-hats
    
    # MFCC settings
    n_mfcc: int = 13
    
    # Chroma settings
    n_chroma: int = 12


class SpectralProcessor:
    """GPU-accelerated spectral feature extraction"""
    
    def __init__(self, config: Optional[SpectralConfig] = None, device: Optional[torch.device] = None):
        self.config = config or SpectralConfig()
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(__name__)
        
        # Initialize GPU transforms
        self._initialize_transforms()
        
        self.logger.info(f"SpectralProcessor initialized on {self.device}")
    
    def _initialize_transforms(self):
        """Initialize GPU-accelerated transforms"""
        try:
            # Mel-spectrogram transform
            self.mel_transform = T.MelSpectrogram(
                sample_rate=self.config.sample_rate,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                n_mels=self.config.n_mels,
                f_min=self.config.f_min,
                f_max=self.config.f_max,
                power=2.0,
                normalized=False
            ).to(self.device)
            
            # MFCC transform
            self.mfcc_transform = T.MFCC(
                sample_rate=self.config.sample_rate,
                n_mfcc=self.config.n_mfcc,
                melkwargs={
                    "n_fft": self.config.n_fft,
                    "hop_length": self.config.hop_length,
                    "n_mels": self.config.n_mels,
                    "f_min": self.config.f_min,
                    "f_max": self.config.f_max,
                    "power": 2.0,
                    "normalized": False
                }
            ).to(self.device)
            
            # Amplitude to dB conversion
            self.amplitude_to_db = T.AmplitudeToDB(
                stype="power",
                top_db=80.0
            ).to(self.device)
            
            self.logger.info("GPU transforms initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize GPU transforms: {e}")
            self.device = torch.device("cpu")
            self._initialize_cpu_fallback()
    
    def _initialize_cpu_fallback(self):
        """Initialize CPU fallback transforms"""
        self.logger.warning("Falling back to CPU transforms")
        
        self.mel_transform = T.MelSpectrogram(
            sample_rate=self.config.sample_rate,
            n_fft=self.config.n_fft,
            hop_length=self.config.hop_length,
            n_mels=self.config.n_mels,
            f_min=self.config.f_min,
            f_max=self.config.f_max,
            power=2.0
        )
        
        self.mfcc_transform = T.MFCC(
            sample_rate=self.config.sample_rate,
            n_mfcc=self.config.n_mfcc,
            melkwargs={
                "n_fft": self.config.n_fft,
                "hop_length": self.config.hop_length,
                "n_mels": self.config.n_mels,
                "f_min": self.config.f_min,
                "f_max": self.config.f_max
            }
        )
        
        self.amplitude_to_db = T.AmplitudeToDB(stype="power", top_db=80.0)
    
    def extract_mel_spectrogram(self, audio: torch.Tensor) -> torch.Tensor:
        """
        Extract Mel-spectrogram features
        
        Args:
            audio: Audio tensor [samples] or [batch, samples]
            
        Returns:
            Mel-spectrogram tensor [time, n_mels] or [batch, time, n_mels]
        """
        try:
            # Ensure audio is on correct device
            audio = audio.to(self.device)
            
            # Handle batch dimension
            if audio.dim() == 1:
                audio = audio.unsqueeze(0)  # Add batch dimension
                squeeze_output = True
            else:
                squeeze_output = False
            
            # Extract mel-spectrogram
            mel_spec = self.mel_transform(audio)
            
            # Convert to dB scale
            mel_spec_db = self.amplitude_to_db(mel_spec)
            
            # Transpose to [batch, time, freq] format
            mel_spec_db = mel_spec_db.transpose(-2, -1)
            
            # Remove batch dimension if input was 1D
            if squeeze_output:
                mel_spec_db = mel_spec_db.squeeze(0)
            
            return mel_spec_db
            
        except Exception as e:
            self.logger.error(f"Error extracting mel-spectrogram: {e}")
            raise
    
    def extract_mfcc(self, audio: torch.Tensor) -> torch.Tensor:
        """
        Extract MFCC features
        
        Args:
            audio: Audio tensor [samples] or [batch, samples]
            
        Returns:
            MFCC tensor [time, n_mfcc] or [batch, time, n_mfcc]
        """
        try:
            # Ensure audio is on correct device
            audio = audio.to(self.device)
            
            # Handle batch dimension
            if audio.dim() == 1:
                audio = audio.unsqueeze(0)
                squeeze_output = True
            else:
                squeeze_output = False
            
            # Extract MFCC
            mfcc = self.mfcc_transform(audio)
            
            # Transpose to [batch, time, coeff] format
            mfcc = mfcc.transpose(-2, -1)
            
            # Remove batch dimension if input was 1D
            if squeeze_output:
                mfcc = mfcc.squeeze(0)
            
            return mfcc
            
        except Exception as e:
            self.logger.error(f"Error extracting MFCC: {e}")
            raise
    
    def extract_chroma(self, audio: torch.Tensor) -> torch.Tensor:
        """
        Extract Chroma features (CPU-based using librosa)
        
        Args:
            audio: Audio tensor [samples]
            
        Returns:
            Chroma tensor [time, n_chroma]
        """
        try:
            # Convert to numpy for librosa processing
            if isinstance(audio, torch.Tensor):
                audio_np = audio.cpu().numpy()
            else:
                audio_np = audio
            
            # Handle batch dimension
            if audio_np.ndim == 2:
                # Process each item in batch
                batch_chroma = []
                for i in range(audio_np.shape[0]):
                    chroma = librosa.feature.chroma_stft(
                        y=audio_np[i],
                        sr=self.config.sample_rate,
                        hop_length=self.config.hop_length,
                        n_chroma=self.config.n_chroma
                    )
                    batch_chroma.append(chroma.T)  # Transpose to [time, chroma]
                
                chroma_tensor = torch.from_numpy(np.stack(batch_chroma)).float()
            else:
                # Single audio file
                chroma = librosa.feature.chroma_stft(
                    y=audio_np,
                    sr=self.config.sample_rate,
                    hop_length=self.config.hop_length,
                    n_chroma=self.config.n_chroma
                )
                chroma_tensor = torch.from_numpy(chroma.T).float()  # Transpose to [time, chroma]
            
            return chroma_tensor.to(self.device)
            
        except Exception as e:
            self.logger.error(f"Error extracting chroma: {e}")
            raise
    
    def extract_all_spectral_features(self, audio: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Extract all spectral features (mel-spectrogram, MFCC, chroma)
        
        Args:
            audio: Audio tensor [samples] or [batch, samples]
            
        Returns:
            Dictionary containing all spectral features
        """
        try:
            # Extract individual features
            mel_spec = self.extract_mel_spectrogram(audio)
            mfcc = self.extract_mfcc(audio)
            chroma = self.extract_chroma(audio)
            
            # Ensure all features have the same time dimension
            min_time_frames = min(mel_spec.shape[-2], mfcc.shape[-2], chroma.shape[-2])
            
            # Trim to same length
            mel_spec = mel_spec[..., :min_time_frames, :]
            mfcc = mfcc[..., :min_time_frames, :]
            chroma = chroma[..., :min_time_frames, :]
            
            # Combine features
            if audio.dim() == 1:  # Single audio file
                combined_features = torch.cat([mel_spec, mfcc, chroma], dim=-1)
            else:  # Batch
                combined_features = torch.cat([mel_spec, mfcc, chroma], dim=-1)
            
            return {
                "mel_spectrogram": mel_spec,
                "mfcc": mfcc,
                "chroma": chroma,
                "combined_spectral": combined_features,
                "feature_info": {
                    "time_frames": min_time_frames,
                    "mel_dims": mel_spec.shape[-1],
                    "mfcc_dims": mfcc.shape[-1],
                    "chroma_dims": chroma.shape[-1],
                    "total_spectral_dims": combined_features.shape[-1]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting spectral features: {e}")
            raise
    
    def validate_spectral_features(self, features: Dict[str, torch.Tensor]) -> Dict[str, bool]:
        """
        Validate extracted spectral features
        
        Args:
            features: Dictionary of extracted features
            
        Returns:
            Dictionary of validation results
        """
        validation = {}
        
        try:
            # Check mel-spectrogram
            mel_spec = features["mel_spectrogram"]
            validation["mel_shape_valid"] = mel_spec.shape[-1] == self.config.n_mels
            validation["mel_no_nan"] = not torch.isnan(mel_spec).any()
            validation["mel_finite"] = torch.isfinite(mel_spec).all()
            validation["mel_dynamic_range"] = float(mel_spec.max() - mel_spec.min()) > 10.0
            
            # Check MFCC
            mfcc = features["mfcc"]
            validation["mfcc_shape_valid"] = mfcc.shape[-1] == self.config.n_mfcc
            validation["mfcc_no_nan"] = not torch.isnan(mfcc).any()
            validation["mfcc_finite"] = torch.isfinite(mfcc).all()
            
            # Check chroma
            chroma = features["chroma"]
            validation["chroma_shape_valid"] = chroma.shape[-1] == self.config.n_chroma
            validation["chroma_no_nan"] = not torch.isnan(chroma).any()
            validation["chroma_finite"] = torch.isfinite(chroma).all()
            validation["chroma_normalized"] = (chroma >= 0).all() and (chroma <= 1).all()
            
            # Overall validation
            validation["all_features_valid"] = all(validation.values())
            
        except Exception as e:
            self.logger.error(f"Error validating spectral features: {e}")
            validation["validation_error"] = str(e)
            validation["all_features_valid"] = False
        
        return validation
