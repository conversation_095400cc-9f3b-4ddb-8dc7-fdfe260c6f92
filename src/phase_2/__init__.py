"""
Phase 2: Audio Feature Extraction and Temporal Alignment

This phase transforms validated audio files into rich, time-aligned feature
representations suitable for machine learning. It extracts multi-scale audio
features that capture rhythmic patterns, harmonic content, and temporal dynamics
essential for generating high-difficulty TJA charts.

Key Components:
- Multi-scale audio feature extraction
- Temporal alignment with TJA timing
- Hardware-optimized processing (RTX 3070)
- Feature validation and quality assessment

Input: Validated audio files from Phase 1
Output: Time-aligned audio features [T, 201] for Phase 3 consumption
"""

from .controller import Phase2Controller
from .feature_extractor import AudioFeatureExtractor
from .spectral_processor import SpectralProcessor
from .rhythmic_processor import RhythmicProcessor
from .temporal_aligner import TemporalAligner
from .unified_audio_processor import (
    UnifiedAudioProcessor,
    AudioProcessingConfig,
    SpectralFeatureExtractor,
    RhythmicFeatureExtractor,
    TemporalFeatureExtractor,
    AudioFeatureExtractorInterface
)
from .gpu_optimizer import RTX3070Optimizer

__all__ = [
    'Phase2Controller',
    'AudioFeatureExtractor',
    'SpectralProcessor',
    'RhythmicProcessor',
    'TemporalAligner',
    'UnifiedAudioProcessor',
    'AudioProcessingConfig',
    'SpectralFeatureExtractor',
    'RhythmicFeatureExtractor',
    'TemporalFeatureExtractor',
    'AudioFeatureExtractorInterface',
    'RTX3070Optimizer'
]
