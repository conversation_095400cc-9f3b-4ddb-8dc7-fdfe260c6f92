"""
Phase 3: TJA Sequence Processing and Temporal Encoding

This phase transforms TJA chart data into structured sequence representations
suitable for neural network training. It creates temporal encodings that capture
note patterns, difficulty progression, and musical structure while maintaining
precise alignment with audio features from Phase 2.

Key Components:
- TJA sequence parsing and encoding
- Temporal alignment with audio features
- Note pattern extraction and validation
- Difficulty-aware sequence processing

Input: Audio features [T, 201] and TJA parsed data from Phases 1-2
Output: Note sequences [T, 45] for Phase 4 consumption
"""

from .controller import Phase3Controller
from .unified_tja_processor import (
    UnifiedTjaProcessor,
    UnifiedTjaParser,
    UnifiedTjaValidator,
    TjaChart,
    TjaCourse,
    TjaNote,
    TjaCommand,
    TjaParserInterface,
    TjaValidatorInterface
)

__all__ = [
    'Phase3Controller',
    'UnifiedTjaProcessor',
    'UnifiedTjaParser',
    'UnifiedTjaValidator',
    'TjaChart',
    'TjaCourse',
    'TjaNote',
    'TjaCommand',
    'TjaParserInterface',
    'TjaValidatorInterface'
]
