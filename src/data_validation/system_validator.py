from typing import (
    Any,
    Dict,
    List,
    Optional
)
"""
System Validation Framework

Comprehensive validation system to ensure all refactored components maintain
full functionality and data flow integrity between phases.
"""

import json
import time
import logging
from pathlib import Path
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..utils.base_classes import BaseValidator, ValidationResult
from ..config.unified_config_manager import UnifiedConfigManager
from ..path_management.path_manager import PathManager, PathType


@dataclass
class ComponentValidationResult:
    """Result of component validation"""
    component_name: str
    is_functional: bool
    performance_score: float
    memory_usage_mb: float
    processing_time_seconds: float
    errors: List[str]
    warnings: List[str]
    test_results: Dict[str, Any]


@dataclass
class SystemValidationReport:
    """Complete system validation report"""
    validation_timestamp: str
    overall_health_score: float
    components_tested: int
    components_passed: int
    components_failed: int
    component_results: List[ComponentValidationResult]
    data_flow_validation: Dict[str, Any]
    performance_metrics: Dict[str, float]
    recommendations: List[str]


class ComponentValidatorInterface(ABC):
    """Interface for component validators"""
    
    @abstractmethod
    def validate_component(self) -> ComponentValidationResult:
        """Validate specific component"""
        pass
    
    @abstractmethod
    def get_component_name(self) -> str:
        """Get component name"""
        pass


class BaseProcessorValidator(ComponentValidatorInterface):
    """Validator for BaseProcessor implementations"""
    
    def __init__(self, processor_class, test_data: Any = None):
        self.processor_class = processor_class
        self.test_data = test_data
        self.logger = logging.getLogger(f"{__name__}.{processor_class.__name__}Validator")
    
    def validate_component(self) -> ComponentValidationResult:
        """Validate processor component"""
        start_time = time.time()
        errors = []
        warnings = []
        test_results = {}
        
        try:
            # Test instantiation
            processor = self.processor_class()
            test_results["instantiation"] = "success"
            
            # Test abstract method implementation
            if hasattr(processor, 'process'):
                test_results["process_method"] = "implemented"
            else:
                errors.append("Missing required process method")
            
            # Test configuration handling
            if hasattr(processor, 'config'):
                test_results["configuration"] = "supported"
            else:
                warnings.append("No configuration support detected")
            
            # Test memory monitoring
            if hasattr(processor, 'memory_monitor'):
                test_results["memory_monitoring"] = "enabled"
            else:
                warnings.append("Memory monitoring not available")
            
            # Test statistics tracking
            if hasattr(processor, 'get_statistics'):
                stats = processor.get_statistics()
                test_results["statistics"] = stats
            else:
                warnings.append("Statistics tracking not available")
            
            # Test processing if test data provided
            if self.test_data is not None:
                try:
                    result = processor.process(self.test_data)
                    if hasattr(result, 'success'):
                        test_results["processing"] = "functional" if result.success else "failed"
                        if not result.success:
                            errors.append(f"Processing failed: {result.error_message}")
                    else:
                        warnings.append("Processing result format non-standard")
                except Exception as e:
                    errors.append(f"Processing test failed: {e}")
                    test_results["processing"] = "error"
            
            # Calculate performance score
            performance_score = self._calculate_performance_score(test_results, errors, warnings)
            
            # Get memory usage
            memory_usage = getattr(processor, 'memory_monitor', None)
            memory_mb = memory_usage.get_current_usage() if memory_usage else 0.0
            
            processing_time = time.time() - start_time
            
            return ComponentValidationResult(
                component_name=self.get_component_name(),
                is_functional=len(errors) == 0,
                performance_score=performance_score,
                memory_usage_mb=memory_mb,
                processing_time_seconds=processing_time,
                errors=errors,
                warnings=warnings,
                test_results=test_results
            )
            
        except Exception as e:
            return ComponentValidationResult(
                component_name=self.get_component_name(),
                is_functional=False,
                performance_score=0.0,
                memory_usage_mb=0.0,
                processing_time_seconds=time.time() - start_time,
                errors=[f"Validation failed: {e}"],
                warnings=[],
                test_results={"validation_error": str(e)}
            )
    
    def get_component_name(self) -> str:
        return self.processor_class.__name__
    
    def _calculate_performance_score(self, test_results: Dict, errors: List, warnings: List) -> float:
        """Calculate performance score based on test results"""
        base_score = 1.0
        
        # Deduct for errors and warnings
        base_score -= len(errors) * 0.3
        base_score -= len(warnings) * 0.1
        
        # Bonus for implemented features
        if test_results.get("processing") == "functional":
            base_score += 0.2
        if test_results.get("memory_monitoring") == "enabled":
            base_score += 0.1
        if test_results.get("statistics"):
            base_score += 0.1
        
        return max(0.0, min(1.0, base_score))


class DataFlowValidator:
    """Validator for data flow between phases"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.DataFlowValidator")
        self.path_manager = PathManager()
        self.config_manager = UnifiedConfigManager()
    
    def validate_phase_data_flow(self) -> Dict[str, Any]:
        """Validate data flow between all phases"""
        flow_results = {}
        
        # Phase 1 -> Phase 2 flow
        flow_results["phase1_to_phase2"] = self._validate_phase1_to_phase2()
        
        # Phase 2 -> Phase 3 flow
        flow_results["phase2_to_phase3"] = self._validate_phase2_to_phase3()
        
        # Phase 3 -> Phase 4 flow
        flow_results["phase3_to_phase4"] = self._validate_phase3_to_phase4()
        
        # Overall flow health
        flow_results["overall_health"] = self._calculate_flow_health(flow_results)
        
        return flow_results
    
    def _validate_phase1_to_phase2(self) -> Dict[str, Any]:
        """Validate Phase 1 to Phase 2 data flow"""
        result = {
            "catalog_exists": False,
            "catalog_valid": False,
            "audio_files_accessible": False,
            "metadata_complete": False,
            "phase2_ready_count": 0,
            "issues": []
        }
        
        try:
            # Check for Phase 1 catalog
            catalog_path = self.path_manager.get_standardized_path(
                PathType.DATA_PROCESSED, "catalog.json"
            )
            
            if catalog_path.exists():
                result["catalog_exists"] = True
                
                # Load and validate catalog
                with open(catalog_path, 'r', encoding='utf-8') as f:
                    catalog = json.load(f)
                
                if "songs" in catalog:
                    result["catalog_valid"] = True
                    songs = catalog["songs"]
                    
                    # Check Phase 2 readiness
                    phase2_ready = [s for s in songs if s.get("phase_2_ready", False)]
                    result["phase2_ready_count"] = len(phase2_ready)
                    
                    # Check audio file accessibility
                    accessible_count = 0
                    for song in phase2_ready[:10]:  # Sample first 10
                        audio_path = song.get("audio_path")
                        if audio_path and Path(audio_path).exists():
                            accessible_count += 1
                    
                    result["audio_files_accessible"] = accessible_count > 0
                    
                    # Check metadata completeness
                    complete_metadata = 0
                    for song in songs[:10]:  # Sample first 10
                        if (song.get("reference_metadata") and 
                            song.get("notation_metadata")):
                            complete_metadata += 1
                    
                    result["metadata_complete"] = complete_metadata > 0
                    
                else:
                    result["issues"].append("Catalog missing 'songs' field")
            else:
                result["issues"].append("Phase 1 catalog not found")
                
        except Exception as e:
            result["issues"].append(f"Validation error: {e}")
        
        return result
    
    def _validate_phase2_to_phase3(self) -> Dict[str, Any]:
        """Validate Phase 2 to Phase 3 data flow"""
        result = {
            "feature_tensors_exist": False,
            "feature_dimensions_correct": False,
            "phase3_catalog_exists": False,
            "tensor_count": 0,
            "issues": []
        }
        
        try:
            # Check for feature tensors directory
            features_path = self.path_manager.get_standardized_path(PathType.AUDIO_FEATURES)
            tensor_path = features_path / "feature_tensors"
            
            if tensor_path.exists():
                # Count tensor files
                tensor_files = list(tensor_path.glob("*.pt"))
                result["tensor_count"] = len(tensor_files)
                result["feature_tensors_exist"] = len(tensor_files) > 0
                
                # Check tensor dimensions (sample first file)
                if tensor_files:
                    try:
                        import torch
                        sample_tensor = torch.load(tensor_files[0], weights_only=False)
                        if hasattr(sample_tensor, 'shape') and len(sample_tensor.shape) == 2:
                            if sample_tensor.shape[1] == 201:  # Expected feature dimensions
                                result["feature_dimensions_correct"] = True
                            else:
                                result["issues"].append(
                                    f"Incorrect feature dimensions: {sample_tensor.shape[1]} (expected 201)"
                                )
                    except Exception as e:
                        result["issues"].append(f"Error checking tensor dimensions: {e}")
            else:
                result["issues"].append("Feature tensors directory not found")
            
            # Check for Phase 3 input catalog
            phase3_catalog = features_path / "phase3_input_catalog.json"
            result["phase3_catalog_exists"] = phase3_catalog.exists()
            
            if not phase3_catalog.exists():
                result["issues"].append("Phase 3 input catalog not found")
                
        except Exception as e:
            result["issues"].append(f"Validation error: {e}")
        
        return result
    
    def _validate_phase3_to_phase4(self) -> Dict[str, Any]:
        """Validate Phase 3 to Phase 4 data flow"""
        result = {
            "model_exists": False,
            "model_loadable": False,
            "checkpoint_valid": False,
            "issues": []
        }
        
        try:
            # Check for trained models
            models_path = self.path_manager.get_standardized_path(PathType.MODELS)
            
            # Look for model files
            model_files = list(models_path.glob("*.pt")) + list(models_path.glob("*.pth"))
            result["model_exists"] = len(model_files) > 0
            
            if model_files:
                # Try to load a model
                try:
                    import torch
                    model_data = torch.load(model_files[0], map_location='cpu', weights_only=False)
                    result["model_loadable"] = True
                    
                    # Check if it's a proper checkpoint
                    if isinstance(model_data, dict) and 'model_state_dict' in model_data:
                        result["checkpoint_valid"] = True
                    
                except Exception as e:
                    result["issues"].append(f"Model loading failed: {e}")
            else:
                result["issues"].append("No model files found")
                
        except Exception as e:
            result["issues"].append(f"Validation error: {e}")
        
        return result
    
    def _calculate_flow_health(self, flow_results: Dict[str, Any]) -> float:
        """Calculate overall data flow health score"""
        scores = []
        
        for phase_flow in ["phase1_to_phase2", "phase2_to_phase3", "phase3_to_phase4"]:
            if phase_flow in flow_results:
                phase_result = flow_results[phase_flow]
                phase_score = 0.0
                
                # Count successful validations
                for key, value in phase_result.items():
                    if key != "issues" and isinstance(value, bool) and value:
                        phase_score += 0.25
                    elif key.endswith("_count") and isinstance(value, int) and value > 0:
                        phase_score += 0.25
                
                # Deduct for issues
                issues_count = len(phase_result.get("issues", []))
                phase_score -= issues_count * 0.1
                
                scores.append(max(0.0, min(1.0, phase_score)))
        
        return sum(scores) / len(scores) if scores else 0.0


class SystemValidator(BaseValidator):
    """
    Comprehensive system validator
    
    Validates all refactored components and data flow integrity
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.path_manager = PathManager()
        self.config_manager = UnifiedConfigManager()
        self.data_flow_validator = DataFlowValidator()
        
        # Component validators
        self.component_validators = []
        self._initialize_component_validators()
    
    def _initialize_component_validators(self):
        """Initialize validators for all components"""
        try:
            # Import and validate core processors
            from ..preprocessing.data_analyzer import TjaDataAnalyzer
            from ..pipeline.audio_analyzer import TjaAudioFeatureAnalyzer
            from ..audio.unified_audio_processor import UnifiedAudioProcessor
            from ..tja.unified_tja_processor import UnifiedTjaProcessor
            from ..io.unified_io_manager import UnifiedIoManager
            
            # Add component validators
            self.component_validators.extend([
                BaseProcessorValidator(TjaDataAnalyzer, ["test_file.tja"]),
                BaseProcessorValidator(TjaAudioFeatureAnalyzer, {"audio_files": []}),
                BaseProcessorValidator(UnifiedAudioProcessor, "test_audio.wav"),
                BaseProcessorValidator(UnifiedTjaProcessor, "test_file.tja"),
                BaseProcessorValidator(UnifiedIoManager, {"operation": "read", "file_path": "test.json"})
            ])
            
        except ImportError as e:
            self.logger.warning(f"Could not import component for validation: {e}")
    
    def validate(self, data: Any = None) -> ValidationResult:
        """Validate entire system according to BaseValidator interface"""
        validation_report = self.run_comprehensive_validation()
        
        return ValidationResult(
            is_valid=validation_report.components_failed == 0,
            quality_score=validation_report.overall_health_score,
            errors=[f"Component {r.component_name} failed" for r in validation_report.component_results if not r.is_functional],
            warnings=[f"Component {r.component_name} has warnings" for r in validation_report.component_results if r.warnings],
            metrics={
                "components_tested": validation_report.components_tested,
                "components_passed": validation_report.components_passed,
                "data_flow_health": validation_report.data_flow_validation.get("overall_health", 0.0)
            }
        )
    
    def run_comprehensive_validation(self) -> SystemValidationReport:
        """Run comprehensive system validation"""
        start_time = time.time()
        
        # Validate all components
        component_results = []
        for validator in self.component_validators:
            try:
                result = validator.validate_component()
                component_results.append(result)
            except Exception as e:
                self.logger.error(f"Component validation failed: {e}")
                component_results.append(ComponentValidationResult(
                    component_name=validator.get_component_name(),
                    is_functional=False,
                    performance_score=0.0,
                    memory_usage_mb=0.0,
                    processing_time_seconds=0.0,
                    errors=[str(e)],
                    warnings=[],
                    test_results={}
                ))
        
        # Validate data flow
        data_flow_results = self.data_flow_validator.validate_phase_data_flow()
        
        # Calculate metrics
        components_passed = sum(1 for r in component_results if r.is_functional)
        components_failed = len(component_results) - components_passed
        
        # Calculate overall health score
        component_scores = [r.performance_score for r in component_results]
        avg_component_score = sum(component_scores) / len(component_scores) if component_scores else 0.0
        data_flow_score = data_flow_results.get("overall_health", 0.0)
        overall_health = (avg_component_score + data_flow_score) / 2
        
        # Generate recommendations
        recommendations = self._generate_recommendations(component_results, data_flow_results)
        
        # Performance metrics
        performance_metrics = {
            "validation_time_seconds": time.time() - start_time,
            "average_component_score": avg_component_score,
            "data_flow_score": data_flow_score,
            "total_memory_usage_mb": sum(r.memory_usage_mb for r in component_results)
        }
        
        return SystemValidationReport(
            validation_timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            overall_health_score=overall_health,
            components_tested=len(component_results),
            components_passed=components_passed,
            components_failed=components_failed,
            component_results=component_results,
            data_flow_validation=data_flow_results,
            performance_metrics=performance_metrics,
            recommendations=recommendations
        )
    
    def _generate_recommendations(self, component_results: List[ComponentValidationResult], 
                                data_flow_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Component-based recommendations
        for result in component_results:
            if not result.is_functional:
                recommendations.append(f"Fix critical issues in {result.component_name}")
            elif result.performance_score < 0.7:
                recommendations.append(f"Optimize performance of {result.component_name}")
            if result.memory_usage_mb > 1000:  # > 1GB
                recommendations.append(f"Reduce memory usage in {result.component_name}")
        
        # Data flow recommendations
        flow_health = data_flow_results.get("overall_health", 0.0)
        if flow_health < 0.5:
            recommendations.append("Critical data flow issues detected - review phase integration")
        elif flow_health < 0.8:
            recommendations.append("Data flow optimization recommended")
        
        # Phase-specific recommendations
        phase1_to_phase2 = data_flow_results.get("phase1_to_phase2", {})
        if not phase1_to_phase2.get("catalog_exists"):
            recommendations.append("Run Phase 1 to generate required catalog")
        
        phase2_to_phase3 = data_flow_results.get("phase2_to_phase3", {})
        if not phase2_to_phase3.get("feature_tensors_exist"):
            recommendations.append("Run Phase 2 to generate feature tensors")
        
        return recommendations
