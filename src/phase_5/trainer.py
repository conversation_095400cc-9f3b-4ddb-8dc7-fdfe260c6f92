from typing import Any, Dict, Optional
"""
TJA Trainer

Main training loop for TJA generation model with RTX 3070 optimization,
mixed precision training, and comprehensive evaluation.
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import time
import logging
import json
from pathlib import Path
from tqdm import tqdm
import wandb

from src.phase_4.tja_generator import TJAGeneratorModel
from .optimizer import (
    create_optimizer, create_scheduler, GradientAccumulator,
    MixedPrecisionTrainer, GradientClipper, estimate_memory_usage
)
from .metrics import TJAMetrics, GenerationEvaluator
from src.shared.utils.memory_monitor import MemoryMonitor, MemoryContext


class TJATrainer:
    """
    Comprehensive trainer for TJA generation model
    
    Features:
    - Mixed precision training for RTX 3070 optimization
    - Gradient accumulation for effective large batch training
    - Comprehensive evaluation and metrics
    - Checkpointing and resuming
    - Wandb integration for experiment tracking
    """
    
    def __init__(self, model: TJAGeneratorModel, config: Dict[str, Any],
                 output_dir: str = "outputs/phase3_training"):
        """
        Initialize trainer
        
        Args:
            model: TJA generation model
            config: Training configuration
            output_dir: Directory for saving outputs
        """
        self.model = model
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Device setup
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # Training components
        self.optimizer = create_optimizer(self.model, config["optimization"])
        self.scheduler = create_scheduler(self.optimizer, config["optimization"])
        
        # Training utilities
        self.gradient_accumulator = GradientAccumulator(
            config["data"]["gradient_accumulation_steps"]
        )
        self.mixed_precision = MixedPrecisionTrainer(
            config.get("mixed_precision", False)  # Default to False for stability
        )
        self.gradient_clipper = GradientClipper(
            config.get("gradient_clip_norm", 1.0)
        )
        
        # Metrics and evaluation
        self.metrics = TJAMetrics()
        self.evaluator = GenerationEvaluator(config["evaluation"])

        # Memory monitoring
        self.memory_monitor = MemoryMonitor(self.device)

        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.early_stopping_counter = 0
        
        # Memory estimation and monitoring
        self._log_memory_estimates()
        self.memory_monitor.log_memory_status("Initialization")

        # Initialize wandb if configured
        self._init_wandb()
        
        self.logger.info("TJATrainer initialized successfully")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_file = self.output_dir / "training.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _log_memory_estimates(self):
        """Log GPU memory usage estimates"""
        batch_size = self.config["data"]["batch_size"]
        seq_length = self.config["data"]["max_sequence_length"]
        
        memory_est = estimate_memory_usage(self.model, batch_size, seq_length)
        
        self.logger.info("GPU Memory Usage Estimates:")
        for key, value in memory_est.items():
            self.logger.info(f"  {key}: {value:.2f} GB")
        
        # Check if estimates fit in RTX 3070 (8GB)
        if memory_est["safety_margin"] > 8.0:
            self.logger.warning(
                f"Estimated memory usage ({memory_est['safety_margin']:.2f} GB) "
                "may exceed RTX 3070 capacity (8GB). Consider reducing batch size."
            )
    
    def _init_wandb(self):
        """Initialize Weights & Biases logging"""
        if self.config.get("wandb", {}).get("enabled", False):
            try:
                wandb.init(
                    project=self.config["wandb"].get("project", "tja-generator"),
                    name=self.config["wandb"].get("run_name", f"phase3-{int(time.time())}"),
                    config=self.config,
                    dir=str(self.output_dir)
                )
                self.logger.info("Wandb initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize wandb: {e}")
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader,
              test_loader: Optional[DataLoader] = None) -> Dict[str, Any]:
        """
        Main training loop
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            test_loader: Test data loader (optional)
            
        Returns:
            Training results and statistics
        """
        self.logger.info("Starting training...")
        
        # Calculate total steps
        gradient_accumulation_steps = self.config.get("gradient_accumulation_steps", 4)
        max_epochs = self.config.get("max_epochs", 10)
        steps_per_epoch = len(train_loader) // gradient_accumulation_steps
        total_steps = steps_per_epoch * max_epochs
        
        self.logger.info(f"Training configuration:")
        self.logger.info(f"  Epochs: {max_epochs}")
        self.logger.info(f"  Steps per epoch: {steps_per_epoch}")
        self.logger.info(f"  Total steps: {total_steps}")
        self.logger.info(f"  Batch size: {self.config.get('batch_size', 2)}")
        self.logger.info(f"  Gradient accumulation: {gradient_accumulation_steps}")
        self.logger.info(f"  Effective batch size: {self.config.get('batch_size', 2) * gradient_accumulation_steps}")
        
        # Training loop
        training_start_time = time.time()

        for epoch in range(max_epochs):
            self.epoch = epoch
            
            # Training epoch
            train_metrics = self._train_epoch(train_loader)
            
            # Validation
            val_metrics = self._validate_epoch(val_loader)
            
            # Learning rate scheduling
            if self.scheduler is not None:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_metrics["loss"])
                else:
                    self.scheduler.step()
            
            # Logging
            self._log_epoch_metrics(epoch, train_metrics, val_metrics)
            
            # Checkpointing
            is_best = val_metrics["loss"] < self.best_val_loss
            if is_best:
                self.best_val_loss = val_metrics["loss"]
                self.early_stopping_counter = 0
            else:
                self.early_stopping_counter += 1
            
            self._save_checkpoint(is_best, train_metrics, val_metrics)
            
            # Early stopping
            early_stopping_patience = self.config.get("early_stopping_patience", 10)
            if self.early_stopping_counter >= early_stopping_patience:
                self.logger.info(f"Early stopping triggered after {epoch + 1} epochs")
                break
        
        # Final evaluation
        training_time = time.time() - training_start_time
        final_results = self._final_evaluation(test_loader, training_time)
        
        self.logger.info("Training completed successfully!")
        return final_results
    
    def _train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        epoch_metrics = self.metrics.reset()
        
        progress_bar = tqdm(
            train_loader, 
            desc=f"Epoch {self.epoch + 1} [Train]",
            leave=False
        )
        
        for batch_idx, batch in enumerate(progress_bar):
            try:
                # Memory monitoring
                self.memory_monitor.monitor_training_step(self.global_step)

                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                # Forward pass with mixed precision and memory monitoring
                with self.mixed_precision.autocast():
                    with MemoryContext(self.memory_monitor, f"forward_step_{batch_idx}"):
                        outputs = self.model(
                            audio_features=batch["audio_features"],
                            target_sequence=batch["note_sequence"],
                            target_timing=batch["timing_sequence"],
                            difficulty=batch["difficulty"],
                            audio_mask=batch["audio_mask"],
                            return_loss=True
                        )

                        loss = outputs["total_loss"]

            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    self.memory_monitor.handle_oom_error(e)
                    # Skip this batch and continue
                    continue
                else:
                    raise e
            
            # Gradient accumulation
            scaled_loss = self.gradient_accumulator.accumulate_loss(loss)
            
            # Backward pass
            self.mixed_precision.backward(scaled_loss)
            
            # Update metrics
            self.metrics.update(outputs, batch)
            
            # Optimizer step
            if self.gradient_accumulator.should_update():
                # Gradient clipping
                grad_norm = self.gradient_clipper.clip_gradients(self.model)

                # Optimizer step
                self.mixed_precision.step(self.optimizer)
                self.optimizer.zero_grad()

                # Memory cleanup after optimizer step
                self.memory_monitor.cleanup_memory()

                # Update global step
                self.global_step += 1
                
                # Logging
                if self.global_step % self.config["training"]["logging_frequency"] == 0:
                    self._log_training_step(outputs, grad_norm)
                
                # Validation during training
                if self.global_step % self.config["training"]["validation_frequency"] == 0:
                    val_metrics = self._validate_step(train_loader)
                    self._log_validation_step(val_metrics)
            
            # Update progress bar
            avg_loss = self.gradient_accumulator.accumulated_loss / max(1, self.gradient_accumulator.current_step % self.gradient_accumulator.accumulation_steps)
            progress_bar.set_postfix({
                "loss": f"{avg_loss:.4f}",
                "lr": f"{self.optimizer.param_groups[0]['lr']:.2e}",
                "step": self.global_step
            })
        
        return self.metrics.compute()
    
    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """Validate for one epoch"""
        self.model.eval()
        val_metrics = self.metrics.reset()
        
        with torch.no_grad():
            progress_bar = tqdm(
                val_loader,
                desc=f"Epoch {self.epoch + 1} [Val]",
                leave=False
            )
            
            for batch in progress_bar:
                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # Forward pass
                with self.mixed_precision.autocast():
                    outputs = self.model(
                        audio_features=batch["audio_features"],
                        target_sequence=batch["note_sequence"],
                        target_timing=batch["timing_sequence"],
                        difficulty=batch["difficulty"],
                        audio_mask=batch["audio_mask"],
                        return_loss=True
                    )
                
                # Update metrics
                self.metrics.update(outputs, batch)
                
                # Update progress bar
                progress_bar.set_postfix({
                    "val_loss": f"{outputs['total_loss'].item():.4f}"
                })
        
        return self.metrics.compute()
    
    def _validate_step(self, train_loader: DataLoader) -> Dict[str, float]:
        """Quick validation step during training"""
        # This would be a subset validation for frequent checking
        # For now, return empty dict
        return {}
    
    def _log_training_step(self, outputs: Dict[str, torch.Tensor], grad_norm: float):
        """Log training step metrics"""
        metrics = {
            "train/total_loss": outputs["total_loss"].item(),
            "train/note_loss": outputs["note_loss"].item(),
            "train/timing_loss": outputs["timing_loss"].item(),
            "train/difficulty_loss": outputs["difficulty_loss"].item(),
            "train/pattern_loss": outputs["pattern_loss"].item(),
            "train/structure_loss": outputs["structure_loss"].item(),
            "train/gradient_norm": grad_norm,
            "train/learning_rate": self.optimizer.param_groups[0]["lr"],
            "global_step": self.global_step
        }
        
        # Add accuracy metrics
        if "accuracy_metrics" in outputs:
            for key, value in outputs["accuracy_metrics"].items():
                metrics[f"train/{key}"] = value
        
        # Log to wandb
        if wandb.run is not None:
            wandb.log(metrics, step=self.global_step)
    
    def _log_validation_step(self, val_metrics: Dict[str, float]):
        """Log validation step metrics"""
        if val_metrics:
            wandb_metrics = {f"val_step/{k}": v for k, v in val_metrics.items()}
            wandb_metrics["global_step"] = self.global_step
            
            if wandb.run is not None:
                wandb.log(wandb_metrics, step=self.global_step)
    
    def _log_epoch_metrics(self, epoch: int, train_metrics: Dict[str, float], 
                          val_metrics: Dict[str, float]):
        """Log epoch-level metrics"""
        self.logger.info(f"Epoch {epoch + 1} Results:")
        self.logger.info(f"  Train Loss: {train_metrics.get('loss', 0):.4f}")
        self.logger.info(f"  Val Loss: {val_metrics.get('loss', 0):.4f}")
        self.logger.info(f"  Train Accuracy: {train_metrics.get('note_accuracy', 0):.3f}")
        self.logger.info(f"  Val Accuracy: {val_metrics.get('note_accuracy', 0):.3f}")
        
        # Log to wandb
        if wandb.run is not None:
            epoch_metrics = {}
            for key, value in train_metrics.items():
                epoch_metrics[f"epoch_train/{key}"] = value
            for key, value in val_metrics.items():
                epoch_metrics[f"epoch_val/{key}"] = value
            epoch_metrics["epoch"] = epoch
            
            wandb.log(epoch_metrics, step=self.global_step)
    
    def _save_checkpoint(self, is_best: bool, train_metrics: Dict[str, float],
                        val_metrics: Dict[str, float]):
        """Save model checkpoint"""
        checkpoint_data = {
            "optimizer_state": self.optimizer.state_dict(),
            "scheduler_state": self.scheduler.state_dict() if self.scheduler else None,
            "training_stats": {
                "epoch": self.epoch,
                "global_step": self.global_step,
                "best_val_loss": self.best_val_loss,
                "train_metrics": train_metrics,
                "val_metrics": val_metrics
            }
        }
        
        # Save regular checkpoint
        checkpoint_path = self.output_dir / f"checkpoint_epoch_{self.epoch + 1}.pt"
        self.model.save_checkpoint(checkpoint_path, **checkpoint_data)
        
        # Save best model
        if is_best:
            best_path = self.output_dir / "best_model.pt"
            self.model.save_checkpoint(best_path, **checkpoint_data)
            self.logger.info(f"New best model saved: {best_path}")
    
    def _final_evaluation(self, test_loader: Optional[DataLoader], 
                         training_time: float) -> Dict[str, Any]:
        """Perform final evaluation and return results"""
        results = {
            "training_time_seconds": training_time,
            "total_epochs": self.epoch + 1,
            "total_steps": self.global_step,
            "best_val_loss": self.best_val_loss
        }
        
        # Test evaluation if test loader provided
        if test_loader is not None:
            self.logger.info("Running final test evaluation...")
            test_metrics = self._validate_epoch(test_loader)
            results["test_metrics"] = test_metrics
            
            # Generation evaluation
            generation_results = self.evaluator.evaluate_generation(
                self.model, test_loader, self.device
            )
            results["generation_evaluation"] = generation_results
        
        # Save final results
        results_path = self.output_dir / "final_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        return results
