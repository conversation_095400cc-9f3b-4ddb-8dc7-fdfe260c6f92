"""
Advanced Checkpoint Manager - Phase 5

Sophisticated checkpoint management with model versioning, automatic cleanup,
and integration with Phase 4 deployment pipeline.
"""

import torch
import torch.nn as nn
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass, asdict


@dataclass
class CheckpointMetadata:
    """Metadata for model checkpoints"""
    step: int
    epoch: int
    loss: float
    quality_score: float
    learning_rate: float
    timestamp: float
    model_config: Dict[str, Any]
    training_config: Dict[str, Any]
    phase5_config: Dict[str, Any]
    file_size_mb: float
    is_best: bool = False
    validation_metrics: Optional[Dict[str, Any]] = None


class AdvancedCheckpointManager:
    """
    Advanced checkpoint management system
    
    Features:
    - Automatic best model tracking
    - Configurable checkpoint retention
    - Model versioning and metadata
    - Integration with Phase 4 deployment
    - Automatic cleanup and storage optimization
    """
    
    def __init__(self, 
                 checkpoint_dir: str,
                 save_top_k: int = 3,
                 save_every_n_steps: int = 2000,
                 max_checkpoints: int = 10):
        """
        Initialize checkpoint manager
        
        Args:
            checkpoint_dir: Directory to save checkpoints
            save_top_k: Number of best checkpoints to keep
            save_every_n_steps: Save checkpoint every N steps
            max_checkpoints: Maximum total checkpoints to keep
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.save_top_k = save_top_k
        self.save_every_n_steps = save_every_n_steps
        self.max_checkpoints = max_checkpoints
        
        # Checkpoint tracking
        self.checkpoints = []  # List of CheckpointMetadata
        self.best_checkpoint_path = None
        self.best_quality_score = float('-inf')
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Load existing checkpoints
        self._load_existing_checkpoints()
        
        self.logger.info(f"Checkpoint manager initialized: {self.checkpoint_dir}")
        self.logger.info(f"Found {len(self.checkpoints)} existing checkpoints")
    
    def save_checkpoint(self,
                       model: nn.Module,
                       optimizer: torch.optim.Optimizer,
                       scheduler: torch.optim.lr_scheduler._LRScheduler,
                       step: int,
                       metrics: Dict[str, Any],
                       epoch: int = 0,
                       is_best: bool = False,
                       model_config: Optional[Dict[str, Any]] = None,
                       training_config: Optional[Dict[str, Any]] = None,
                       phase5_config: Optional[Dict[str, Any]] = None) -> str:
        """
        Save model checkpoint with comprehensive metadata
        
        Args:
            model: Model to save
            optimizer: Optimizer state
            scheduler: Scheduler state
            step: Training step
            metrics: Training/validation metrics
            epoch: Training epoch
            is_best: Whether this is the best checkpoint
            model_config: Model configuration
            training_config: Training configuration
            phase5_config: Phase 5 specific configuration
            
        Returns:
            Path to saved checkpoint
        """
        # Create checkpoint filename
        timestamp = int(time.time())
        checkpoint_name = f"checkpoint_step_{step}_epoch_{epoch}_{timestamp}.pt"
        checkpoint_path = self.checkpoint_dir / checkpoint_name
        
        # Prepare checkpoint data
        checkpoint_data = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "scheduler_state_dict": scheduler.state_dict(),
            "step": step,
            "epoch": epoch,
            "metrics": metrics,
            "timestamp": timestamp,
            "model_config": model_config or {},
            "training_config": training_config or {},
            "phase5_config": phase5_config or {},
            "version": "phase5_v1.0"
        }
        
        # Save checkpoint
        torch.save(checkpoint_data, checkpoint_path)
        
        # Calculate file size
        file_size_mb = checkpoint_path.stat().st_size / (1024 * 1024)
        
        # Create metadata
        metadata = CheckpointMetadata(
            step=step,
            epoch=epoch,
            loss=metrics.get("loss", float('inf')),
            quality_score=metrics.get("quality_score", 0.0),
            learning_rate=scheduler.get_last_lr()[0] if scheduler else 0.0,
            timestamp=timestamp,
            model_config=model_config or {},
            training_config=training_config or {},
            phase5_config=phase5_config or {},
            file_size_mb=file_size_mb,
            is_best=is_best,
            validation_metrics=metrics.get("validation_metrics")
        )
        
        # Update best checkpoint tracking
        if is_best or metadata.quality_score > self.best_quality_score:
            self.best_quality_score = metadata.quality_score
            self.best_checkpoint_path = checkpoint_path
            metadata.is_best = True
            
            # Create best model symlink
            best_link = self.checkpoint_dir / "best_model.pt"
            if best_link.exists():
                best_link.unlink()
            best_link.symlink_to(checkpoint_path.name)
        
        # Add to checkpoint list
        self.checkpoints.append(metadata)
        
        # Save metadata
        self._save_checkpoint_metadata(metadata, checkpoint_path)
        
        # Cleanup old checkpoints
        self._cleanup_checkpoints()
        
        self.logger.info(f"Saved checkpoint: {checkpoint_path.name} (Quality: {metadata.quality_score:.4f})")
        
        return str(checkpoint_path)
    
    def load_checkpoint(self, checkpoint_path: str) -> Optional[Dict[str, Any]]:
        """
        Load checkpoint from path
        
        Args:
            checkpoint_path: Path to checkpoint file
            
        Returns:
            Checkpoint data or None if loading fails
        """
        try:
            checkpoint_path = Path(checkpoint_path)
            if not checkpoint_path.exists():
                self.logger.error(f"Checkpoint not found: {checkpoint_path}")
                return None
            
            checkpoint_data = torch.load(checkpoint_path, map_location='cpu')
            
            self.logger.info(f"Loaded checkpoint: {checkpoint_path.name}")
            return checkpoint_data
            
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint {checkpoint_path}: {e}")
            return None
    
    def load_best_checkpoint(self) -> Optional[Dict[str, Any]]:
        """Load the best checkpoint"""
        if self.best_checkpoint_path and self.best_checkpoint_path.exists():
            return self.load_checkpoint(str(self.best_checkpoint_path))
        
        # Fallback: find best checkpoint by quality score
        if self.checkpoints:
            best_checkpoint = max(self.checkpoints, key=lambda x: x.quality_score)
            checkpoint_path = self.checkpoint_dir / f"checkpoint_step_{best_checkpoint.step}_epoch_{best_checkpoint.epoch}_{int(best_checkpoint.timestamp)}.pt"
            
            if checkpoint_path.exists():
                return self.load_checkpoint(str(checkpoint_path))
        
        self.logger.warning("No best checkpoint found")
        return None
    
    def restore_training_state(self, 
                              model: nn.Module,
                              optimizer: torch.optim.Optimizer,
                              scheduler: torch.optim.lr_scheduler._LRScheduler,
                              checkpoint_path: Optional[str] = None) -> Tuple[int, int]:
        """
        Restore training state from checkpoint
        
        Args:
            model: Model to restore
            optimizer: Optimizer to restore
            scheduler: Scheduler to restore
            checkpoint_path: Specific checkpoint path (uses best if None)
            
        Returns:
            Tuple of (step, epoch) from restored checkpoint
        """
        if checkpoint_path is None:
            checkpoint_data = self.load_best_checkpoint()
        else:
            checkpoint_data = self.load_checkpoint(checkpoint_path)
        
        if checkpoint_data is None:
            self.logger.warning("No checkpoint to restore from")
            return 0, 0
        
        try:
            # Restore model state
            model.load_state_dict(checkpoint_data["model_state_dict"])
            
            # Restore optimizer state
            optimizer.load_state_dict(checkpoint_data["optimizer_state_dict"])
            
            # Restore scheduler state
            if "scheduler_state_dict" in checkpoint_data:
                scheduler.load_state_dict(checkpoint_data["scheduler_state_dict"])
            
            step = checkpoint_data.get("step", 0)
            epoch = checkpoint_data.get("epoch", 0)
            
            self.logger.info(f"Restored training state from step {step}, epoch {epoch}")
            return step, epoch
            
        except Exception as e:
            self.logger.error(f"Failed to restore training state: {e}")
            return 0, 0
    
    def export_for_phase4_deployment(self, 
                                    output_path: str,
                                    checkpoint_path: Optional[str] = None) -> bool:
        """
        Export model for Phase 4 deployment pipeline
        
        Args:
            output_path: Path to save deployment-ready model
            checkpoint_path: Specific checkpoint to export (uses best if None)
            
        Returns:
            True if export successful
        """
        try:
            # Load checkpoint
            if checkpoint_path is None:
                checkpoint_data = self.load_best_checkpoint()
                source_path = self.best_checkpoint_path
            else:
                checkpoint_data = self.load_checkpoint(checkpoint_path)
                source_path = Path(checkpoint_path)
            
            if checkpoint_data is None:
                self.logger.error("No checkpoint available for export")
                return False
            
            # Create Phase 4 compatible model state
            phase4_model_state = {
                "model_state_dict": checkpoint_data["model_state_dict"],
                "model_config": checkpoint_data.get("model_config", {}),
                "training_config": checkpoint_data.get("training_config", {}),
                "phase5_training_info": {
                    "step": checkpoint_data.get("step", 0),
                    "epoch": checkpoint_data.get("epoch", 0),
                    "final_loss": checkpoint_data.get("metrics", {}).get("loss", 0.0),
                    "quality_score": checkpoint_data.get("metrics", {}).get("quality_score", 0.0),
                    "timestamp": checkpoint_data.get("timestamp", time.time())
                },
                "version": "phase5_to_phase4_v1.0"
            }
            
            # Save in Phase 4 format
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            torch.save(phase4_model_state, output_path)
            
            self.logger.info(f"Exported model for Phase 4 deployment: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export model for Phase 4: {e}")
            return False
    
    def _load_existing_checkpoints(self):
        """Load metadata for existing checkpoints"""
        try:
            # Find all checkpoint files
            checkpoint_files = list(self.checkpoint_dir.glob("checkpoint_*.pt"))
            
            for checkpoint_file in checkpoint_files:
                # Try to load metadata
                metadata_file = checkpoint_file.with_suffix('.json')
                
                if metadata_file.exists():
                    with open(metadata_file, 'r') as f:
                        metadata_dict = json.load(f)
                    
                    metadata = CheckpointMetadata(**metadata_dict)
                    self.checkpoints.append(metadata)
                    
                    # Update best checkpoint tracking
                    if metadata.is_best or metadata.quality_score > self.best_quality_score:
                        self.best_quality_score = metadata.quality_score
                        self.best_checkpoint_path = checkpoint_file
                else:
                    # Try to extract basic info from checkpoint file
                    try:
                        checkpoint_data = torch.load(checkpoint_file, map_location='cpu')
                        
                        metadata = CheckpointMetadata(
                            step=checkpoint_data.get("step", 0),
                            epoch=checkpoint_data.get("epoch", 0),
                            loss=checkpoint_data.get("metrics", {}).get("loss", float('inf')),
                            quality_score=checkpoint_data.get("metrics", {}).get("quality_score", 0.0),
                            learning_rate=0.0,
                            timestamp=checkpoint_data.get("timestamp", checkpoint_file.stat().st_mtime),
                            model_config=checkpoint_data.get("model_config", {}),
                            training_config=checkpoint_data.get("training_config", {}),
                            phase5_config=checkpoint_data.get("phase5_config", {}),
                            file_size_mb=checkpoint_file.stat().st_size / (1024 * 1024)
                        )
                        
                        self.checkpoints.append(metadata)
                        
                        # Save metadata for future use
                        self._save_checkpoint_metadata(metadata, checkpoint_file)
                        
                    except Exception as e:
                        self.logger.warning(f"Could not load checkpoint metadata for {checkpoint_file}: {e}")
            
            # Sort checkpoints by step
            self.checkpoints.sort(key=lambda x: x.step)
            
        except Exception as e:
            self.logger.error(f"Failed to load existing checkpoints: {e}")
    
    def _save_checkpoint_metadata(self, metadata: CheckpointMetadata, checkpoint_path: Path):
        """Save checkpoint metadata to JSON file"""
        try:
            metadata_path = checkpoint_path.with_suffix('.json')
            
            with open(metadata_path, 'w') as f:
                json.dump(asdict(metadata), f, indent=2)
                
        except Exception as e:
            self.logger.warning(f"Failed to save checkpoint metadata: {e}")
    
    def _cleanup_checkpoints(self):
        """Clean up old checkpoints based on retention policy"""
        if len(self.checkpoints) <= self.max_checkpoints:
            return
        
        # Sort checkpoints by quality score (descending)
        sorted_by_quality = sorted(self.checkpoints, key=lambda x: x.quality_score, reverse=True)
        
        # Keep top-k best checkpoints (use indices to avoid hashability issues)
        keep_best_indices = set(range(min(self.save_top_k, len(sorted_by_quality))))
        keep_best = [sorted_by_quality[i] for i in keep_best_indices]

        # Keep recent checkpoints
        sorted_by_step = sorted(self.checkpoints, key=lambda x: x.step, reverse=True)
        keep_recent_count = max(0, self.max_checkpoints - len(keep_best))
        keep_recent = sorted_by_step[:keep_recent_count]

        # Combine lists and remove duplicates
        keep_checkpoints = list({cp.step: cp for cp in (keep_best + keep_recent)}.values())
        
        # Remove checkpoints not in keep set
        checkpoints_to_remove = [cp for cp in self.checkpoints if cp not in keep_checkpoints]
        
        for checkpoint in checkpoints_to_remove:
            try:
                # Construct checkpoint path
                checkpoint_path = self.checkpoint_dir / f"checkpoint_step_{checkpoint.step}_epoch_{checkpoint.epoch}_{int(checkpoint.timestamp)}.pt"
                metadata_path = checkpoint_path.with_suffix('.json')
                
                # Remove files
                if checkpoint_path.exists():
                    checkpoint_path.unlink()
                if metadata_path.exists():
                    metadata_path.unlink()
                
                self.logger.info(f"Removed old checkpoint: {checkpoint_path.name}")
                
            except Exception as e:
                self.logger.warning(f"Failed to remove checkpoint: {e}")
        
        # Update checkpoint list
        self.checkpoints = list(keep_checkpoints)
    
    def get_checkpoint_summary(self) -> Dict[str, Any]:
        """Get summary of all checkpoints"""
        if not self.checkpoints:
            return {"total_checkpoints": 0}
        
        # Calculate statistics
        quality_scores = [cp.quality_score for cp in self.checkpoints]
        losses = [cp.loss for cp in self.checkpoints if cp.loss != float('inf')]
        file_sizes = [cp.file_size_mb for cp in self.checkpoints]
        
        summary = {
            "total_checkpoints": len(self.checkpoints),
            "best_checkpoint": {
                "step": max(self.checkpoints, key=lambda x: x.quality_score).step,
                "quality_score": max(quality_scores),
                "loss": min(losses) if losses else float('inf')
            },
            "latest_checkpoint": {
                "step": max(self.checkpoints, key=lambda x: x.step).step,
                "timestamp": max(self.checkpoints, key=lambda x: x.timestamp).timestamp
            },
            "storage_info": {
                "total_size_mb": sum(file_sizes),
                "avg_size_mb": sum(file_sizes) / len(file_sizes),
                "checkpoint_dir": str(self.checkpoint_dir)
            },
            "quality_statistics": {
                "mean_quality": sum(quality_scores) / len(quality_scores),
                "max_quality": max(quality_scores),
                "min_quality": min(quality_scores)
            }
        }
        
        return summary
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """List all checkpoints with metadata"""
        return [asdict(cp) for cp in sorted(self.checkpoints, key=lambda x: x.step, reverse=True)]
