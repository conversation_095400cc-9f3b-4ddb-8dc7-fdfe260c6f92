"""
Advanced Training Strategies - Phase 5

Curriculum learning, adaptive loss weighting, and other advanced training
strategies optimized for TJA generation and RTX 3070 hardware.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass

from .training_config import OptimizationConfig


class CurriculumLearningStrategy:
    """
    Curriculum learning strategy for progressive TJA generation training
    
    Implements progressive difficulty training starting with simple patterns
    and gradually increasing complexity to improve model convergence.
    """
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.current_stage_idx = 0
        self.stage_start_step = 0
        self.logger = logging.getLogger(__name__)
        
        # Validate curriculum stages
        if not config.curriculum_stages:
            raise ValueError("Curriculum stages must be defined")
        
        self.logger.info(f"Initialized curriculum learning with {len(config.curriculum_stages)} stages")
    
    def get_current_stage(self, global_step: int) -> Dict[str, Any]:
        """Get current curriculum stage based on training step"""
        cumulative_steps = 0
        
        for idx, stage in enumerate(self.config.curriculum_stages):
            cumulative_steps += stage["steps"]
            if global_step < cumulative_steps:
                if idx != self.current_stage_idx:
                    self.current_stage_idx = idx
                    self.stage_start_step = global_step
                    self.logger.info(f"Advanced to curriculum stage {idx}: {stage['name']}")
                
                return stage
        
        # Return final stage if beyond all stages
        final_stage = self.config.curriculum_stages[-1]
        if self.current_stage_idx != len(self.config.curriculum_stages) - 1:
            self.current_stage_idx = len(self.config.curriculum_stages) - 1
            self.logger.info(f"Reached final curriculum stage: {final_stage['name']}")
        
        return final_stage
    
    def should_update_curriculum(self, global_step: int) -> bool:
        """Check if curriculum should be updated"""
        current_stage = self.get_current_stage(global_step)
        return global_step == self.stage_start_step
    
    def get_difficulty_filter(self, global_step: int) -> List[int]:
        """Get difficulty levels to focus on for current stage"""
        current_stage = self.get_current_stage(global_step)
        return current_stage.get("difficulty_focus", [0, 1, 2])
    
    def get_max_sequence_length(self, global_step: int) -> int:
        """Get maximum sequence length for current stage"""
        current_stage = self.get_current_stage(global_step)
        return current_stage.get("max_sequence_length", 400)
    
    def get_stage_loss_weights(self, global_step: int) -> Dict[str, float]:
        """Get loss weights for current curriculum stage"""
        current_stage = self.get_current_stage(global_step)
        return current_stage.get("loss_weights", {})


class AdaptiveLossWeighting:
    """
    Adaptive loss weighting strategy that adjusts loss component weights
    based on training dynamics and relative loss magnitudes.
    """
    
    def __init__(self, initial_weights: Dict[str, float], config: OptimizationConfig):
        self.initial_weights = initial_weights.copy()
        self.current_weights = initial_weights.copy()
        self.config = config
        
        # Loss history for adaptation
        self.loss_history = {name: [] for name in initial_weights.keys()}
        self.weight_history = {name: [weight] for name, weight in initial_weights.items()}
        
        # Adaptation parameters
        self.momentum = config.loss_weight_momentum
        self.adaptation_rate = config.loss_weight_adaptation_rate
        self.update_frequency = config.adaptive_loss_update_frequency
        
        self.last_update_step = 0
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Initialized adaptive loss weighting with weights: {initial_weights}")
    
    def update_weights(self, losses: Dict[str, torch.Tensor], global_step: int):
        """Update loss weights based on current losses"""
        # Store loss values
        for name, loss_tensor in losses.items():
            if name in self.loss_history:
                self.loss_history[name].append(loss_tensor.item())
        
        # Update weights periodically
        if global_step - self.last_update_step >= self.update_frequency:
            self._adapt_weights(global_step)
            self.last_update_step = global_step
    
    def _adapt_weights(self, global_step: int):
        """Adapt loss weights based on loss history"""
        if not any(self.loss_history.values()):
            return
        
        # Calculate recent loss statistics
        window_size = min(100, len(next(iter(self.loss_history.values()))))
        if window_size < 10:
            return
        
        recent_losses = {}
        loss_ratios = {}
        
        for name, history in self.loss_history.items():
            if len(history) >= window_size:
                recent_losses[name] = np.mean(history[-window_size:])
        
        if len(recent_losses) < 2:
            return
        
        # Calculate relative loss magnitudes
        total_loss = sum(recent_losses.values())
        for name, loss_value in recent_losses.items():
            loss_ratios[name] = loss_value / total_loss if total_loss > 0 else 0.0
        
        # Adapt weights inversely to loss ratios (balance losses)
        max_ratio = max(loss_ratios.values()) if loss_ratios else 1.0
        
        for name in self.current_weights.keys():
            if name in loss_ratios:
                # Inverse relationship: higher loss gets lower weight
                target_weight = self.initial_weights[name] * (max_ratio / (loss_ratios[name] + 1e-8))
                
                # Apply momentum-based update
                current_weight = self.current_weights[name]
                new_weight = (self.momentum * current_weight + 
                             (1 - self.momentum) * target_weight)
                
                # Clamp weights to reasonable range
                new_weight = np.clip(new_weight, 0.1 * self.initial_weights[name], 
                                   2.0 * self.initial_weights[name])
                
                self.current_weights[name] = new_weight
                self.weight_history[name].append(new_weight)
        
        # Log weight updates
        if global_step % (self.update_frequency * 5) == 0:
            weight_str = ", ".join([f"{name}={weight:.3f}" for name, weight in self.current_weights.items()])
            self.logger.info(f"Updated loss weights at step {global_step}: {weight_str}")
    
    def get_current_weights(self, global_step: int) -> Dict[str, float]:
        """Get current loss weights"""
        return self.current_weights.copy()
    
    def get_weight_statistics(self) -> Dict[str, Dict[str, float]]:
        """Get statistics about weight adaptation"""
        stats = {}
        
        for name, history in self.weight_history.items():
            if len(history) > 1:
                stats[name] = {
                    "initial": history[0],
                    "current": history[-1],
                    "mean": np.mean(history),
                    "std": np.std(history),
                    "min": np.min(history),
                    "max": np.max(history)
                }
        
        return stats


class ProgressiveResizing:
    """
    Progressive resizing strategy for sequence data
    
    Note: Less applicable to TJA sequences but can be used for
    progressive sequence length training.
    """
    
    def __init__(self, min_length: int = 100, max_length: int = 400, 
                 resize_frequency: int = 5000):
        self.min_length = min_length
        self.max_length = max_length
        self.resize_frequency = resize_frequency
        self.current_length = min_length
        self.logger = logging.getLogger(__name__)
    
    def get_current_length(self, global_step: int) -> int:
        """Get current sequence length based on training progress"""
        # Linear progression from min to max length
        progress = min(1.0, global_step / (self.resize_frequency * 10))
        new_length = int(self.min_length + progress * (self.max_length - self.min_length))
        
        if new_length != self.current_length:
            self.current_length = new_length
            self.logger.info(f"Updated sequence length to {new_length} at step {global_step}")
        
        return self.current_length


class MixupStrategy:
    """
    Mixup data augmentation strategy for TJA training
    
    Applies mixup to audio features while handling discrete note sequences
    through label smoothing.
    """
    
    def __init__(self, alpha: float = 0.2, enabled: bool = True):
        self.alpha = alpha
        self.enabled = enabled
        self.logger = logging.getLogger(__name__)
    
    def apply_mixup(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Apply mixup augmentation to batch"""
        if not self.enabled or self.alpha <= 0:
            return batch
        
        batch_size = batch["audio_features"].size(0)
        if batch_size < 2:
            return batch
        
        # Sample mixing coefficient
        lam = np.random.beta(self.alpha, self.alpha) if self.alpha > 0 else 1.0
        
        # Random permutation for mixing
        indices = torch.randperm(batch_size)
        
        # Mix audio features
        mixed_audio = lam * batch["audio_features"] + (1 - lam) * batch["audio_features"][indices]
        
        # For discrete sequences, we use label smoothing instead of direct mixing
        # Create soft targets for note sequences
        note_sequence = batch["note_sequence"]
        mixed_note_targets = self._create_soft_targets(note_sequence, note_sequence[indices], lam)
        
        # Update batch
        mixed_batch = batch.copy()
        mixed_batch["audio_features"] = mixed_audio
        mixed_batch["mixed_note_targets"] = mixed_note_targets
        mixed_batch["mixup_lambda"] = lam
        mixed_batch["mixup_indices"] = indices
        
        return mixed_batch
    
    def _create_soft_targets(self, seq1: torch.Tensor, seq2: torch.Tensor, 
                           lam: float) -> torch.Tensor:
        """Create soft targets for discrete sequences"""
        batch_size, seq_len = seq1.shape
        num_classes = 8  # Number of note types
        
        # Convert to one-hot
        seq1_onehot = torch.nn.functional.one_hot(seq1, num_classes).float()
        seq2_onehot = torch.nn.functional.one_hot(seq2, num_classes).float()
        
        # Mix one-hot representations
        mixed_targets = lam * seq1_onehot + (1 - lam) * seq2_onehot
        
        return mixed_targets


class GradualUnfreezing:
    """
    Gradual unfreezing strategy for transfer learning
    
    Gradually unfreezes model layers during training to improve
    fine-tuning stability.
    """
    
    def __init__(self, model: nn.Module, unfreeze_schedule: List[Tuple[int, str]]):
        """
        Initialize gradual unfreezing
        
        Args:
            model: Model to apply unfreezing to
            unfreeze_schedule: List of (step, layer_pattern) tuples
        """
        self.model = model
        self.unfreeze_schedule = sorted(unfreeze_schedule, key=lambda x: x[0])
        self.unfrozen_layers = set()
        self.logger = logging.getLogger(__name__)
        
        # Initially freeze all parameters
        for param in self.model.parameters():
            param.requires_grad = False
    
    def update_frozen_layers(self, global_step: int):
        """Update frozen/unfrozen layers based on schedule"""
        for step, layer_pattern in self.unfreeze_schedule:
            if global_step >= step and layer_pattern not in self.unfrozen_layers:
                self._unfreeze_layers(layer_pattern)
                self.unfrozen_layers.add(layer_pattern)
    
    def _unfreeze_layers(self, layer_pattern: str):
        """Unfreeze layers matching pattern"""
        unfrozen_count = 0
        
        for name, param in self.model.named_parameters():
            if layer_pattern in name:
                param.requires_grad = True
                unfrozen_count += 1
        
        self.logger.info(f"Unfroze {unfrozen_count} parameters matching '{layer_pattern}'")


@dataclass
class TrainingStrategy:
    """Container for all training strategies"""
    curriculum_learning: Optional[CurriculumLearningStrategy] = None
    adaptive_loss_weighting: Optional[AdaptiveLossWeighting] = None
    progressive_resizing: Optional[ProgressiveResizing] = None
    mixup: Optional[MixupStrategy] = None
    gradual_unfreezing: Optional[GradualUnfreezing] = None
    
    def apply_strategies(self, batch: Dict[str, torch.Tensor], 
                        global_step: int) -> Dict[str, torch.Tensor]:
        """Apply all enabled strategies to batch"""
        processed_batch = batch
        
        # Apply mixup if enabled
        if self.mixup:
            processed_batch = self.mixup.apply_mixup(processed_batch)
        
        # Update gradual unfreezing
        if self.gradual_unfreezing:
            self.gradual_unfreezing.update_frozen_layers(global_step)
        
        return processed_batch
    
    def get_current_config(self, global_step: int) -> Dict[str, Any]:
        """Get current configuration for all strategies"""
        config = {}
        
        if self.curriculum_learning:
            config["curriculum_stage"] = self.curriculum_learning.get_current_stage(global_step)
            config["max_sequence_length"] = self.curriculum_learning.get_max_sequence_length(global_step)
        
        if self.adaptive_loss_weighting:
            config["loss_weights"] = self.adaptive_loss_weighting.get_current_weights(global_step)
        
        if self.progressive_resizing:
            config["sequence_length"] = self.progressive_resizing.get_current_length(global_step)
        
        return config
