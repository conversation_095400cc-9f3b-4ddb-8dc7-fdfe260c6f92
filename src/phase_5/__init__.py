"""
Phase 5: Model Training and Optimization

This phase implements advanced training strategies and optimization techniques
for the TJA generation model. It includes hyperparameter optimization, curriculum
learning, data augmentation, and production-ready training pipelines optimized
for RTX 3070 hardware.

Key Components:
- Advanced training strategies with curriculum learning
- Hyperparameter optimization using Optuna
- Data augmentation and regularization techniques
- Model ensemble and knowledge distillation
- Hardware-optimized training pipeline

Input: Model architecture from Phase 4
Output: Optimized trained model for Phase 6 inference
"""

from .controller import Phase5Controller
from .data_loader import DataLoader
from .metrics import TJAMetrics
from .optimizer import create_optimizer, create_scheduler
from .trainer import TJATrainer
from .training_config import Phase5TrainingConfig, OptimizationConfig, create_phase5_config, validate_hardware_compatibility
from .advanced_trainer import AdvancedTJATrainer
from .training_strategies import CurriculumLearningStrategy, AdaptiveLossWeighting
from .data_augmentation import AdvancedTJAAugmentation
from .hyperparameter_optimizer import HyperparameterOptimizer
from .training_diagnostics import TrainingDiagnostics, PerformanceProfiler
from .model_ensemble import ModelEnsemble, KnowledgeDistillation
from .checkpoint_manager import AdvancedCheckpointManager

__all__ = [
    'Phase5Controller',
    'DataLoader',
    'TJAMetrics',
    'create_optimizer',
    'create_scheduler',
    'TJATrainer',
    'Phase5TrainingConfig',
    'OptimizationConfig',
    'AdvancedTJATrainer',
    'CurriculumLearningStrategy',
    'AdaptiveLossWeighting',
    'AdvancedTJAAugmentation',
    'HyperparameterOptimizer',
    'TrainingDiagnostics',
    'PerformanceProfiler',
    'ModelEnsemble',
    'KnowledgeDistillation',
    'AdvancedCheckpointManager',
    'create_phase5_config',
    'validate_hardware_compatibility'
]

# Phase 5 configuration optimized for RTX 3070
PHASE_5_CONFIG = {
    "training": {
        "model_name": "tja_generation_v2_optimized",
        "batch_size": 2,  # Optimized for RTX 3070
        "gradient_accumulation_steps": 8,  # Effective batch size = 16
        "learning_rate": 1e-4,
        "weight_decay": 0.01,
        "max_grad_norm": 1.0,
        "warmup_steps": 1000,
        "total_training_steps": 50000,
        "lr_scheduler": "cosine_with_warmup",
        "mixed_precision": True,
        "gradient_checkpointing": True,
        "compile_model": True
    },
    "optimization": {
        "use_curriculum_learning": True,
        "use_adaptive_loss_weighting": True,
        "use_advanced_augmentation": True,
        "use_ensemble_training": False,  # Disabled by default for memory
        "use_knowledge_distillation": False,
        "hyperparameter_optimization": True
    },
    "hardware": {
        "gpu_model": "RTX 3070",
        "max_vram_gb": 6.8,  # Safety margin
        "target_gpu_utilization": 0.88,
        "target_memory_efficiency": 0.85,
        "enable_profiling": True
    },
    "validation": {
        "validation_frequency": 500,
        "checkpoint_frequency": 2000,
        "early_stopping_patience": 10,
        "quality_threshold": 0.75  # Minimum quality for production
    },
    "data_augmentation": {
        "enabled": True,
        "probability": 0.3,
        "strategies": [
            "tempo_variation",
            "pitch_shift", 
            "noise_injection",
            "time_masking",
            "frequency_masking",
            "mixup"
        ]
    }
}
