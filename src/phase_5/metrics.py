from typing import Any, Dict, Optional
"""
Metrics and Evaluation

Comprehensive metrics for TJA generation including accuracy, musical quality,
and generation evaluation metrics.
"""

import torch
import torch.nn.functional as F
import numpy as np
from collections import defaultdict
import logging


class TJAMetrics:
    """
    Comprehensive metrics tracker for TJA generation training
    """
    
    def __init__(self):
        self.reset()
        self.logger = logging.getLogger(__name__)
    
    def reset(self) -> Dict[str, float]:
        """Reset all metrics"""
        self.metrics = defaultdict(list)
        self.batch_count = 0
        return {}
    
    def update(self, outputs: Dict[str, torch.Tensor], batch: Dict[str, torch.Tensor]):
        """
        Update metrics with batch results
        
        Args:
            outputs: Model outputs including losses and predictions
            batch: Input batch data
        """
        self.batch_count += 1
        
        # Loss metrics
        if "total_loss" in outputs:
            self.metrics["loss"].append(outputs["total_loss"].item())
        
        if "note_loss" in outputs:
            self.metrics["note_loss"].append(outputs["note_loss"].item())
        
        if "timing_loss" in outputs:
            self.metrics["timing_loss"].append(outputs["timing_loss"].item())
        
        if "difficulty_loss" in outputs:
            self.metrics["difficulty_loss"].append(outputs["difficulty_loss"].item())
        
        if "pattern_loss" in outputs:
            self.metrics["pattern_loss"].append(outputs["pattern_loss"].item())
        
        if "structure_loss" in outputs:
            self.metrics["structure_loss"].append(outputs["structure_loss"].item())
        
        # Accuracy metrics
        if "accuracy_metrics" in outputs:
            for key, value in outputs["accuracy_metrics"].items():
                self.metrics[key].append(value)
        
        # Compute additional metrics
        if "logits" in outputs and "note_sequence" in batch:
            additional_metrics = self._compute_additional_metrics(
                outputs["logits"], batch["note_sequence"], batch.get("sequence_mask")
            )
            for key, value in additional_metrics.items():
                self.metrics[key].append(value)
    
    def _compute_additional_metrics(self, logits: torch.Tensor, 
                                   targets: torch.Tensor,
                                   mask: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """Compute additional metrics from logits and targets"""
        metrics = {}
        
        # Predictions
        predictions = torch.argmax(logits, dim=-1)
        
        # Per-class accuracy
        for note_type in range(8):  # 8 note types
            class_mask = (targets == note_type)
            if mask is not None:
                class_mask = class_mask & mask.bool()
            
            if class_mask.sum() > 0:
                class_correct = ((predictions == note_type) & class_mask).float().sum()
                class_total = class_mask.float().sum()
                class_accuracy = (class_correct / class_total).item()
                metrics[f"note_{note_type}_accuracy"] = class_accuracy
        
        # Top-k accuracy
        for k in [3, 5]:
            if logits.shape[-1] >= k:
                top_k_pred = torch.topk(logits, k, dim=-1)[1]
                top_k_correct = (top_k_pred == targets.unsqueeze(-1)).any(dim=-1).float()
                
                if mask is not None:
                    top_k_correct = top_k_correct * mask
                    top_k_accuracy = top_k_correct.sum() / mask.sum()
                else:
                    top_k_accuracy = top_k_correct.mean()
                
                metrics[f"top_{k}_accuracy"] = top_k_accuracy.item()
        
        # Sequence-level metrics
        if mask is not None:
            # Exact sequence match
            seq_correct = ((predictions == targets).float() * mask).sum(dim=1) == mask.sum(dim=1)
            metrics["sequence_accuracy"] = seq_correct.float().mean().item()
            
            # Partial sequence match (>90% correct)
            seq_accuracy = ((predictions == targets).float() * mask).sum(dim=1) / mask.sum(dim=1)
            partial_correct = (seq_accuracy > 0.9).float()
            metrics["partial_sequence_accuracy"] = partial_correct.mean().item()
        
        # Confidence metrics
        probs = F.softmax(logits, dim=-1)
        max_probs = torch.max(probs, dim=-1)[0]
        
        if mask is not None:
            avg_confidence = (max_probs * mask).sum() / mask.sum()
        else:
            avg_confidence = max_probs.mean()
        
        metrics["average_confidence"] = avg_confidence.item()
        
        # Entropy (uncertainty)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        if mask is not None:
            avg_entropy = (entropy * mask).sum() / mask.sum()
        else:
            avg_entropy = entropy.mean()
        
        metrics["average_entropy"] = avg_entropy.item()
        
        return metrics
    
    def compute(self) -> Dict[str, float]:
        """Compute final metrics"""
        if self.batch_count == 0:
            return {}
        
        final_metrics = {}
        
        for key, values in self.metrics.items():
            if values:
                final_metrics[key] = np.mean(values)
        
        return final_metrics
    
    def get_summary(self) -> str:
        """Get summary string of current metrics"""
        metrics = self.compute()
        
        if not metrics:
            return "No metrics available"
        
        summary_lines = [
            f"Metrics Summary ({self.batch_count} batches):",
            f"  Loss: {metrics.get('loss', 0):.4f}",
            f"  Note Accuracy: {metrics.get('note_accuracy', 0):.3f}",
            f"  Sequence Accuracy: {metrics.get('sequence_accuracy', 0):.3f}",
            f"  Average Confidence: {metrics.get('average_confidence', 0):.3f}",
            f"  Average Entropy: {metrics.get('average_entropy', 0):.3f}"
        ]
        
        return "\n".join(summary_lines)


class GenerationEvaluator:
    """
    Evaluator for generated TJA sequences
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def evaluate_generation(self, model: torch.nn.Module, 
                           data_loader: torch.utils.data.DataLoader,
                           device: torch.device) -> Dict[str, Any]:
        """
        Evaluate generation quality on a dataset
        
        Args:
            model: Trained model
            data_loader: Data loader for evaluation
            device: Device to run evaluation on
            
        Returns:
            Generation evaluation results
        """
        model.eval()
        
        generation_results = {
            "samples": [],
            "metrics": defaultdict(list)
        }
        
        num_samples = min(self.config.get("generation_samples", 10), len(data_loader))
        
        with torch.no_grad():
            for i, batch in enumerate(data_loader):
                if i >= num_samples:
                    break
                
                # Move batch to device
                batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # Generate sequences
                generated_outputs = model.generate(
                    audio_features=batch["audio_features"],
                    difficulty=batch["difficulty"][0].item(),
                    temperature=self.config.get("temperature", 1.0),
                    top_k=self.config.get("top_k", 50),
                    top_p=self.config.get("top_p", 0.9)
                )
                
                # Evaluate each sample in batch
                for j in range(batch["audio_features"].shape[0]):
                    sample_result = self._evaluate_sample(
                        generated_sequence=generated_outputs["generated_sequence"][j],
                        target_sequence=batch["note_sequence"][j],
                        difficulty=batch["difficulty"][j],
                        song_id=batch["song_ids"][j]
                    )
                    
                    generation_results["samples"].append(sample_result)
                    
                    # Accumulate metrics
                    for key, value in sample_result["metrics"].items():
                        generation_results["metrics"][key].append(value)
        
        # Compute aggregate metrics
        aggregate_metrics = {}
        for key, values in generation_results["metrics"].items():
            if values:
                aggregate_metrics[key] = {
                    "mean": np.mean(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values)
                }
        
        generation_results["aggregate_metrics"] = aggregate_metrics
        
        self.logger.info(f"Generation evaluation completed on {len(generation_results['samples'])} samples")
        
        return generation_results
    
    def _evaluate_sample(self, generated_sequence: torch.Tensor,
                        target_sequence: torch.Tensor,
                        difficulty: torch.Tensor,
                        song_id: str) -> Dict[str, Any]:
        """Evaluate a single generated sample"""
        # Convert to numpy for easier processing
        generated = generated_sequence.cpu().numpy()
        target = target_sequence.cpu().numpy()
        diff_level = difficulty.item()
        
        metrics = {}
        
        # Basic accuracy metrics
        metrics["exact_match"] = float(np.array_equal(generated, target))
        metrics["token_accuracy"] = float(np.mean(generated == target))
        
        # Length metrics
        metrics["generated_length"] = len(generated)
        metrics["target_length"] = len(target)
        metrics["length_ratio"] = len(generated) / max(1, len(target))
        
        # Note distribution analysis
        for note_type in range(8):
            gen_count = np.sum(generated == note_type)
            target_count = np.sum(target == note_type)
            
            metrics[f"note_{note_type}_count_generated"] = int(gen_count)
            metrics[f"note_{note_type}_count_target"] = int(target_count)
            
            if target_count > 0:
                metrics[f"note_{note_type}_ratio"] = gen_count / target_count
            else:
                metrics[f"note_{note_type}_ratio"] = 0.0
        
        # Difficulty appropriateness
        metrics["difficulty_appropriateness"] = self._evaluate_difficulty_appropriateness(
            generated, diff_level
        )
        
        # Pattern coherence
        metrics["pattern_coherence"] = self._evaluate_pattern_coherence(generated)
        
        # Musical structure
        metrics["musical_structure"] = self._evaluate_musical_structure(generated)
        
        # Diversity metrics
        metrics["unique_notes"] = len(np.unique(generated))
        metrics["note_diversity"] = len(np.unique(generated)) / 8.0  # Normalized by max note types
        
        return {
            "song_id": song_id,
            "difficulty": diff_level,
            "generated_sequence": generated.tolist(),
            "target_sequence": target.tolist(),
            "metrics": metrics
        }
    
    def _evaluate_difficulty_appropriateness(self, sequence: np.ndarray, 
                                           difficulty: int) -> float:
        """Evaluate if generated sequence matches expected difficulty"""
        # Simple difficulty assessment based on note density and complexity
        non_blank_ratio = np.mean(sequence != 0)
        
        # Expected complexity ranges for each difficulty (0=Oni8, 1=Oni9, 2=Oni10)
        expected_ranges = {
            0: (0.3, 0.6),  # Oni 8: 30-60% notes
            1: (0.5, 0.8),  # Oni 9: 50-80% notes
            2: (0.7, 1.0)   # Oni 10: 70-100% notes
        }
        
        expected_min, expected_max = expected_ranges.get(difficulty, (0.3, 1.0))
        
        if expected_min <= non_blank_ratio <= expected_max:
            return 1.0
        else:
            # Score based on distance from expected range
            if non_blank_ratio < expected_min:
                return non_blank_ratio / expected_min
            else:
                return expected_max / non_blank_ratio
    
    def _evaluate_pattern_coherence(self, sequence: np.ndarray) -> float:
        """Evaluate pattern coherence in generated sequence"""
        if len(sequence) < 4:
            return 0.0
        
        # Look for repeating patterns
        pattern_scores = []
        
        for pattern_length in [2, 4, 8]:
            if len(sequence) >= pattern_length * 2:
                patterns = []
                for i in range(len(sequence) - pattern_length + 1):
                    pattern = tuple(sequence[i:i + pattern_length])
                    patterns.append(pattern)
                
                # Count pattern repetitions
                pattern_counts = {}
                for pattern in patterns:
                    pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
                
                # Score based on pattern repetition
                if patterns:
                    max_repetitions = max(pattern_counts.values())
                    repetition_ratio = max_repetitions / len(patterns)
                    pattern_scores.append(repetition_ratio)
        
        return np.mean(pattern_scores) if pattern_scores else 0.0
    
    def _evaluate_musical_structure(self, sequence: np.ndarray) -> float:
        """Evaluate musical structure in generated sequence"""
        if len(sequence) < 16:
            return 0.0
        
        # Simple structure evaluation based on regularity
        # Look for regular patterns every 4, 8, or 16 beats
        structure_scores = []
        
        for period in [4, 8, 16]:
            if len(sequence) >= period * 2:
                # Check for regularity at this period
                similarities = []
                
                for offset in range(period):
                    positions = list(range(offset, len(sequence), period))
                    if len(positions) >= 2:
                        values = sequence[positions]
                        # Measure consistency
                        if len(values) > 1:
                            consistency = 1.0 - (np.std(values) / (np.mean(values) + 1e-8))
                            similarities.append(max(0, consistency))
                
                if similarities:
                    structure_scores.append(np.mean(similarities))
        
        return np.mean(structure_scores) if structure_scores else 0.0


class MusicalQualityEvaluator:
    """
    Evaluator for musical quality aspects of generated TJA sequences
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def evaluate_musical_quality(self, sequence: np.ndarray, 
                                audio_features: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Evaluate musical quality of generated sequence
        
        Args:
            sequence: Generated note sequence
            audio_features: Corresponding audio features (optional)
            
        Returns:
            Musical quality metrics
        """
        metrics = {}
        
        # Rhythm consistency
        metrics["rhythm_consistency"] = self._evaluate_rhythm_consistency(sequence)
        
        # Note variety
        metrics["note_variety"] = self._evaluate_note_variety(sequence)
        
        # Flow and transitions
        metrics["transition_smoothness"] = self._evaluate_transitions(sequence)
        
        # Intensity progression
        metrics["intensity_progression"] = self._evaluate_intensity(sequence)
        
        # Audio-sequence alignment (if audio features provided)
        if audio_features is not None:
            metrics["audio_alignment"] = self._evaluate_audio_alignment(sequence, audio_features)
        
        return metrics
    
    def _evaluate_rhythm_consistency(self, sequence: np.ndarray) -> float:
        """Evaluate rhythm consistency"""
        # Simplified rhythm evaluation
        # Look for consistent spacing between notes
        note_positions = np.where(sequence != 0)[0]
        
        if len(note_positions) < 3:
            return 0.0
        
        intervals = np.diff(note_positions)
        
        # Measure consistency of intervals
        if len(intervals) > 1:
            interval_consistency = 1.0 - (np.std(intervals) / (np.mean(intervals) + 1e-8))
            return max(0.0, min(1.0, interval_consistency))
        
        return 0.5
    
    def _evaluate_note_variety(self, sequence: np.ndarray) -> float:
        """Evaluate note type variety"""
        unique_notes = len(np.unique(sequence[sequence != 0]))
        max_possible = 7  # Excluding blank (0)
        
        return unique_notes / max_possible
    
    def _evaluate_transitions(self, sequence: np.ndarray) -> float:
        """Evaluate smoothness of note transitions"""
        # Look for abrupt changes or good flow
        transitions = []
        
        for i in range(len(sequence) - 1):
            curr_note = sequence[i]
            next_note = sequence[i + 1]
            
            # Define transition smoothness rules
            if curr_note == 0 and next_note == 0:
                transitions.append(1.0)  # Silence to silence is smooth
            elif curr_note == 0 and next_note != 0:
                transitions.append(0.8)  # Silence to note is good
            elif curr_note != 0 and next_note == 0:
                transitions.append(0.8)  # Note to silence is good
            elif abs(curr_note - next_note) <= 1:
                transitions.append(1.0)  # Similar notes are smooth
            else:
                transitions.append(0.6)  # Different notes are less smooth
        
        return np.mean(transitions) if transitions else 0.0
    
    def _evaluate_intensity(self, sequence: np.ndarray) -> float:
        """Evaluate intensity progression"""
        # Simple intensity based on note density in windows
        window_size = 16
        intensities = []
        
        for i in range(0, len(sequence), window_size):
            window = sequence[i:i + window_size]
            intensity = np.mean(window != 0)
            intensities.append(intensity)
        
        if len(intensities) < 2:
            return 0.5
        
        # Look for good intensity variation (not too flat, not too chaotic)
        intensity_var = np.var(intensities)
        
        # Optimal variance is around 0.1-0.3
        if 0.1 <= intensity_var <= 0.3:
            return 1.0
        elif intensity_var < 0.1:
            return 0.5  # Too flat
        else:
            return max(0.0, 1.0 - (intensity_var - 0.3) / 0.7)  # Too chaotic
    
    def _evaluate_audio_alignment(self, sequence: np.ndarray, 
                                 audio_features: np.ndarray) -> float:
        """Evaluate alignment between sequence and audio features"""
        # This would require more sophisticated analysis
        # For now, return a placeholder
        return 0.5
