"""
Base Phase Controller

Provides standardized interface and common functionality for all phase controllers
implementing SOLID principles and enterprise-grade error handling.
"""

import abc
import time
import argparse
from pathlib import Path
from typing import Any, Dict, Optional

from src.shared.utils.base_classes import BaseProcessor, ProcessingResult
from src.shared.config.unified_config_manager import UnifiedConfigManager
from src.shared.utils.resource_manager import ResourceManager
from src.shared.utils.memory_monitor import MemoryMonitor


class BasePhaseController(BaseProcessor):
    """
    Abstract base class for all phase controllers
    
    Implements consistent interface, resource management, and error handling
    patterns across all phases of the TJA generation system.
    """
    
    def __init__(self, 
                 phase_number: int,
                 phase_name: str,
                 config_manager: Optional[UnifiedConfigManager] = None,
                 resource_manager: Optional[ResourceManager] = None):
        super().__init__(logger_name=f"Phase{phase_number}Controller")
        
        self.phase_number = phase_number
        self.phase_name = phase_name
        
        # Initialize managers
        self.config_manager = config_manager or UnifiedConfigManager()
        self.resource_manager = resource_manager or ResourceManager()
        self.memory_monitor = MemoryMonitor()
        
        # Get phase-specific configuration
        self.phase_config = self.config_manager.get_phase_config(phase_number)
        
        self.logger.info(f"Initialized {phase_name} controller")
    
    def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Process phase execution according to BaseProcessor interface
        
        Args:
            input_data: Phase execution parameters
            
        Returns:
            ProcessingResult: Standardized result structure
        """
        start_time = time.time()
        
        try:
            # Execute phase-specific logic
            success = self._execute_phase(input_data)
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=success,
                data={"phase": self.phase_number, "name": self.phase_name},
                processing_time_seconds=processing_time,
                memory_usage_mb=self.memory_monitor.get_current_usage()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Phase {self.phase_number} execution failed: {e}")
            
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time_seconds=processing_time,
                memory_usage_mb=self.memory_monitor.get_current_usage()
            )
    
    def execute(self, args: argparse.Namespace) -> bool:
        """
        Execute phase with command line arguments
        
        Args:
            args: Command line arguments
            
        Returns:
            bool: Success status
        """
        try:
            # Validate prerequisites
            if not self._validate_prerequisites(args):
                self.logger.error(f"Phase {self.phase_number} prerequisites not met")
                return False
            
            # Prepare execution parameters
            execution_params = self._prepare_execution_params(args)
            
            # Execute phase
            result = self.process(execution_params)
            
            if result.success:
                self.logger.info(f"Phase {self.phase_number} completed successfully")
                self._post_execution_cleanup(args)
                return True
            else:
                self.logger.error(f"Phase {self.phase_number} failed: {result.error_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Phase {self.phase_number} execution error: {e}")
            return False
    
    @abc.abstractmethod
    def _execute_phase(self, params: Dict[str, Any]) -> bool:
        """Execute phase-specific logic"""
        pass
    
    @abc.abstractmethod
    def _validate_prerequisites(self, args: argparse.Namespace) -> bool:
        """Validate phase prerequisites"""
        pass
    
    @abc.abstractmethod
    def _prepare_execution_params(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Prepare execution parameters from command line arguments"""
        pass
    
    def _post_execution_cleanup(self, args: argparse.Namespace):
        """Perform post-execution cleanup (optional override)"""
        self.resource_manager.cleanup_memory()
        self.logger.debug(f"Phase {self.phase_number} cleanup completed")
    
    def _display_phase_info(self):
        """Display phase information"""
        print(f"\n🎯 Phase {self.phase_number}: {self.phase_name}")
        print(f"🔧 Hardware-optimized for RTX 3070")
        print(f"📅 {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def _validate_input_directory(self, directory_path: str, required_files: Optional[list] = None) -> bool:
        """Validate input directory exists and contains required files"""
        path = Path(directory_path)
        
        if not path.exists():
            self.logger.error(f"Input directory not found: {directory_path}")
            return False
        
        if not path.is_dir():
            self.logger.error(f"Path is not a directory: {directory_path}")
            return False
        
        if required_files:
            for required_file in required_files:
                if not (path / required_file).exists():
                    self.logger.error(f"Required file not found: {path / required_file}")
                    return False
        
        return True
    
    def _validate_output_directory(self, directory_path: str, create_if_missing: bool = True) -> bool:
        """Validate output directory and optionally create if missing"""
        path = Path(directory_path)
        
        if not path.exists():
            if create_if_missing:
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    self.logger.info(f"Created output directory: {directory_path}")
                    return True
                except Exception as e:
                    self.logger.error(f"Failed to create output directory {directory_path}: {e}")
                    return False
            else:
                self.logger.error(f"Output directory not found: {directory_path}")
                return False
        
        if not path.is_dir():
            self.logger.error(f"Output path is not a directory: {directory_path}")
            return False
        
        return True
    
    def get_phase_status(self) -> Dict[str, Any]:
        """Get current phase status and statistics"""
        return {
            "phase_number": self.phase_number,
            "phase_name": self.phase_name,
            "statistics": self.get_statistics(),
            "memory_usage_mb": self.memory_monitor.get_current_usage(),
            "resource_status": self.resource_manager.get_current_status()
        }
    
    def _validate_hardware_environment(self) -> bool:
        """Validate hardware environment meets requirements"""
        try:
            from src.shared.utils.hardware_monitor import get_system_info
            
            system_info = get_system_info()
            
            # Check minimum requirements
            if system_info['memory']['total_gb'] < 16:
                self.logger.warning(f"Low memory: {system_info['memory']['total_gb']:.1f}GB < 16GB recommended")
            
            if system_info['cpu']['logical_cores'] < 8:
                self.logger.warning(f"Low CPU cores: {system_info['cpu']['logical_cores']} < 8 recommended")
            
            self.logger.info(f"Hardware validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Hardware validation failed: {e}")
            return False
