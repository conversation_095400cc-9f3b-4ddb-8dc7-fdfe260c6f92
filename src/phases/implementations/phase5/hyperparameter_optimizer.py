"""
Hyperparameter Optimization - Phase 5

Advanced hyperparameter optimization using Optuna for automated
hyperparameter tuning optimized for RTX 3070 hardware constraints.
"""

import torch
from torch.utils.data import DataLoader
import time
import logging
import json
from pathlib import Path
import numpy as np

# Optional imports
try:
    import optuna
    HAS_OPTUNA = True
except ImportError:
    HAS_OPTUNA = False
    # Mock optuna for when it's not available
    class MockOptuna:
        class Trial:
            def suggest_float(self, name, low, high, log=False): return (low + high) / 2
            def suggest_int(self, name, low, high): return (low + high) // 2
            def suggest_categorical(self, name, choices): return choices[0]

        class Study:
            def optimize(self, objective, n_trials=None, timeout=None, callbacks=None): pass
            @property
            def best_trial(self):
                trial = self.Trial()
                trial.params = {}
                trial.value = 0.0
                trial.number = 0
                return trial
            @property
            def trials(self): return []

        def create_study(self, **kwargs): return self.Study()

        class samplers:
            @staticmethod
            def TPESampler(**kwargs): return None

        class pruners:
            @staticmethod
            def MedianPruner(**kwargs): return None

        class trial:
            class TrialState:
                COMPLETE = "COMPLETE"
                PRUNED = "PRUNED"
                FAIL = "FAIL"

        class TrialPruned(Exception): pass

    optuna = MockOptuna()

from .training_config import Phase5TrainingConfig, OptimizationConfig
from .advanced_trainer import AdvancedTJATrainer
from ..model.tja_generator import TJAGeneratorModel
from ..utils.memory_monitor import MemoryMonitor


class HyperparameterOptimizer:
    """
    Hyperparameter optimizer using Optuna for TJA generation model
    
    Optimizes training hyperparameters while respecting RTX 3070 memory constraints
    and focusing on TJA generation quality metrics.
    """
    
    def __init__(self, 
                 base_config: Phase5TrainingConfig,
                 optimization_config: OptimizationConfig,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 output_dir: str = "outputs/phase5_hyperopt"):
        """
        Initialize hyperparameter optimizer
        
        Args:
            base_config: Base training configuration
            optimization_config: Optimization configuration
            train_loader: Training data loader
            val_loader: Validation data loader
            output_dir: Output directory for optimization results
        """
        self.base_config = base_config
        self.optimization_config = optimization_config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Memory monitoring
        self.memory_monitor = MemoryMonitor()
        
        # Optimization results
        self.best_params = None
        self.best_score = float('-inf')
        self.optimization_history = []
        
        self.logger.info("Hyperparameter optimizer initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for hyperparameter optimization"""
        logger = logging.getLogger("hyperparameter_optimizer")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_file = self.output_dir / "hyperparameter_optimization.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def optimize(self, n_trials: int = None, timeout: int = None) -> Dict[str, Any]:
        """
        Run hyperparameter optimization
        
        Args:
            n_trials: Number of optimization trials (overrides config)
            timeout: Timeout in seconds (overrides config)
            
        Returns:
            Dictionary with optimization results
        """
        n_trials = n_trials or self.optimization_config.optimization_trials
        timeout = timeout or (self.optimization_config.optimization_timeout_hours * 3600)
        
        self.logger.info(f"Starting hyperparameter optimization with {n_trials} trials, {timeout}s timeout")
        
        # Create Optuna study
        study = optuna.create_study(
            direction='maximize',  # Maximize validation quality score
            study_name=f"tja_generation_hyperopt_{int(time.time())}",
            storage=f"sqlite:///{self.output_dir}/optuna_study.db",
            load_if_exists=True
        )
        
        # Add custom sampler for better exploration
        study.sampler = optuna.samplers.TPESampler(
            n_startup_trials=10,
            n_ei_candidates=24,
            multivariate=True,
            warn_independent_sampling=False
        )
        
        # Add pruner for early stopping of unpromising trials
        study.pruner = optuna.pruners.MedianPruner(
            n_startup_trials=5,
            n_warmup_steps=1000,
            interval_steps=500
        )
        
        try:
            # Run optimization
            study.optimize(
                self._objective,
                n_trials=n_trials,
                timeout=timeout,
                callbacks=[self._trial_callback]
            )
            
            # Get best results
            best_trial = study.best_trial
            self.best_params = best_trial.params
            self.best_score = best_trial.value
            
            # Save results
            optimization_results = {
                "best_params": self.best_params,
                "best_score": self.best_score,
                "n_trials": len(study.trials),
                "optimization_history": self.optimization_history,
                "study_summary": {
                    "best_trial_number": best_trial.number,
                    "best_value": best_trial.value,
                    "best_params": best_trial.params,
                    "n_completed_trials": len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
                }
            }
            
            # Save to file
            results_file = self.output_dir / "optimization_results.json"
            with open(results_file, 'w') as f:
                json.dump(optimization_results, f, indent=2)
            
            self.logger.info(f"Optimization completed. Best score: {self.best_score:.4f}")
            self.logger.info(f"Best parameters: {self.best_params}")
            
            return optimization_results
            
        except Exception as e:
            self.logger.error(f"Hyperparameter optimization failed: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            return {
                "success": False,
                "error": str(e),
                "n_trials_completed": len(study.trials) if 'study' in locals() else 0
            }
    
    def _objective(self, trial: optuna.Trial) -> float:
        """
        Objective function for hyperparameter optimization
        
        Args:
            trial: Optuna trial object
            
        Returns:
            Objective value (validation quality score)
        """
        try:
            # Sample hyperparameters
            params = self._sample_hyperparameters(trial)
            
            # Create configuration with sampled parameters
            config = self._create_config_from_params(params)
            
            # Validate memory constraints
            if not self._validate_memory_constraints(config):
                self.logger.warning(f"Trial {trial.number}: Memory constraints violated")
                raise optuna.TrialPruned()
            
            # Create model
            model = TJAGeneratorModel(config.get_model_config())
            
            # Create trainer
            trainer = AdvancedTJATrainer(
                model=model,
                config=config,
                optimization_config=self.optimization_config,
                train_loader=self.train_loader,
                val_loader=self.val_loader,
                output_dir=self.output_dir / f"trial_{trial.number}"
            )
            
            # Run short training for evaluation
            training_results = self._run_trial_training(trainer, trial)
            
            if not training_results["success"]:
                self.logger.warning(f"Trial {trial.number}: Training failed")
                raise optuna.TrialPruned()
            
            # Get objective value (validation quality score)
            objective_value = training_results.get("best_quality_score", 0.0)
            
            # Log trial results
            self.logger.info(f"Trial {trial.number}: Score={objective_value:.4f}, Params={params}")
            
            # Store in history
            self.optimization_history.append({
                "trial_number": trial.number,
                "params": params,
                "score": objective_value,
                "training_time": training_results.get("total_training_time", 0.0)
            })
            
            return objective_value
            
        except optuna.TrialPruned:
            raise
        except Exception as e:
            self.logger.error(f"Trial {trial.number} failed: {e}")
            raise optuna.TrialPruned()
    
    def _sample_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Sample hyperparameters for trial"""
        params = {
            # Learning rate
            "learning_rate": trial.suggest_float("learning_rate", 1e-5, 1e-3, log=True),
            
            # Batch size (constrained by RTX 3070 memory)
            "batch_size": trial.suggest_categorical("batch_size", [1, 2, 4]),
            
            # Gradient accumulation
            "gradient_accumulation_steps": trial.suggest_categorical("gradient_accumulation_steps", [4, 8, 16]),
            
            # Weight decay
            "weight_decay": trial.suggest_float("weight_decay", 1e-4, 1e-1, log=True),
            
            # Dropout rate
            "dropout_rate": trial.suggest_float("dropout_rate", 0.05, 0.3),
            
            # Label smoothing
            "label_smoothing": trial.suggest_float("label_smoothing", 0.0, 0.2),
            
            # Warmup steps
            "warmup_steps": trial.suggest_int("warmup_steps", 500, 2000),
            
            # Loss weights
            "loss_weight_note_type": trial.suggest_float("loss_weight_note_type", 0.5, 2.0),
            "loss_weight_timing": trial.suggest_float("loss_weight_timing", 0.3, 1.5),
            "loss_weight_pattern": trial.suggest_float("loss_weight_pattern", 0.2, 1.0),
            
            # Augmentation probability
            "augmentation_probability": trial.suggest_float("augmentation_probability", 0.1, 0.5),
            
            # Mixup alpha
            "mixup_alpha": trial.suggest_float("mixup_alpha", 0.1, 0.4),
            
            # Scheduler parameters
            "min_lr_ratio": trial.suggest_float("min_lr_ratio", 0.01, 0.2),
        }
        
        return params
    
    def _create_config_from_params(self, params: Dict[str, Any]) -> Phase5TrainingConfig:
        """Create training configuration from sampled parameters"""
        config = Phase5TrainingConfig(
            experiment_name=f"hyperopt_trial",
            batch_size=params["batch_size"],
            gradient_accumulation_steps=params["gradient_accumulation_steps"],
            learning_rate=params["learning_rate"],
            weight_decay=params["weight_decay"],
            dropout_rate=params["dropout_rate"],
            label_smoothing=params["label_smoothing"],
            warmup_steps=params["warmup_steps"],
            min_lr_ratio=params["min_lr_ratio"],
            augmentation_probability=params["augmentation_probability"],
            mixup_alpha=params["mixup_alpha"],
            
            # Reduced training steps for hyperparameter optimization
            total_training_steps=5000,  # Shorter training for faster evaluation
            validation_frequency=500,
            checkpoint_frequency=1000,
            early_stopping_patience=5,
            
            # Loss weights
            loss_weights={
                'note_type': params["loss_weight_note_type"],
                'timing': params["loss_weight_timing"],
                'pattern': params["loss_weight_pattern"],
                'difficulty': 0.4,
                'density': 0.3
            }
        )
        
        return config
    
    def _validate_memory_constraints(self, config: Phase5TrainingConfig) -> bool:
        """Validate that configuration respects memory constraints"""
        # Estimate memory usage
        effective_batch_size = config.get_effective_batch_size()
        
        # Simple heuristic: larger batch sizes need more memory
        if effective_batch_size > 16:  # Conservative limit for RTX 3070
            return False
        
        # Check if batch size is too large for single GPU
        if config.batch_size > 4:
            return False
        
        return True
    
    def _run_trial_training(self, trainer: AdvancedTJATrainer, trial: optuna.Trial) -> Dict[str, Any]:
        """Run training for a single trial with pruning"""
        try:
            # Monitor memory before training
            initial_memory = self.memory_monitor.get_memory_stats()
            
            # Run training with intermediate reporting for pruning
            training_results = trainer.train()
            
            # Check memory usage
            final_memory = self.memory_monitor.get_memory_stats()
            
            # Add memory usage to results
            training_results["memory_usage"] = {
                "initial_gpu_gb": initial_memory.gpu_reserved_gb,
                "final_gpu_gb": final_memory.gpu_reserved_gb,
                "peak_gpu_gb": final_memory.gpu_reserved_gb  # Simplified
            }
            
            return training_results
            
        except Exception as e:
            self.logger.error(f"Trial training failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _trial_callback(self, study: optuna.Study, trial: optuna.Trial):
        """Callback function called after each trial"""
        if trial.state == optuna.trial.TrialState.COMPLETE:
            self.logger.info(f"Trial {trial.number} completed with value {trial.value:.4f}")
            
            # Update best parameters if this trial is better
            if trial.value > self.best_score:
                self.best_score = trial.value
                self.best_params = trial.params
                self.logger.info(f"New best score: {self.best_score:.4f}")
        
        elif trial.state == optuna.trial.TrialState.PRUNED:
            self.logger.info(f"Trial {trial.number} was pruned")
        
        elif trial.state == optuna.trial.TrialState.FAIL:
            self.logger.warning(f"Trial {trial.number} failed")
    
    def get_best_config(self) -> Optional[Phase5TrainingConfig]:
        """Get best configuration from optimization"""
        if self.best_params is None:
            return None
        
        return self._create_config_from_params(self.best_params)
    
    def create_optimization_report(self) -> str:
        """Create comprehensive optimization report"""
        if not self.optimization_history:
            return "No optimization history available"
        
        report_lines = [
            "# Hyperparameter Optimization Report",
            f"## Summary",
            f"- Total trials: {len(self.optimization_history)}",
            f"- Best score: {self.best_score:.4f}",
            f"- Best parameters: {self.best_params}",
            "",
            "## Trial History"
        ]
        
        # Sort trials by score
        sorted_trials = sorted(self.optimization_history, key=lambda x: x["score"], reverse=True)
        
        for i, trial in enumerate(sorted_trials[:10]):  # Top 10 trials
            report_lines.extend([
                f"### Trial {trial['trial_number']} (Rank {i+1})",
                f"- Score: {trial['score']:.4f}",
                f"- Training time: {trial['training_time']:.2f}s",
                f"- Parameters:",
            ])
            
            for param, value in trial["params"].items():
                report_lines.append(f"  - {param}: {value}")
            
            report_lines.append("")
        
        # Parameter importance analysis
        if len(self.optimization_history) >= 10:
            report_lines.extend([
                "## Parameter Analysis",
                "Top parameters by impact on performance:",
            ])
            
            # Simple correlation analysis
            param_correlations = self._analyze_parameter_importance()
            for param, correlation in param_correlations.items():
                report_lines.append(f"- {param}: {correlation:.3f} correlation with score")
        
        report_content = "\n".join(report_lines)
        
        # Save report
        report_file = self.output_dir / "optimization_report.md"
        with open(report_file, 'w') as f:
            f.write(report_content)
        
        return report_content
    
    def _analyze_parameter_importance(self) -> Dict[str, float]:
        """Analyze parameter importance using correlation"""
        if len(self.optimization_history) < 10:
            return {}
        
        # Extract parameter values and scores
        param_names = list(self.optimization_history[0]["params"].keys())
        param_correlations = {}
        
        scores = [trial["score"] for trial in self.optimization_history]
        
        for param_name in param_names:
            param_values = [trial["params"][param_name] for trial in self.optimization_history]
            
            # Convert categorical to numeric if needed
            if isinstance(param_values[0], str):
                continue
            
            # Calculate correlation
            correlation = np.corrcoef(param_values, scores)[0, 1]
            if not np.isnan(correlation):
                param_correlations[param_name] = abs(correlation)
        
        # Sort by absolute correlation
        return dict(sorted(param_correlations.items(), key=lambda x: x[1], reverse=True))


def run_hyperparameter_optimization(
    base_config: Phase5TrainingConfig,
    optimization_config: OptimizationConfig,
    train_loader: DataLoader,
    val_loader: DataLoader,
    n_trials: int = 50,
    timeout_hours: int = 24
) -> Dict[str, Any]:
    """
    Convenience function to run hyperparameter optimization
    
    Args:
        base_config: Base training configuration
        optimization_config: Optimization configuration
        train_loader: Training data loader
        val_loader: Validation data loader
        n_trials: Number of optimization trials
        timeout_hours: Timeout in hours
        
    Returns:
        Optimization results
    """
    optimizer = HyperparameterOptimizer(
        base_config=base_config,
        optimization_config=optimization_config,
        train_loader=train_loader,
        val_loader=val_loader
    )
    
    results = optimizer.optimize(
        n_trials=n_trials,
        timeout=timeout_hours * 3600
    )
    
    # Generate report
    report = optimizer.create_optimization_report()
    results["optimization_report"] = report
    
    return results
