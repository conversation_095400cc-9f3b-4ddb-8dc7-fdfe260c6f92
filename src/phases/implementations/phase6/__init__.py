"""
Phase 6: Inference Pipeline and Validation

from .phase6 import (
    Complete inference pipeline for TJA chart generation with comprehensive validation,
performance benchmarking, and production deployment capabilities.

Transforms the trained model from Phase 5 into a production-ready system with
end-to-end workflows for generating high-quality TJA charts from audio input.
"""

from .inference_system import TJAInferenceSystem
from .audio_preprocessing import AudioPreprocessor, SpectralFeatureExtractor, RhythmicFeatureExtractor, TemporalFeatureExtractor
from .tja_postprocessing import TJAPostProcessor, TJAFormatValidator
from .validation_framework import TJAValidator, MusicalCoherenceValidator, DifficultyValidator, TimingValidator
from .performance_benchmark import PerformanceBenchmark, InferencePerformanceMonitor
from .config import Phase6Config, create_phase6_config, validate_environment

__all__ = [
    # Core inference system
    'TJAInferenceSystem',

    # Audio preprocessing
    'AudioPreprocessor',
    'SpectralFeatureExtractor',
    'RhythmicFeatureExtractor',
    'TemporalFeatureExtractor',

    # TJA post-processing
    'TJAPostProcessor',
    'TJAFormatValidator',

    # Validation framework
    'TJAValidator',
    'MusicalCoherenceValidator',
    'DifficultyValidator',
    'TimingValidator',

    # Performance and benchmarking
    'PerformanceBenchmark',
    'InferencePerformanceMonitor',

    # Configuration
    'Phase6Config',
    'create_phase6_config',
    'validate_environment'
]

# Version information
__version__ = "1.0.0"
__phase__ = "Phase 6: Inference Pipeline and Validation"
__description__ = "Production-ready TJA generation inference system with comprehensive validation"

# Hardware optimization targets for RTX 3070
HARDWARE_OPTIMIZATION_TARGETS = {
    "gpu_inference_utilization": {
    "sustained_gpu_utilization": 0.70,
    "vram_efficiency": 0.95,
        "max_gpu_memory_gb": 4.0
    },
    "cpu_memory_efficiency": {
    "memory_utilization": 0.85,
    "max_ram_usage_gb": 16,
        "cache_hit_ratio": 0.95
    },
    "inference_pipeline_efficiency": {
    "pipeline_utilization": 0.88,
        "end_to_end_efficiency": 0.82
    },
    "quality_metrics": {
    "chart_quality_score": 0.80,
    "human_similarity": 0.75,
        "playability_score": 0.85
    }
}
