"""
Optimized TJA Generation Pipeline

Enhanced pipeline with dynamic resource allocation and aggressive GPU utilization
for maximum performance on RTX 3070 hardware.
"""

import torch
import time
import numpy as np

from .pipeline import TJ<PERSON>eneration<PERSON><PERSON>eline, TJAGenerationError
from ..utils.memory_monitor import MemoryMonitor, MemoryContext


class OptimizedTJAGenerationPipeline(TJAGenerationPipeline):
    """
    Optimized TJA generation pipeline with dynamic resource allocation
    
    Enhancements:
    - Dynamic batch size optimization based on available VRAM
    - Mixed precision inference for memory efficiency
    - Parallel sequence generation for multiple difficulties
    - Aggressive memory utilization with safety margins
    - Adaptive sequence length based on audio duration
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        
        # Optimization settings
        self.optimization_config = self.config["phase3_integration"]["memory_optimization"]
        self.use_mixed_precision = self.optimization_config.get("mixed_precision", True)
        self.use_dynamic_batching = self.optimization_config.get("dynamic_batching", True)
        self.max_gpu_memory_fraction = self.optimization_config.get("max_gpu_memory_fraction", 0.75)
        
        # Performance tracking
        self.performance_stats = {
            "optimal_batch_size": 1,
            "peak_memory_usage_gb": 0.0,
            "average_generation_time": 0.0,
            "throughput_sequences_per_second": 0.0
        }
        
        self.logger.info("Optimized TJA Generation Pipeline initialized")
    
    def initialize(self) -> bool:
        """Initialize with optimization settings"""
        if not super().initialize():
            return False
        
        # Enable mixed precision if requested
        if self.use_mixed_precision and torch.cuda.is_available():
            self.scaler = torch.cuda.amp.GradScaler()
            self.logger.info("Mixed precision inference enabled")
        else:
            self.scaler = None
        
        # Determine optimal batch size
        if self.use_dynamic_batching:
            self._optimize_batch_size()
        
        # Pre-allocate memory pools for efficiency
        self._preallocate_memory_pools()
        
        return True
    
    def _optimize_batch_size(self):
        """Dynamically determine optimal batch size based on available VRAM"""
        self.logger.info("Optimizing batch size for available VRAM...")
        
        if not torch.cuda.is_available():
            self.performance_stats["optimal_batch_size"] = 1
            return
        
        # Get available GPU memory
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        gpu_memory_available = gpu_memory_total * self.max_gpu_memory_fraction
        
        self.logger.info(f"GPU memory: {gpu_memory_total:.1f}GB total, {gpu_memory_available:.1f}GB target")
        
        # Test different batch sizes
        max_sequence_length = self.config["phase3_integration"]["model_config"]["max_sequence_length"]
        hidden_dims = self.config["phase3_integration"]["model_config"]["hidden_dims"]
        
        optimal_batch_size = 1
        
        for batch_size in [1, 2, 4, 8, 16, 32]:
            try:
                # Estimate memory usage
                estimated_memory = self._estimate_memory_usage(batch_size, max_sequence_length, hidden_dims)
                
                if estimated_memory <= gpu_memory_available:
                    # Test actual allocation
                    if self._test_batch_size(batch_size, max_sequence_length):
                        optimal_batch_size = batch_size
                        self.logger.info(f"Batch size {batch_size} successful (estimated {estimated_memory:.2f}GB)")
                    else:
                        break
                else:
                    break
                    
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    break
                else:
                    raise
        
        self.performance_stats["optimal_batch_size"] = optimal_batch_size
        self.logger.info(f"Optimal batch size determined: {optimal_batch_size}")
    
    def _estimate_memory_usage(self, batch_size: int, seq_len: int, hidden_dims: int) -> float:
        """Estimate GPU memory usage for given parameters"""
        # Rough estimation based on transformer architecture
        # Model parameters
        model_params = 1.56e6  # From previous measurements
        model_memory = model_params * 4 / (1024**3)  # 4 bytes per float32
        
        # Activation memory (rough estimate)
        activation_memory = (
            batch_size * seq_len * hidden_dims * 4 * 10  # Multiple layers and attention
        ) / (1024**3)
        
        # Add safety margin
        total_memory = (model_memory + activation_memory) * 1.5
        
        return total_memory
    
    def _test_batch_size(self, batch_size: int, seq_len: int) -> bool:
        """Test if batch size works without OOM"""
        try:
            device = next(self.model.parameters()).device
            
            # Create test tensors
            test_audio = torch.randn(batch_size, seq_len, 201, device=device)
            test_difficulty = torch.randint(0, 3, (batch_size,), device=device)
            
            # Test forward pass
            with torch.no_grad():
                if self.use_mixed_precision:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(
                            audio_features=test_audio,
                            difficulty=test_difficulty,
                            return_loss=False
                        )
                else:
                    outputs = self.model(
                        audio_features=test_audio,
                        difficulty=test_difficulty,
                        return_loss=False
                    )
            
            # Cleanup
            del test_audio, test_difficulty, outputs
            torch.cuda.empty_cache()
            
            return True
            
        except RuntimeError as e:
            if "out of memory" in str(e).lower():
                torch.cuda.empty_cache()
                return False
            else:
                raise
    
    def _preallocate_memory_pools(self):
        """Pre-allocate memory pools for efficient processing"""
        if not torch.cuda.is_available():
            return
        
        try:
            # Pre-allocate common tensor sizes
            device = next(self.model.parameters()).device
            batch_size = self.performance_stats["optimal_batch_size"]
            max_seq_len = self.config["phase3_integration"]["model_config"]["max_sequence_length"]
            
            # Pre-allocate and immediately free to establish memory pools
            temp_tensors = []
            
            for seq_len in [100, 200, 400, 800]:
                if seq_len <= max_seq_len:
                    temp_tensor = torch.randn(batch_size, seq_len, 201, device=device)
                    temp_tensors.append(temp_tensor)
            
            # Free all at once
            del temp_tensors
            torch.cuda.empty_cache()
            
            self.logger.info("Memory pools pre-allocated")
            
        except Exception as e:
            self.logger.warning(f"Memory pool pre-allocation failed: {e}")
    
    def _generate_sequences_optimized(self, audio_features: torch.Tensor, 
                                    difficulty_levels: List[int]) -> Dict[int, torch.Tensor]:
        """Generate sequences with optimization"""
        device = next(self.model.parameters()).device
        batch_size = self.performance_stats["optimal_batch_size"]
        
        # Prepare batched input
        audio_batch = audio_features.unsqueeze(0).expand(batch_size, -1, -1).to(device)
        
        sequences = {}
        generation_params = self.config["phase3_integration"]["generation_parameters"]
        
        start_time = time.time()
        
        for difficulty in difficulty_levels:
            self.logger.info(f"Generating difficulty level {difficulty} (optimized)...")
            
            # Map difficulty levels (8,9,10) to model indices (0,1,2)
            model_difficulty = difficulty - 8
            difficulty_batch = torch.full((batch_size,), model_difficulty, device=device)
            
            with torch.no_grad():
                try:
                    if self.use_mixed_precision:
                        with torch.cuda.amp.autocast():
                            outputs = self.model(
                                audio_features=audio_batch,
                                difficulty=difficulty_batch,
                                return_loss=False
                            )
                    else:
                        outputs = self.model(
                            audio_features=audio_batch,
                            difficulty=difficulty_batch,
                            return_loss=False
                        )
                    
                    # Use the first batch result (they should be similar)
                    generated_sequence = torch.argmax(outputs['logits'], dim=-1)
                    sequences[difficulty] = generated_sequence[0].cpu()
                    
                    self.logger.info(f"Generated sequence for difficulty {difficulty}: {generated_sequence.shape}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to generate sequence for difficulty {difficulty}: {e}")
                    # Create fallback sequence
                    fallback_length = min(200, audio_features.shape[0] // 2)
                    sequences[difficulty] = torch.zeros(fallback_length, dtype=torch.long)
        
        generation_time = time.time() - start_time
        
        # Update performance stats
        total_sequences = len(difficulty_levels)
        self.performance_stats["average_generation_time"] = generation_time / total_sequences
        self.performance_stats["throughput_sequences_per_second"] = total_sequences / generation_time
        
        # Track peak memory usage
        if torch.cuda.is_available():
            peak_memory = torch.cuda.max_memory_allocated() / (1024**3)
            self.performance_stats["peak_memory_usage_gb"] = max(
                self.performance_stats["peak_memory_usage_gb"], peak_memory
            )
        
        return sequences
    
    def generate_tja(self, audio_file_path: str, 
                    difficulty_levels: List[int] = [8, 9, 10],
                    output_path: Optional[str] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate TJA with optimizations"""
        if not self.is_initialized:
            if not self.initialize():
                raise TJAGenerationError("Pipeline initialization failed")
        
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting optimized TJA generation for {audio_file_path}")
            
            with MemoryContext(self.memory_monitor, "optimized_tja_generation"):
                # Step 1: Validate and preprocess audio
                audio_info = self._validate_audio_file(audio_file_path)
                
                # Step 2: Extract audio features (Phase 2)
                audio_features = self._extract_audio_features(audio_file_path)
                
                # Step 3: Generate TJA sequences (optimized)
                tja_sequences = self._generate_sequences_optimized(audio_features, difficulty_levels)
                
                # Step 4: Create TJA file content
                tja_content = self._create_tja_content(tja_sequences, audio_info, metadata)
                
                # Step 5: Quality assessment
                quality_metrics = self._assess_quality(tja_sequences, audio_features)
                
                # Step 6: Save TJA file
                output_file_path = self._save_tja_file(tja_content, output_path, audio_file_path)
                
                # Step 7: Generate results with performance stats
                processing_time = time.time() - start_time
                results = self._create_results(
                    output_file_path, tja_sequences, quality_metrics, 
                    processing_time, audio_info
                )
                
                # Add optimization stats
                results["optimization_stats"] = self.performance_stats.copy()
                results["memory_efficiency"] = {
                    "peak_gpu_usage_gb": self.performance_stats["peak_memory_usage_gb"],
                    "gpu_utilization_percent": (self.performance_stats["peak_memory_usage_gb"] / 8.0) * 100,
                    "optimal_batch_size": self.performance_stats["optimal_batch_size"]
                }
                
                # Update statistics
                self._update_statistics(processing_time, True)
                
                self.logger.info(f"Optimized TJA generation completed in {processing_time:.2f}s")
                self.logger.info(f"Peak GPU usage: {self.performance_stats['peak_memory_usage_gb']:.2f}GB")
                self.logger.info(f"Throughput: {self.performance_stats['throughput_sequences_per_second']:.1f} seq/s")
                
                return results
                
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_statistics(processing_time, False)
            
            self.logger.error(f"Optimized TJA generation failed: {e}")
            raise TJAGenerationError(f"Generation failed: {str(e)}") from e
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization performance statistics"""
        memory_stats = self.memory_monitor.get_memory_stats()
        
        return {
            "performance_stats": self.performance_stats.copy(),
            "current_memory": {
                "gpu_memory_gb": memory_stats.gpu_reserved_gb,
                "gpu_utilization_percent": memory_stats.gpu_utilization_percent,
                "system_memory_percent": memory_stats.system_memory_percent
            },
            "optimization_settings": {
                "mixed_precision": self.use_mixed_precision,
                "dynamic_batching": self.use_dynamic_batching,
                "max_gpu_memory_fraction": self.max_gpu_memory_fraction
            },
            "hardware_info": {
                "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "CPU",
                "gpu_memory_total_gb": torch.cuda.get_device_properties(0).total_memory / (1024**3) if torch.cuda.is_available() else 0
            }
        }
