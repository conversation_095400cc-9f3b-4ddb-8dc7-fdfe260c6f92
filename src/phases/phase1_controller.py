"""
Phase 1 Controller: Data Analysis and Preprocessing

Refactored controller for Phase 1 implementing unified interface
and enterprise-grade error handling with RTX 3070 optimization.
"""

import argparse
from pathlib import Path
from typing import Any, Dict

from .base_phase_controller import BasePhaseController
from ..preprocessing.data_analyzer import <PERSON>jaDataAnalyzer
from ..utils.hardware_monitor import get_system_info, setup_hardware_optimized_processing
from ..path_management.path_manager import PathManager, PathType


class Phase1Controller(BasePhaseController):
    """
    Phase 1: Data Analysis and Preprocessing Controller
    
    Processes ~2,800 TJA files with strict metadata/notation separation
    and hardware optimization for RTX 3070 system.
    """
    
    def __init__(self, config_manager=None, resource_manager=None):
        super().__init__(
            phase_number=1,
            phase_name="Data Analysis and Preprocessing",
            config_manager=config_manager,
            resource_manager=resource_manager
        )
        
        # Initialize path manager
        self.path_manager = PathManager()
        
        # Initialize analyzer (will be created during execution)
        self.analyzer = None
    
    def _validate_prerequisites(self, args: argparse.Namespace) -> bool:
        """Validate Phase 1 prerequisites"""
        self._display_phase_info()
        
        # Validate hardware environment
        if not self._validate_hardware_environment():
            return False
        
        # Determine data directory
        if hasattr(args, 'data_dir') and args.data_dir:
            data_directory = self.path_manager.resolve_path(args.data_dir)
        else:
            data_directory = self.path_manager.get_standardized_path(PathType.DATA_RAW, "ese")
        
        # Validate data directory and discover TJA files
        if not self._validate_and_discover_data(str(data_directory)):
            return False
        
        # Store validated data directory for execution
        self.data_directory = data_directory
        
        return True
    
    def _prepare_execution_params(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Prepare execution parameters from command line arguments"""
        return {
            "data_directory": str(self.data_directory),
            "test_mode": getattr(args, 'test', False),
            "test_count": getattr(args, 'count', 50),
            "validate_only": getattr(args, 'validate_only', False)
        }
    
    def _execute_phase(self, params: Dict[str, Any]) -> bool:
        """Execute Phase 1 processing logic"""
        try:
            # Skip processing if validation only
            if params.get("validate_only", False):
                self.logger.info("Validation complete. Exiting (validate-only mode).")
                return True
            
            # Initialize analyzer with hardware optimization
            config = setup_hardware_optimized_processing()
            self.analyzer = TjaDataAnalyzer(config)
            
            self.logger.info(f"Initialized TjaDataAnalyzer with {config['parallel_workers']} workers")
            self.logger.info(f"Memory allocation: {config['memory_per_worker_gb']}GB per worker")
            self.logger.info(f"Batch size: {config['batch_size']} files per batch")
            
            if params["test_mode"]:
                self.logger.info(f"Test mode: Processing {params['test_count']} files only")
            
            # Discover TJA files
            tja_files = self.analyzer.discover_tja_files(params["data_directory"])
            
            if not tja_files:
                self.logger.error("No TJA files found for processing")
                return False
            
            self.logger.info(f"Processing {len(tja_files)} TJA files...")
            
            # Process files using the correct method
            results = self.analyzer._process_tja_file_list(
                tja_files,
                test_mode=params["test_mode"],
                test_count=params["test_count"]
            )
            
            # Display and log results
            self._display_processing_results(results)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Phase 1 execution failed: {e}")
            return False
    
    def _validate_hardware_environment(self) -> bool:
        """Validate hardware and software environment"""
        self.logger.info("Validating hardware environment...")
        
        system_info = get_system_info()
        
        self.logger.info("Hardware Configuration:")
        self.logger.info(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
        self.logger.info(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
        self.logger.info(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
        
        if system_info['gpu']['cuda_available']:
            self.logger.info(f"    Name: {system_info['gpu']['name']}")
            self.logger.info(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
        
        # Check requirements and log warnings
        warnings = []
        
        if system_info['cpu']['logical_cores'] < 12:
            warnings.append(f"Recommended 16+ logical cores, found {system_info['cpu']['logical_cores']}")
        
        if system_info['memory']['total_gb'] < 30:
            warnings.append(f"Recommended 32GB RAM, found {system_info['memory']['total_gb']:.1f}GB")
        
        if not system_info['gpu']['cuda_available']:
            warnings.append("CUDA not available - GPU acceleration disabled")
        
        if warnings:
            self.logger.warning("Hardware configuration warnings:")
            for warning in warnings:
                self.logger.warning(f"  {warning}")
        else:
            self.logger.info("Hardware configuration optimal")
        
        return True  # Continue even with warnings
    
    def _validate_and_discover_data(self, data_dir: str) -> bool:
        """Discover and validate TJA data"""
        self.logger.info(f"Discovering and validating data in: {data_dir}")
        
        data_path = Path(data_dir)
        if not data_path.exists():
            self.logger.error(f"Data directory not found: {data_dir}")
            return False
        
        # Discover TJA files
        tja_files = list(data_path.rglob("*.tja"))
        self.logger.info(f"Found {len(tja_files)} TJA files in {data_dir}")
        
        if len(tja_files) == 0:
            self.logger.error("No TJA files found")
            return False
        
        # Sample validation
        self.logger.info("Validating sample files...")
        sample_files = tja_files[:5]  # Validate first 5 files
        
        for i, tja_file in enumerate(sample_files, 1):
            try:
                # Test encoding detection
                from ..utils.encoding_detector import detect_file_encoding
                encoding = detect_file_encoding(str(tja_file))
                
                # Test basic parsing
                from ..parsing.custom_tja_parser import CustomTJAParser
                parser = CustomTJAParser(str(tja_file))
                
                self.logger.info(f"  {i}. {tja_file.name} - OK (encoding: {encoding})")
                
            except Exception as e:
                self.logger.warning(f"  {i}. {tja_file.name} - Error: {str(e)}")
        
        self.logger.info(f"Data validation complete - {len(tja_files)} files ready for processing")
        return True
    
    def _display_processing_results(self, results: Dict[str, Any]):
        """Display processing results"""
        stats = results['statistics']
        
        self.logger.info("=" * 60)
        self.logger.info("PROCESSING RESULTS")
        self.logger.info("=" * 60)
        self.logger.info(f"Total files: {stats['total_files']}")
        self.logger.info(f"Successful: {stats['successful_files']} ({stats['success_rate']:.1%})")
        self.logger.info(f"Failed: {stats['failed_files']}")
        self.logger.info(f"Speed: {stats['files_per_second']:.1f} files/second")
        self.logger.info(f"WAVE field compliance: {stats['wave_field_compliance']:.1%}")
        self.logger.info(f"Phase 2 eligible: {results['catalog']['processing_statistics']['phase_2_eligible']}")
        
        # Audio pairing statistics
        self.logger.info("Audio Pairing:")
        self.logger.info(f"  WAVE field resolved: {stats['wave_field_resolved']}")
        self.logger.info(f"  Filename fallback: {stats['filename_fallback_used']}")
        self.logger.info(f"  Missing audio: {stats['missing_audio']}")
        self.logger.info(f"  Empty WAVE field: {stats['wave_field_empty']}")
        
        # Training data statistics
        training_stats = results['catalog']['training_statistics']
        self.logger.info("Training Data:")
        self.logger.info(f"  Total note sequences: {training_stats['total_notation_sequences']:,}")
        self.logger.info(f"  Valid sequences: {training_stats['valid_notation_sequences']:,}")
        self.logger.info(f"  Total notes: {training_stats['total_notes']:,}")
        
        # Validation summary
        validation = results['catalog']['validation_summary']
        self.logger.info("Validation Summary:")
        self.logger.info(f"  Overall success rate: {validation['overall_success_rate']:.1%}")
        self.logger.info(f"  Notation purity score: {validation['notation_purity_score']:.1%}")
        self.logger.info(f"  Training readiness: {validation['training_readiness']:.1%}")
        
        self.logger.info("Phase 1 processing complete!")
        self.logger.info("Catalog saved to: data/processed/catalog.json")
        self.logger.info("Statistics saved to: data/processed/processing_statistics.json")
        
        if results['errors']:
            self.logger.warning(f"{len(results['errors'])} errors logged to: data/processed/processing_errors.json")
