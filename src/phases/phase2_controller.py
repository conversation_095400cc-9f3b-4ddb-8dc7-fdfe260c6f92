"""
Phase 2 Controller: Audio Feature Extraction and Temporal Alignment

Refactored controller for Phase 2 implementing unified interface
and enterprise-grade error handling with RTX 3070 optimization.
"""

import json
import time
import argparse
from pathlib import Path
from typing import Any, Dict

from .base_phase_controller import BasePhaseController
from ..pipeline.audio_analyzer import TjaAudioFeatureAnalyzer
from ..utils.hardware_monitor import get_system_info, setup_hardware_optimized_processing
from ..path_management.path_manager import PathManager, PathType


class Phase2Controller(BasePhaseController):
    """
    Phase 2: Audio Feature Extraction and Temporal Alignment Controller
    
    Extracts [T, 201] feature tensors from 2,800 audio files with precise
    temporal alignment and hardware optimization for RTX 3070 system.
    """
    
    def __init__(self, config_manager=None, resource_manager=None):
        super().__init__(
            phase_number=2,
            phase_name="Audio Feature Extraction and Temporal Alignment",
            config_manager=config_manager,
            resource_manager=resource_manager
        )
        
        # Initialize path manager
        self.path_manager = PathManager()
        
        # Initialize analyzer (will be created during execution)
        self.analyzer = None
    
    def _validate_prerequisites(self, args: argparse.Namespace) -> bool:
        """Validate Phase 2 prerequisites"""
        self._display_phase_info()
        
        # Validate Phase 1 outputs
        if not self._validate_phase1_outputs(args):
            return False
        
        # Validate hardware environment
        if not self._validate_hardware_environment():
            return False
        
        return True
    
    def _prepare_execution_params(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Prepare execution parameters from command line arguments"""
        # Determine catalog path
        if hasattr(args, 'catalog') and args.catalog:
            catalog_path = args.catalog
        else:
            catalog_path = str(self.path_manager.get_standardized_path(
                PathType.DATA_PROCESSED, "catalog.json"
            ))
        
        return {
            "catalog_path": catalog_path,
            "test_mode": getattr(args, 'test', False),
            "test_count": getattr(args, 'count', 50),
            "validate_only": getattr(args, 'validate_only', False)
        }
    
    def _execute_phase(self, params: Dict[str, Any]) -> bool:
        """Execute Phase 2 processing logic"""
        try:
            # Skip processing if validation only
            if params.get("validate_only", False):
                self.logger.info("Validation complete. Exiting (validate-only mode).")
                return True
            
            # Initialize analyzer with hardware optimization
            config = setup_hardware_optimized_processing()
            self.analyzer = TjaAudioFeatureAnalyzer(config)
            
            self.logger.info("Initialized TjaAudioFeatureAnalyzer with hardware optimization")
            self.logger.info(f"Batch size: {config['batch_size']} songs")
            self.logger.info(f"Workers: {config['parallel_workers']}")
            self.logger.info("Target: [T, 201] feature tensors")
            
            if params["test_mode"]:
                self.logger.info(f"Test mode: Processing {params['test_count']} songs only")
            
            # Load Phase 1 catalog
            self.logger.info(f"Loading Phase 1 catalog from {params['catalog_path']}...")
            catalog = self.analyzer.load_phase1_catalog(params["catalog_path"])
            
            if catalog["eligible_songs"] == 0:
                self.logger.error("No songs eligible for Phase 2 processing")
                return False
            
            self.logger.info(f"Loaded catalog: {catalog['eligible_songs']} eligible songs")
            
            # Process audio features
            self.logger.info("Starting audio feature extraction...")
            results = self.analyzer.process_audio_features(
                catalog, 
                test_mode=params["test_mode"], 
                test_count=params["test_count"]
            )
            
            # Display and log results
            self._display_processing_results(results)
            
            # Generate Phase 3 catalog
            self.logger.info("Generating Phase 3 input catalog...")
            phase3_catalog = self.analyzer.generate_phase3_catalog(results)
            
            self.logger.info(f"Phase 3 catalog generated with {phase3_catalog['phase3_ready_songs']} ready songs")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Phase 2 execution failed: {e}")
            return False
    
    def _validate_phase1_outputs(self, args: argparse.Namespace) -> bool:
        """Validate Phase 1 outputs exist and are valid"""
        # Determine catalog path
        if hasattr(args, 'catalog') and args.catalog:
            catalog_path = Path(args.catalog)
        else:
            catalog_path = self.path_manager.get_standardized_path(
                PathType.DATA_PROCESSED, "catalog.json"
            )
        
        if not catalog_path.exists():
            self.logger.error(f"Phase 1 catalog not found: {catalog_path}")
            self.logger.error("Please run Phase 1 first to generate the catalog")
            return False
        
        self.logger.info(f"Phase 1 catalog found: {catalog_path}")
        
        # Check catalog content
        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
            
            songs = catalog.get("songs", [])
            phase2_ready = sum(1 for song in songs if song.get("phase_2_ready", False))
            
            self.logger.info(f"Total songs in catalog: {len(songs)}")
            self.logger.info(f"Phase 2 ready songs: {phase2_ready}")
            self.logger.info(f"Phase 1 success rate: {catalog.get('processing_statistics', {}).get('success_rate', 0):.1%}")
            
            if phase2_ready == 0:
                self.logger.error("No songs ready for Phase 2 processing")
                return False
            
            self.logger.info("Phase 1 outputs validated successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating Phase 1 catalog: {e}")
            return False
    
    def _validate_hardware_environment(self) -> bool:
        """Validate hardware environment for Phase 2"""
        self.logger.info("Validating hardware environment...")
        
        system_info = get_system_info()
        
        self.logger.info("Hardware Configuration:")
        self.logger.info(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
        self.logger.info(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
        self.logger.info(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
        
        if system_info['gpu']['cuda_available']:
            self.logger.info(f"    Name: {system_info['gpu']['name']}")
            self.logger.info(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
        
        # Check requirements and log warnings
        warnings = []
        
        if system_info['cpu']['logical_cores'] < 12:
            warnings.append(f"Recommended 16+ logical cores, found {system_info['cpu']['logical_cores']}")
        
        if system_info['memory']['total_gb'] < 30:
            warnings.append(f"Recommended 32GB RAM, found {system_info['memory']['total_gb']:.1f}GB")
        
        if not system_info['gpu']['cuda_available']:
            warnings.append("CUDA not available - GPU acceleration disabled")
        
        if warnings:
            self.logger.warning("Hardware configuration warnings:")
            for warning in warnings:
                self.logger.warning(f"  {warning}")
        else:
            self.logger.info("Hardware configuration optimal")
        
        return True  # Continue even with warnings
    
    def _display_processing_results(self, results: Dict[str, Any]):
        """Display processing results"""
        stats = results["statistics"]
        
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2 PROCESSING RESULTS")
        self.logger.info("=" * 60)
        self.logger.info(f"Total songs processed: {stats['processed_songs']}")
        self.logger.info(f"Successful extractions: {stats['successful_extractions']} ({stats['feature_extraction_success_rate']:.1%})")
        self.logger.info(f"Failed extractions: {stats['failed_extractions']}")
        self.logger.info(f"Average processing time: {stats['average_processing_time']:.2f}s per song")
        self.logger.info(f"Temporal alignment accuracy: {stats.get('temporal_alignment_accuracy', 0):.1%}")
        
        # Hardware performance
        hardware_perf = results["hardware_performance"]
        self.logger.info("Hardware Performance:")
        self.logger.info(f"  Processing speed: {hardware_perf.get('files_per_second', 0):.1f} songs/second")
        self.logger.info(f"  Total processed: {hardware_perf.get('total_processed', 0)} items")
        
        # Feature extraction quality
        batch_stats = results["batch_processor_stats"]
        self.logger.info("Feature Extraction Quality:")
        self.logger.info(f"  Success rate: {batch_stats.get('success_rate', 0):.1%}")
        self.logger.info(f"  Songs per minute: {batch_stats.get('songs_per_minute', 0):.1f}")
        
        self.logger.info("Phase 2 processing complete!")
        self.logger.info("Feature tensors saved to: data/processed/audio_features/")
        self.logger.info("Phase 3 catalog saved to: data/processed/phase3_catalog.json")
