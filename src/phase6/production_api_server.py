"""
Production API Server for TJA Generation

FastAPI-based production server implementing the Phase 6 RFP specifications
with comprehensive monitoring, rate limiting, and error handling.

API Endpoints:
- POST /generate: Generate TJA chart from audio
- GET /health: Health check endpoint
- GET /metrics: Performance metrics endpoint
- GET /status: System status and resource usage
"""

import os
import time
import tempfile
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel, Field
import psutil

from .production_inference_system import TJAInferenceSystem
from .config import create_phase6_config, Phase6Config
from ..utils.resource_manager import ResourceManager


class GenerateRequest(BaseModel):
    """Request model for chart generation"""
    bpm: float = Field(..., ge=60, le=300, description="Beats per minute")
    offset: float = Field(0.0, ge=-10.0, le=10.0, description="Chart offset in seconds")
    difficulty: int = Field(..., ge=8, le=10, description="Difficulty level (8, 9, or 10)")
    course_type: str = Field("oni", regex="^(oni|edit)$", description="Course type")
    title: Optional[str] = Field(None, description="Chart title")
    artist: Optional[str] = Field(None, description="Artist name")


class GenerateResponse(BaseModel):
    """Response model for chart generation"""
    success: bool
    tja_content: Optional[str] = None
    validation_results: Optional[Dict[str, Any]] = None
    performance_metrics: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    recommendations: Optional[List[str]] = None


class HealthResponse(BaseModel):
    """Response model for health check"""
    status: str
    timestamp: str
    model_loaded: bool
    system_resources: Dict[str, Any]
    uptime_seconds: float


class MetricsResponse(BaseModel):
    """Response model for metrics"""
    inference_statistics: Dict[str, Any]
    resource_usage: Dict[str, Any]
    hardware_info: Dict[str, Any]
    api_statistics: Dict[str, Any]


class ProductionAPIServer:
    """
    Production-ready FastAPI server for TJA generation
    
    Implements comprehensive monitoring, rate limiting, error handling,
    and resource management for production deployment.
    """
    
    def __init__(self, config: Phase6Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="TJA Generation API",
            description="Production API for generating Taiko no Tatsujin rhythm charts",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # Server state
        self.start_time = time.time()
        self.inference_system: Optional[TJAInferenceSystem] = None
        self.resource_manager = ResourceManager()
        
        # API statistics
        self.api_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "active_requests": 0,
            "rate_limited_requests": 0
        }
        
        # Rate limiting
        self.request_timestamps = []
        self.max_requests_per_minute = 60
        self.max_concurrent_requests = 5
        
        # Setup middleware and routes
        self._setup_middleware()
        self._setup_routes()
        
        self.logger.info("Production API server initialized")
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Compression middleware
        self.app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        # Request logging middleware
        @self.app.middleware("http")
        async def log_requests(request, call_next):
            start_time = time.time()
            
            # Rate limiting check
            if not self._check_rate_limit():
                self.api_stats["rate_limited_requests"] += 1
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            
            # Concurrent request check
            if self.api_stats["active_requests"] >= self.max_concurrent_requests:
                raise HTTPException(status_code=503, detail="Server busy - too many concurrent requests")
            
            self.api_stats["active_requests"] += 1
            self.api_stats["total_requests"] += 1
            
            try:
                response = await call_next(request)
                
                # Update statistics
                response_time = time.time() - start_time
                self._update_response_time_stats(response_time)
                
                if response.status_code < 400:
                    self.api_stats["successful_requests"] += 1
                else:
                    self.api_stats["failed_requests"] += 1
                
                # Add performance headers
                response.headers["X-Response-Time"] = f"{response_time:.3f}s"
                response.headers["X-Server-Time"] = datetime.now().isoformat()
                
                return response
                
            except Exception as e:
                self.api_stats["failed_requests"] += 1
                self.logger.error(f"Request failed: {e}")
                raise
            finally:
                self.api_stats["active_requests"] -= 1
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.post("/generate", response_model=GenerateResponse)
        async def generate_chart(
            background_tasks: BackgroundTasks,
            audio_file: UploadFile = File(..., description="Audio file (.ogg, .wav, .mp3)"),
            bpm: float = Form(..., ge=60, le=300, description="Beats per minute"),
            offset: float = Form(0.0, ge=-10.0, le=10.0, description="Chart offset in seconds"),
            difficulty: int = Form(..., ge=8, le=10, description="Difficulty level"),
            course_type: str = Form("oni", regex="^(oni|edit)$", description="Course type"),
            title: Optional[str] = Form(None, description="Chart title"),
            artist: Optional[str] = Form(None, description="Artist name")
        ):
            """Generate TJA chart from uploaded audio file"""
            
            if not self.inference_system:
                raise HTTPException(status_code=503, detail="Inference system not initialized")
            
            # Validate file type
            if not audio_file.filename.lower().endswith(('.ogg', '.wav', '.mp3')):
                raise HTTPException(
                    status_code=400, 
                    detail="Invalid file format. Only .ogg, .wav, and .mp3 files are supported"
                )
            
            # Check file size (max 50MB)
            if audio_file.size and audio_file.size > 50 * 1024 * 1024:
                raise HTTPException(status_code=413, detail="File too large. Maximum size is 50MB")
            
            temp_file_path = None
            
            try:
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix=Path(audio_file.filename).suffix) as temp_file:
                    content = await audio_file.read()
                    temp_file.write(content)
                    temp_file_path = temp_file.name
                
                # Prepare metadata
                metadata = {}
                if title:
                    metadata["title"] = title
                if artist:
                    metadata["artist"] = artist
                
                # Generate chart
                result = self.inference_system.generate_chart(
                    temp_file_path, bpm, offset, difficulty, course_type, metadata
                )
                
                # Schedule cleanup
                background_tasks.add_task(self._cleanup_temp_file, temp_file_path)
                
                if result["success"]:
                    # Convert TJA chart to string
                    tja_content = self._chart_to_string(result["tja_chart"])
                    
                    return GenerateResponse(
                        success=True,
                        tja_content=tja_content,
                        validation_results=result["validation_results"],
                        performance_metrics=result["performance_metrics"]
                    )
                else:
                    return GenerateResponse(
                        success=False,
                        error=result["error"],
                        recommendations=result.get("recommendations", [])
                    )
                    
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Chart generation failed: {e}")
                
                # Cleanup on error
                if temp_file_path and os.path.exists(temp_file_path):
                    background_tasks.add_task(self._cleanup_temp_file, temp_file_path)
                
                return GenerateResponse(
                    success=False,
                    error=str(e),
                    recommendations=["Check audio file format and parameters", "Try with a shorter audio file"]
                )
        
        @self.app.get("/health", response_model=HealthResponse)
        async def health_check():
            """Health check endpoint"""
            
            # Get system resources
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            system_resources = {
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "cpu_percent": cpu_percent,
                "disk_usage_percent": psutil.disk_usage('/').percent
            }
            
            # Add GPU info if available
            if hasattr(self.inference_system, 'device') and self.inference_system.device.type == 'cuda':
                import torch
                gpu_memory_used = torch.cuda.memory_allocated() / (1024**3)
                gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                system_resources["gpu_memory_used_gb"] = gpu_memory_used
                system_resources["gpu_memory_total_gb"] = gpu_memory_total
                system_resources["gpu_memory_percent"] = (gpu_memory_used / gpu_memory_total) * 100
            
            return HealthResponse(
                status="healthy" if self.inference_system else "initializing",
                timestamp=datetime.now().isoformat(),
                model_loaded=self.inference_system is not None,
                system_resources=system_resources,
                uptime_seconds=time.time() - self.start_time
            )
        
        @self.app.get("/metrics", response_model=MetricsResponse)
        async def get_metrics():
            """Get comprehensive performance metrics"""
            
            if not self.inference_system:
                raise HTTPException(status_code=503, detail="Inference system not initialized")
            
            # Get inference system metrics
            performance_summary = self.inference_system.get_performance_summary()
            
            return MetricsResponse(
                inference_statistics=performance_summary["inference_statistics"],
                resource_usage=performance_summary["resource_summary"],
                hardware_info=performance_summary["hardware_info"],
                api_statistics=self.api_stats.copy()
            )
        
        @self.app.get("/status")
        async def get_status():
            """Get detailed system status"""
            
            status = {
                "server": {
                    "status": "running",
                    "uptime_seconds": time.time() - self.start_time,
                    "active_requests": self.api_stats["active_requests"],
                    "total_requests": self.api_stats["total_requests"]
                },
                "inference_system": {
                    "initialized": self.inference_system is not None,
                    "device": str(self.inference_system.device) if self.inference_system else "N/A"
                },
                "resources": self.resource_manager.get_resource_summary()
            }
            
            return status
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        
        # Remove timestamps older than 1 minute
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if current_time - ts < 60
        ]
        
        # Check rate limit
        if len(self.request_timestamps) >= self.max_requests_per_minute:
            return False
        
        # Add current timestamp
        self.request_timestamps.append(current_time)
        return True
    
    def _update_response_time_stats(self, response_time: float):
        """Update response time statistics"""
        n = self.api_stats["successful_requests"] + self.api_stats["failed_requests"]
        if n > 0:
            self.api_stats["average_response_time"] = (
                (self.api_stats["average_response_time"] * (n - 1) + response_time) / n
            )
    
    def _chart_to_string(self, tja_chart) -> str:
        """Convert TJA chart object to string format"""
        if hasattr(tja_chart, 'to_string'):
            return tja_chart.to_string()
        elif hasattr(self.inference_system.postprocessor, 'chart_to_tja_string'):
            return self.inference_system.postprocessor.chart_to_tja_string(tja_chart)
        else:
            # Fallback: basic string representation
            return str(tja_chart)
    
    async def _cleanup_temp_file(self, file_path: str):
        """Cleanup temporary file"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            self.logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
    
    async def initialize_inference_system(self):
        """Initialize the inference system"""
        try:
            self.logger.info("Initializing inference system...")
            
            self.inference_system = TJAInferenceSystem(self.config)
            self.inference_system.initialize_components()
            
            # Load model if path exists
            model_path = self.config.inference.model_path
            if Path(model_path).exists():
                self.inference_system.load_model(model_path)
                self.logger.info("Inference system initialized successfully")
            else:
                self.logger.warning(f"Model not found at {model_path}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize inference system: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown server and cleanup resources"""
        self.logger.info("Shutting down API server...")
        
        if self.inference_system:
            self.inference_system.cleanup()
        
        self.resource_manager.cleanup_memory()
        
        self.logger.info("API server shutdown completed")


def create_production_server(config_path: Optional[str] = None) -> ProductionAPIServer:
    """Create production API server with configuration"""
    
    # Load configuration
    if config_path and Path(config_path).exists():
        config = Phase6Config.load_config(config_path)
    else:
        config = create_phase6_config(
            experiment_name="production_api",
            debug_mode=False
        )
    
    return ProductionAPIServer(config)


async def run_production_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    config_path: Optional[str] = None,
    workers: int = 1
):
    """Run production API server"""
    
    # Create server
    server = create_production_server(config_path)
    
    # Initialize inference system
    await server.initialize_inference_system()
    
    # Configure uvicorn
    config = uvicorn.Config(
        server.app,
        host=host,
        port=port,
        workers=workers,
        log_level="info",
        access_log=True,
        loop="asyncio"
    )
    
    # Run server
    server_instance = uvicorn.Server(config)
    
    try:
        await server_instance.serve()
    finally:
        await server.shutdown()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Production TJA Generation API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host address")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run server
    asyncio.run(run_production_server(
        host=args.host,
        port=args.port,
        config_path=args.config,
        workers=args.workers
    ))
