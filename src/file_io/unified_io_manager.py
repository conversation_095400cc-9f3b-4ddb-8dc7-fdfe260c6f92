"""
Unified I/O Manager

Centralizes all file input/output operations with consistent path handling,
error management, and format standardization across all phases.
"""

import json
import pickle
import logging
import time
from pathlib import Path
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional, List, Union

from ..utils.base_classes import BaseProcessor, ProcessingResult
from ..config.unified_config_manager import UnifiedConfigManager
from ..utils.encoding_detector import detect_file_encoding


@dataclass
class FileOperationResult:
    """Standardized result for file operations"""
    success: bool
    file_path: str
    operation: str
    data_size_bytes: int = 0
    encoding: Optional[str] = None
    error_message: Optional[str] = None
    processing_time_seconds: float = 0.0


class FileHandlerInterface(ABC):
    """Interface for file handlers following Interface Segregation Principle"""
    
    @abstractmethod
    def can_handle(self, file_path: str) -> bool:
        """Check if handler can process this file type"""
        pass
    
    @abstractmethod
    def read_file(self, file_path: str) -> Any:
        """Read file and return data"""
        pass
    
    @abstractmethod
    def write_file(self, file_path: str, data: Any) -> bool:
        """Write data to file"""
        pass


class JsonFileHandler(FileHandlerInterface):
    """Handler for JSON files"""
    
    def can_handle(self, file_path: str) -> bool:
        return Path(file_path).suffix.lower() == '.json'
    
    def read_file(self, file_path: str) -> Any:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def write_file(self, file_path: str, data: Any) -> bool:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception:
            return False


class PickleFileHandler(FileHandlerInterface):
    """Handler for pickle files"""
    
    def can_handle(self, file_path: str) -> bool:
        return Path(file_path).suffix.lower() in ['.pkl', '.pickle']
    
    def read_file(self, file_path: str) -> Any:
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    
    def write_file(self, file_path: str, data: Any) -> bool:
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            return True
        except Exception:
            return False


class TextFileHandler(FileHandlerInterface):
    """Handler for text files with encoding detection"""

    def can_handle(self, file_path: str) -> bool:
        return Path(file_path).suffix.lower() in ['.txt', '.log', '.tja', '.md', '.csv', '.yaml', '.yml']

    def read_file(self, file_path: str) -> Any:
        encoding = detect_file_encoding(file_path)
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()

    def write_file(self, file_path: str, data: Any) -> bool:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(data))
            return True
        except Exception:
            return False


class AudioFileHandler(FileHandlerInterface):
    """Handler for audio files (metadata only)"""

    def can_handle(self, file_path: str) -> bool:
        return Path(file_path).suffix.lower() in ['.mp3', '.wav', '.ogg', '.flac', '.m4a']

    def read_file(self, file_path: str) -> Any:
        # Return file metadata instead of actual audio data
        path = Path(file_path)
        return {
            "file_path": str(path),
            "file_size": path.stat().st_size,
            "file_type": path.suffix.lower(),
            "exists": path.exists()
        }

    def write_file(self, file_path: str, data: Any) -> bool:
        # Audio files are not written through this handler
        raise NotImplementedError("Audio file writing not supported through I/O manager")


class TensorFileHandler(FileHandlerInterface):
    """Handler for tensor files (PyTorch)"""

    def can_handle(self, file_path: str) -> bool:
        return Path(file_path).suffix.lower() in ['.pt', '.pth', '.tensor']

    def read_file(self, file_path: str) -> Any:
        try:
            import torch
            return torch.load(file_path, map_location='cpu')
        except ImportError:
            raise ImportError("PyTorch not available for tensor file handling")

    def write_file(self, file_path: str, data: Any) -> bool:
        try:
            import torch
            torch.save(data, file_path)
            return True
        except Exception:
            return False


class UnifiedIoManager(BaseProcessor):
    """
    Unified I/O manager for all file operations
    
    Provides consistent file handling across all phases with:
    - Standardized path resolution
    - Automatic format detection
    - Error handling and logging
    - Performance monitoring
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "UnifiedIoManager")
        
        # Initialize configuration
        self.config_manager = UnifiedConfigManager()
        
        # Initialize file handlers
        self.file_handlers = [
            JsonFileHandler(),
            PickleFileHandler(),
            TextFileHandler(),
            AudioFileHandler(),
            TensorFileHandler()
        ]
        
        # File operation statistics
        self.operation_stats = {
            "files_read": 0,
            "files_written": 0,
            "bytes_read": 0,
            "bytes_written": 0,
            "read_errors": 0,
            "write_errors": 0
        }
        
        self.logger.info("UnifiedIoManager initialized with consolidated file handlers")
    
    def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Process I/O operations according to BaseProcessor interface
        
        Args:
            input_data: I/O operation parameters
            
        Returns:
            ProcessingResult with operation results
        """
        with self._processing_context("unified_io_operations"):
            try:
                operation = input_data.get("operation", "read")
                file_path = input_data.get("file_path")
                data = input_data.get("data")
                
                if operation == "read":
                    result = self.read_file(file_path)
                elif operation == "write":
                    result = self.write_file(file_path, data)
                elif operation == "batch_read":
                    result = self.batch_read_files(input_data.get("file_paths", []))
                elif operation == "batch_write":
                    result = self.batch_write_files(input_data.get("file_data", {}))
                else:
                    raise ValueError(f"Unknown operation: {operation}")
                
                return ProcessingResult(
                    success=True,
                    data=result,
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
                
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
    
    def read_file(self, file_path: str) -> FileOperationResult:
        """
        Read file with automatic format detection
        
        Args:
            file_path: Path to file to read
            
        Returns:
            FileOperationResult with file data
        """
        try:
            # Resolve path consistently
            resolved_path = self.config_manager.resolve_path(file_path)
            
            if not resolved_path.exists():
                raise FileNotFoundError(f"File not found: {resolved_path}")
            
            # Find appropriate handler
            handler = self._get_file_handler(str(resolved_path))
            if not handler:
                raise ValueError(f"No handler found for file type: {resolved_path.suffix}")
            
            # Read file
            start_time = time.time()
            data = handler.read_file(str(resolved_path))
            processing_time = time.time() - start_time
            
            # Get file size
            file_size = resolved_path.stat().st_size
            
            # Update statistics
            self.operation_stats["files_read"] += 1
            self.operation_stats["bytes_read"] += file_size
            
            self.logger.debug(f"Read file: {resolved_path} ({file_size} bytes)")
            
            return FileOperationResult(
                success=True,
                file_path=str(resolved_path),
                operation="read",
                data_size_bytes=file_size,
                encoding=getattr(handler, 'encoding', None),
                processing_time_seconds=processing_time
            )
            
        except Exception as e:
            self.operation_stats["read_errors"] += 1
            self.logger.error(f"Failed to read file {file_path}: {e}")
            
            return FileOperationResult(
                success=False,
                file_path=str(file_path),
                operation="read",
                error_message=str(e)
            )
    
    def write_file(self, file_path: str, data: Any, 
                   create_directories: bool = True) -> FileOperationResult:
        """
        Write file with automatic format detection
        
        Args:
            file_path: Path to file to write
            data: Data to write
            create_directories: Whether to create parent directories
            
        Returns:
            FileOperationResult with operation status
        """
        try:
            # Resolve path consistently
            resolved_path = self.config_manager.resolve_path(file_path)
            
            # Create parent directories if needed
            if create_directories:
                resolved_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Find appropriate handler
            handler = self._get_file_handler(str(resolved_path))
            if not handler:
                raise ValueError(f"No handler found for file type: {resolved_path.suffix}")
            
            # Write file
            start_time = time.time()
            success = handler.write_file(str(resolved_path), data)
            processing_time = time.time() - start_time
            
            if not success:
                raise IOError(f"Handler failed to write file: {resolved_path}")
            
            # Get file size
            file_size = resolved_path.stat().st_size if resolved_path.exists() else 0
            
            # Update statistics
            self.operation_stats["files_written"] += 1
            self.operation_stats["bytes_written"] += file_size
            
            self.logger.debug(f"Wrote file: {resolved_path} ({file_size} bytes)")
            
            return FileOperationResult(
                success=True,
                file_path=str(resolved_path),
                operation="write",
                data_size_bytes=file_size,
                processing_time_seconds=processing_time
            )
            
        except Exception as e:
            self.operation_stats["write_errors"] += 1
            self.logger.error(f"Failed to write file {file_path}: {e}")
            
            return FileOperationResult(
                success=False,
                file_path=str(file_path),
                operation="write",
                error_message=str(e)
            )
    
    def batch_read_files(self, file_paths: List[str]) -> Dict[str, FileOperationResult]:
        """Read multiple files in batch"""
        results = {}
        
        for file_path in file_paths:
            results[file_path] = self.read_file(file_path)
        
        return results
    
    def batch_write_files(self, file_data: Dict[str, Any]) -> Dict[str, FileOperationResult]:
        """Write multiple files in batch"""
        results = {}
        
        for file_path, data in file_data.items():
            results[file_path] = self.write_file(file_path, data)
        
        return results
    
    def _get_file_handler(self, file_path: str) -> Optional[FileHandlerInterface]:
        """Get appropriate file handler for file type"""
        for handler in self.file_handlers:
            if handler.can_handle(file_path):
                return handler
        return None

    def standardize_phase_data(self, phase_number: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize data structure for phase handoffs

        Args:
            phase_number: Source phase number
            data: Raw phase data

        Returns:
            Standardized data structure
        """
        standardized = {
            "phase": phase_number,
            "timestamp": time.time(),
            "version": "1.0",
            "data": data,
            "metadata": {
                "processing_time": data.get("processing_time", 0.0),
                "memory_usage_mb": data.get("memory_usage_mb", 0.0),
                "success_rate": data.get("success_rate", 0.0)
            }
        }

        self.logger.debug(f"Standardized Phase {phase_number} data structure")
        return standardized

    def validate_phase_handoff(self, from_phase: int, to_phase: int, data: Dict[str, Any]) -> bool:
        """
        Validate data structure for phase-to-phase handoff

        Args:
            from_phase: Source phase number
            to_phase: Target phase number
            data: Data to validate

        Returns:
            bool: Validation success
        """
        required_fields = ["phase", "timestamp", "version", "data"]

        for field in required_fields:
            if field not in data:
                self.logger.error(f"Missing required field '{field}' in phase handoff data")
                return False

        if data["phase"] != from_phase:
            self.logger.error(f"Phase mismatch: expected {from_phase}, got {data['phase']}")
            return False

        self.logger.info(f"Phase {from_phase} to {to_phase} handoff validation successful")
        return True

    def create_phase_catalog(self, phase_number: int, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create standardized catalog for phase outputs

        Args:
            phase_number: Phase number
            entries: List of catalog entries

        Returns:
            Standardized catalog structure
        """
        catalog = {
            "catalog_version": "1.0",
            "phase": phase_number,
            "created_timestamp": time.time(),
            "total_entries": len(entries),
            "entries": entries,
            "statistics": {
                "successful_entries": sum(1 for e in entries if e.get("success", False)),
                "failed_entries": sum(1 for e in entries if not e.get("success", True)),
                "success_rate": sum(1 for e in entries if e.get("success", False)) / len(entries) if entries else 0.0
            }
        }

        self.logger.info(f"Created Phase {phase_number} catalog with {len(entries)} entries")
        return catalog
    
    def get_operation_statistics(self) -> Dict[str, Any]:
        """Get I/O operation statistics"""
        return self.operation_stats.copy()
    
    def list_directory(self, directory_path: str, 
                      pattern: str = "*", 
                      recursive: bool = False) -> List[str]:
        """
        List files in directory with pattern matching
        
        Args:
            directory_path: Directory to list
            pattern: File pattern to match
            recursive: Whether to search recursively
            
        Returns:
            List of file paths
        """
        try:
            dir_path = self.config_manager.resolve_path(directory_path)
            
            if not dir_path.exists() or not dir_path.is_dir():
                return []
            
            if recursive:
                files = list(dir_path.rglob(pattern))
            else:
                files = list(dir_path.glob(pattern))
            
            # Return standardized paths
            return [str(self.config_manager.resolve_path(f)) for f in files if f.is_file()]
            
        except Exception as e:
            self.logger.error(f"Failed to list directory {directory_path}: {e}")
            return []
    
    def ensure_directory_exists(self, directory_path: str) -> bool:
        """Ensure directory exists, create if necessary"""
        try:
            dir_path = self.config_manager.ensure_directory_exists(directory_path)
            return dir_path.exists()
        except Exception as e:
            self.logger.error(f"Failed to create directory {directory_path}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get file information"""
        try:
            resolved_path = self.config_manager.resolve_path(file_path)
            
            if not resolved_path.exists():
                return {"exists": False}
            
            stat = resolved_path.stat()
            
            return {
                "exists": True,
                "size_bytes": stat.st_size,
                "modified_time": stat.st_mtime,
                "is_file": resolved_path.is_file(),
                "is_directory": resolved_path.is_dir(),
                "suffix": resolved_path.suffix,
                "name": resolved_path.name,
                "parent": str(resolved_path.parent)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get file info for {file_path}: {e}")
            return {"exists": False, "error": str(e)}
