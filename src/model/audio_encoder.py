"""
Audio Feature Encoder

Multi-scale CNN + Transformer encoder for processing [T, 201] audio features
into contextualized representations for TJA generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Optional, Tuple


class PositionalEncoding(nn.Module):
    """Sinusoidal positional encoding for temporal sequences"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Add positional encoding to input tensor"""
        return x + self.pe[:x.size(0), :]


class MultiScaleConv1D(nn.Module):
    """Multi-scale 1D convolution for capturing different temporal patterns"""
    
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        
        # Different kernel sizes for multi-scale feature extraction
        self.conv_small = nn.Conv1d(in_channels, out_channels // 4, kernel_size=3, padding=1)
        self.conv_medium = nn.Conv1d(in_channels, out_channels // 4, kernel_size=7, padding=3)
        self.conv_large = nn.Conv1d(in_channels, out_channels // 4, kernel_size=15, padding=7)
        self.conv_xlarge = nn.Conv1d(in_channels, out_channels // 4, kernel_size=31, padding=15)
        
        self.norm = nn.LayerNorm(out_channels)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [batch, time, features]
        Returns:
            Multi-scale features [batch, time, out_channels]
        """
        # Transpose for conv1d: [batch, features, time]
        x_conv = x.transpose(1, 2)
        
        # Apply different scale convolutions
        small = F.relu(self.conv_small(x_conv))
        medium = F.relu(self.conv_medium(x_conv))
        large = F.relu(self.conv_large(x_conv))
        xlarge = F.relu(self.conv_xlarge(x_conv))
        
        # Concatenate and transpose back
        multi_scale = torch.cat([small, medium, large, xlarge], dim=1)
        multi_scale = multi_scale.transpose(1, 2)  # [batch, time, out_channels]
        
        # Normalization and dropout
        multi_scale = self.norm(multi_scale)
        multi_scale = self.dropout(multi_scale)
        
        return multi_scale


class AudioFeatureEncoder(nn.Module):
    """
    Audio feature encoder that processes [T, 201] features into contextualized representations
    
    Architecture:
    1. Input projection: 201 → 256 dimensions
    2. Multi-scale convolution for local pattern extraction
    3. Transformer encoder for long-range dependencies
    4. Output projection for downstream tasks
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        self.input_dims = config["audio_feature_dims"]  # 201
        self.hidden_dims = config["hidden_dims"]        # 256
        self.num_layers = config["num_encoder_layers"]  # 6
        self.num_heads = config["num_attention_heads"]  # 8
        self.dropout = config["dropout"]                # 0.1
        self.max_seq_len = config["max_sequence_length"] # 2000
        
        # Input projection
        self.input_projection = nn.Linear(self.input_dims, self.hidden_dims)
        
        # Multi-scale convolution for local pattern extraction
        self.multi_scale_conv = MultiScaleConv1D(self.hidden_dims, self.hidden_dims)
        
        # Positional encoding
        self.pos_encoding = PositionalEncoding(self.hidden_dims, self.max_seq_len)
        
        # Transformer encoder layers (memory optimized)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.hidden_dims,
            nhead=self.num_heads,
            dim_feedforward=self.hidden_dims * 2,  # Reduced from 4x to 2x for memory
            dropout=self.dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=self.num_layers
        )

        # Enable gradient checkpointing for memory efficiency
        self.use_gradient_checkpointing = True
        
        # Output projection layers
        self.output_norm = nn.LayerNorm(self.hidden_dims)
        self.output_projection = nn.Linear(self.hidden_dims, self.hidden_dims)
        
        # Feature-specific projections for different aspects
        self.spectral_projection = nn.Linear(153, self.hidden_dims // 4)  # Spectral features
        self.rhythmic_projection = nn.Linear(32, self.hidden_dims // 4)   # Rhythmic features  
        self.temporal_projection = nn.Linear(16, self.hidden_dims // 4)   # Temporal features
        self.combined_projection = nn.Linear(self.hidden_dims // 4 * 3, self.hidden_dims)
        
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Conv1d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, audio_features: torch.Tensor, 
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass of audio feature encoder
        
        Args:
            audio_features: [batch, time, 201] audio features from Phase 2
            attention_mask: [batch, time] mask for padding tokens
            
        Returns:
            Dictionary containing:
            - encoded_features: [batch, time, hidden_dims] contextualized features
            - attention_weights: [batch, num_heads, time, time] attention weights
            - feature_embeddings: [batch, time, hidden_dims] before transformer
        """
        batch_size, seq_len, feature_dims = audio_features.shape
        
        # Validate input dimensions
        assert feature_dims == self.input_dims, f"Expected {self.input_dims} features, got {feature_dims}"
        assert seq_len <= self.max_seq_len, f"Sequence length {seq_len} exceeds maximum {self.max_seq_len}"
        
        # Split features into components for specialized processing
        spectral_features = audio_features[:, :, :153]    # Mel + MFCC + Chroma
        rhythmic_features = audio_features[:, :, 153:185] # Rhythmic features
        temporal_features = audio_features[:, :, 185:]    # Temporal features
        
        # Process each feature type separately
        spectral_emb = F.relu(self.spectral_projection(spectral_features))
        rhythmic_emb = F.relu(self.rhythmic_projection(rhythmic_features))
        temporal_emb = F.relu(self.temporal_projection(temporal_features))
        
        # Combine specialized embeddings
        combined_features = torch.cat([spectral_emb, rhythmic_emb, temporal_emb], dim=-1)
        feature_embeddings = self.combined_projection(combined_features)
        
        # Alternative: Direct projection (for comparison)
        # feature_embeddings = self.input_projection(audio_features)
        
        # Multi-scale convolution for local pattern extraction
        conv_features = self.multi_scale_conv(feature_embeddings)
        
        # Add positional encoding
        # Transpose for positional encoding: [time, batch, features]
        conv_features_t = conv_features.transpose(0, 1)
        pos_encoded = self.pos_encoding(conv_features_t)
        pos_encoded = pos_encoded.transpose(0, 1)  # Back to [batch, time, features]
        
        # Transformer encoder with gradient checkpointing
        if attention_mask is not None:
            # Convert attention mask to transformer format (True = ignore)
            # attention_mask is [batch, seq_len], transformer expects [batch, seq_len] for key_padding_mask
            transformer_mask = ~attention_mask.bool()
        else:
            transformer_mask = None

        # Use gradient checkpointing if enabled and in training mode
        if self.use_gradient_checkpointing and self.training:
            from torch.utils.checkpoint import checkpoint
            # For checkpoint, we need to create a wrapper function
            def checkpoint_fn(x, mask):
                return self.transformer_encoder(x, src_key_padding_mask=mask)
            encoded_features = checkpoint(
                checkpoint_fn,
                pos_encoded,
                transformer_mask,
                use_reentrant=False
            )
        else:
            encoded_features = self.transformer_encoder(
                pos_encoded,
                src_key_padding_mask=transformer_mask
            )
        
        # Output normalization and projection
        encoded_features = self.output_norm(encoded_features)
        final_features = self.output_projection(encoded_features)
        
        return {
            "encoded_features": final_features,
            "feature_embeddings": feature_embeddings,
            "conv_features": conv_features,
            "attention_mask": attention_mask
        }
    
    def get_attention_weights(self, audio_features: torch.Tensor,
                            attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Extract attention weights from the transformer encoder
        
        Args:
            audio_features: [batch, time, 201] input features
            attention_mask: [batch, time] attention mask
            
        Returns:
            attention_weights: [batch, num_layers, num_heads, time, time]
        """
        # This would require modifying the transformer to return attention weights
        # For now, return a placeholder
        batch_size, seq_len, _ = audio_features.shape
        return torch.zeros(batch_size, self.num_layers, self.num_heads, seq_len, seq_len)
    
    def encode_sequence_batch(self, audio_features: torch.Tensor,
                             chunk_size: int = 500) -> torch.Tensor:
        """
        Encode long sequences in chunks to manage memory usage
        
        Args:
            audio_features: [batch, time, 201] long audio sequences
            chunk_size: Maximum chunk size for processing
            
        Returns:
            encoded_features: [batch, time, hidden_dims] encoded features
        """
        batch_size, seq_len, feature_dims = audio_features.shape
        
        if seq_len <= chunk_size:
            # Process normally if sequence is short enough
            return self.forward(audio_features)["encoded_features"]
        
        # Process in chunks with overlap
        overlap = chunk_size // 4  # 25% overlap
        stride = chunk_size - overlap
        
        encoded_chunks = []
        
        for start_idx in range(0, seq_len, stride):
            end_idx = min(start_idx + chunk_size, seq_len)
            chunk = audio_features[:, start_idx:end_idx, :]
            
            # Create attention mask for chunk
            chunk_mask = torch.ones(batch_size, chunk.shape[1], 
                                  device=audio_features.device, dtype=torch.bool)
            
            # Encode chunk
            chunk_encoded = self.forward(chunk, chunk_mask)["encoded_features"]
            
            # Handle overlap by averaging
            if start_idx > 0 and len(encoded_chunks) > 0:
                # Average overlapping region
                overlap_start = overlap
                prev_chunk = encoded_chunks[-1]
                prev_overlap = prev_chunk[:, -overlap:, :]
                curr_overlap = chunk_encoded[:, :overlap, :]
                averaged_overlap = (prev_overlap + curr_overlap) / 2
                
                # Update previous chunk and current chunk
                encoded_chunks[-1] = torch.cat([
                    prev_chunk[:, :-overlap, :], 
                    averaged_overlap
                ], dim=1)
                chunk_encoded = chunk_encoded[:, overlap:, :]
            
            encoded_chunks.append(chunk_encoded)
        
        # Concatenate all chunks
        return torch.cat(encoded_chunks, dim=1)[:, :seq_len, :]
