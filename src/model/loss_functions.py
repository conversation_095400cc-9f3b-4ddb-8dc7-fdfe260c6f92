from typing import Dict, Optional
"""
Loss Functions

Specialized loss functions for TJA generation including note prediction loss,
timing loss, difficulty consistency loss, and pattern coherence loss.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class TJAGenerationLoss(nn.Module):
    """
    Comprehensive loss function for TJA generation combining multiple objectives:
    1. Note prediction loss (cross-entropy)
    2. Timing consistency loss
    3. Difficulty appropriateness loss
    4. Pattern coherence loss
    5. Musical structure loss
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        self.vocab_size = config.get("note_types", 8)
        
        # Loss weights
        self.note_loss_weight = config.get("note_loss_weight", 1.0)
        self.timing_loss_weight = config.get("timing_loss_weight", 0.5)
        self.difficulty_loss_weight = config.get("difficulty_loss_weight", 0.3)
        self.pattern_loss_weight = config.get("pattern_loss_weight", 0.2)
        self.structure_loss_weight = config.get("structure_loss_weight", 0.1)
        
        # Note prediction loss
        self.note_criterion = nn.CrossEntropyLoss(ignore_index=0, reduction='none')
        
        # Timing loss
        self.timing_criterion = nn.MSELoss(reduction='none')
        
        # Pattern coherence loss
        self.pattern_criterion = nn.MSELoss(reduction='none')
        
        # Difficulty consistency loss
        self.difficulty_criterion = nn.KLDivLoss(reduction='none')
        
        # Note type weights (to handle class imbalance)
        self.register_buffer(
            "note_weights",
            torch.tensor([0.1, 1.0, 1.0, 1.5, 1.5, 2.0, 2.0, 3.0])  # Weights for note types 0-7
        )
        
    def forward(self, predictions: Dict[str, torch.Tensor],
                targets: Dict[str, torch.Tensor],
                audio_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Compute comprehensive TJA generation loss
        
        Args:
            predictions: Dictionary containing model predictions
                - logits: [batch, seq_len, vocab_size] note prediction logits
                - timing_pred: [batch, seq_len] predicted timing (optional)
                - hidden_states: [batch, seq_len, hidden_dim] decoder hidden states
            targets: Dictionary containing ground truth targets
                - note_sequence: [batch, seq_len] target note sequence
                - timing_sequence: [batch, seq_len] target timing sequence
                - difficulty: [batch] difficulty level
                - attention_mask: [batch, seq_len] sequence mask
            audio_features: [batch, audio_len, feature_dim] audio features (optional)
            
        Returns:
            Dictionary containing individual and total losses
        """
        # Extract predictions and targets
        logits = predictions["logits"]
        note_targets = targets["note_sequence"]
        timing_targets = targets.get("timing_sequence")
        difficulty = targets.get("difficulty")
        attention_mask = targets.get("attention_mask")
        
        batch_size, seq_len, vocab_size = logits.shape
        
        # 1. Note prediction loss
        note_loss = self._compute_note_loss(logits, note_targets, attention_mask)
        
        # 2. Timing consistency loss
        timing_loss = self._compute_timing_loss(
            predictions, timing_targets, attention_mask
        )
        
        # 3. Difficulty appropriateness loss
        difficulty_loss = self._compute_difficulty_loss(
            logits, note_targets, difficulty, attention_mask
        )
        
        # 4. Pattern coherence loss
        pattern_loss = self._compute_pattern_loss(
            predictions, targets, attention_mask
        )
        
        # 5. Musical structure loss
        structure_loss = self._compute_structure_loss(
            predictions, targets, attention_mask
        )
        
        # Combine losses
        total_loss = (
            self.note_loss_weight * note_loss +
            self.timing_loss_weight * timing_loss +
            self.difficulty_loss_weight * difficulty_loss +
            self.pattern_loss_weight * pattern_loss +
            self.structure_loss_weight * structure_loss
        )
        
        return {
            "total_loss": total_loss,
            "note_loss": note_loss,
            "timing_loss": timing_loss,
            "difficulty_loss": difficulty_loss,
            "pattern_loss": pattern_loss,
            "structure_loss": structure_loss,
            "loss_components": {
                "note_weight": self.note_loss_weight,
                "timing_weight": self.timing_loss_weight,
                "difficulty_weight": self.difficulty_loss_weight,
                "pattern_weight": self.pattern_loss_weight,
                "structure_weight": self.structure_loss_weight
            }
        }
    
    def _compute_note_loss(self, logits: torch.Tensor, targets: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Compute weighted cross-entropy loss for note prediction"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # Flatten for loss computation
        logits_flat = logits.view(-1, vocab_size)
        targets_flat = targets.view(-1)
        
        # Compute cross-entropy loss
        loss_flat = self.note_criterion(logits_flat, targets_flat)
        loss = loss_flat.view(batch_size, seq_len)
        
        # Apply note type weights
        note_weights_expanded = self.note_weights[targets.clamp(0, self.vocab_size - 1)]
        loss = loss * note_weights_expanded
        
        # Apply sequence mask
        if mask is not None:
            loss = loss * mask.float()
            return loss.sum() / mask.sum().clamp(min=1)
        else:
            return loss.mean()
    
    def _compute_timing_loss(self, predictions: Dict[str, torch.Tensor],
                           timing_targets: Optional[torch.Tensor],
                           mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Compute timing consistency loss"""
        if timing_targets is None or "timing_pred" not in predictions:
            return torch.tensor(0.0, device=predictions["logits"].device)
        
        timing_pred = predictions["timing_pred"]
        
        # MSE loss between predicted and target timing
        loss = self.timing_criterion(timing_pred, timing_targets.float())
        
        # Apply mask
        if mask is not None:
            loss = loss * mask.float()
            return loss.sum() / mask.sum().clamp(min=1)
        else:
            return loss.mean()
    
    def _compute_difficulty_loss(self, logits: torch.Tensor, note_targets: torch.Tensor,
                               difficulty: Optional[torch.Tensor],
                               mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Compute difficulty appropriateness loss"""
        if difficulty is None:
            return torch.tensor(0.0, device=logits.device)
        
        batch_size, seq_len = note_targets.shape
        
        # Define expected note distributions for each difficulty level
        # Oni 8 (difficulty 0): More basic patterns
        # Oni 9 (difficulty 1): Balanced patterns  
        # Oni 10 (difficulty 2): More complex patterns
        difficulty_distributions = torch.tensor([
            [0.4, 0.25, 0.25, 0.05, 0.03, 0.01, 0.01, 0.0],  # Oni 8
            [0.3, 0.2, 0.2, 0.1, 0.1, 0.05, 0.03, 0.02],     # Oni 9
            [0.2, 0.15, 0.15, 0.15, 0.15, 0.1, 0.05, 0.05]   # Oni 10
        ], device=logits.device)
        
        # Get predicted note distributions
        note_probs = F.softmax(logits, dim=-1)  # [batch, seq_len, vocab_size]
        
        # Average over sequence length to get overall distribution
        avg_note_probs = note_probs.mean(dim=1)  # [batch, vocab_size]
        
        # Get target distributions based on difficulty
        target_distributions = difficulty_distributions[difficulty]  # [batch, vocab_size]
        
        # KL divergence loss
        log_pred = F.log_softmax(avg_note_probs, dim=-1)
        kl_loss = self.difficulty_criterion(log_pred, target_distributions)
        
        return kl_loss.sum(dim=-1).mean()
    
    def _compute_pattern_loss(self, predictions: Dict[str, torch.Tensor],
                            targets: Dict[str, torch.Tensor],
                            mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Compute pattern coherence loss"""
        if "hidden_states" not in predictions:
            return torch.tensor(0.0, device=predictions["logits"].device)
        
        hidden_states = predictions["hidden_states"]
        note_targets = targets["note_sequence"]
        
        # Compute pattern consistency by checking local coherence
        # This is a simplified version - could be more sophisticated
        
        # Look at 4-note patterns
        pattern_length = 4
        if hidden_states.shape[1] < pattern_length:
            return torch.tensor(0.0, device=hidden_states.device)
        
        # Extract overlapping patterns
        pattern_losses = []
        for i in range(hidden_states.shape[1] - pattern_length + 1):
            pattern_hidden = hidden_states[:, i:i+pattern_length, :]
            pattern_notes = note_targets[:, i:i+pattern_length]
            
            # Compute pattern embedding
            pattern_emb = pattern_hidden.mean(dim=1)  # [batch, hidden_dim]
            
            # Check consistency with neighboring patterns
            if i > 0:
                prev_pattern_emb = prev_pattern_emb  # From previous iteration
                consistency_loss = F.mse_loss(pattern_emb, prev_pattern_emb, reduction='none')
                pattern_losses.append(consistency_loss.mean(dim=-1))
            
            prev_pattern_emb = pattern_emb
        
        if pattern_losses:
            return torch.stack(pattern_losses).mean()
        else:
            return torch.tensor(0.0, device=hidden_states.device)
    
    def _compute_structure_loss(self, predictions: Dict[str, torch.Tensor],
                              targets: Dict[str, torch.Tensor],
                              mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Compute musical structure loss (beat/measure alignment)"""
        if "temporal_attention" not in predictions:
            return torch.tensor(0.0, device=predictions["logits"].device)
        
        # This would check if the generated sequence follows musical structure
        # For now, return a placeholder
        return torch.tensor(0.0, device=predictions["logits"].device)
    
    def compute_accuracy_metrics(self, predictions: Dict[str, torch.Tensor],
                                targets: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """Compute accuracy metrics for evaluation"""
        logits = predictions["logits"]
        note_targets = targets["note_sequence"]
        attention_mask = targets.get("attention_mask")
        
        # Note prediction accuracy
        predicted_notes = torch.argmax(logits, dim=-1)
        correct_predictions = (predicted_notes == note_targets).float()
        
        if attention_mask is not None:
            correct_predictions = correct_predictions * attention_mask.float()
            note_accuracy = correct_predictions.sum() / attention_mask.sum()
        else:
            note_accuracy = correct_predictions.mean()
        
        # Per-class accuracy
        per_class_accuracy = {}
        for note_type in range(self.vocab_size):
            mask = (note_targets == note_type)
            if mask.sum() > 0:
                class_correct = ((predicted_notes == note_type) & mask).float().sum()
                class_total = mask.float().sum()
                per_class_accuracy[f"note_{note_type}_accuracy"] = (class_correct / class_total).item()
        
        # Sequence-level accuracy (exact match)
        if attention_mask is not None:
            seq_correct = (correct_predictions * attention_mask.float()).sum(dim=1) == attention_mask.sum(dim=1).float()
        else:
            seq_correct = correct_predictions.sum(dim=1) == correct_predictions.shape[1]
        
        sequence_accuracy = seq_correct.float().mean()
        
        metrics = {
            "note_accuracy": note_accuracy.item(),
            "sequence_accuracy": sequence_accuracy.item(),
            **per_class_accuracy
        }
        
        return metrics


class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance in note prediction"""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: [batch, num_classes] logits
            targets: [batch] class indices
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class TemporalConsistencyLoss(nn.Module):
    """Loss for enforcing temporal consistency in generated sequences"""
    
    def __init__(self, window_size: int = 8):
        super().__init__()
        self.window_size = window_size
    
    def forward(self, hidden_states: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute temporal consistency loss
        
        Args:
            hidden_states: [batch, seq_len, hidden_dim] decoder hidden states
            mask: [batch, seq_len] sequence mask
        """
        batch_size, seq_len, hidden_dim = hidden_states.shape
        
        if seq_len < self.window_size:
            return torch.tensor(0.0, device=hidden_states.device)
        
        # Compute differences between adjacent windows
        consistency_losses = []
        
        for i in range(seq_len - self.window_size + 1):
            window = hidden_states[:, i:i+self.window_size, :]
            
            if i > 0:
                prev_window = hidden_states[:, i-1:i+self.window_size-1, :]
                
                # Compute similarity between overlapping regions
                overlap_curr = window[:, :-1, :]
                overlap_prev = prev_window[:, 1:, :]
                
                consistency_loss = F.mse_loss(overlap_curr, overlap_prev, reduction='none')
                consistency_losses.append(consistency_loss.mean(dim=(1, 2)))
        
        if consistency_losses:
            total_loss = torch.stack(consistency_losses).mean()
            return total_loss
        else:
            return torch.tensor(0.0, device=hidden_states.device)
