from typing import (
    Any,
    Dict,
    List,
    Optional
)
"""
Phase 5 Training Configuration

Comprehensive training configuration system that integrates with existing
Phase 1-4 components and optimizes for RTX 3070 hardware constraints.
"""

from dataclasses import dataclass, field
import torch
from pathlib import Path

from ..model import PHASE_3_MODEL_CONFIG
from ..training import PHASE_3_TRAINING_CONFIG


@dataclass
class Phase5TrainingConfig:
    """
    Comprehensive Phase 5 training configuration
    
    Integrates with existing Phase 3 model and training configurations
    while adding advanced optimization features.
    """
    
    # Model integration
    base_model_config: Dict[str, Any] = field(default_factory=lambda: PHASE_3_MODEL_CONFIG["architecture"])
    base_training_config: Dict[str, Any] = field(default_factory=lambda: PHASE_3_TRAINING_CONFIG)
    
    # Phase 5 specific parameters
    model_name: str = "tja_generation_v2_optimized"
    experiment_name: str = "phase5_optimization"
    checkpoint_dir: str = "outputs/phase5_training"
    
    # Training hyperparameters (RTX 3070 optimized)
    batch_size: int = 2  # Conservative for 8GB VRAM
    gradient_accumulation_steps: int = 8  # Effective batch size = 16
    learning_rate: float = 1e-4
    weight_decay: float = 0.01
    max_grad_norm: float = 1.0
    
    # Learning rate scheduling
    warmup_steps: int = 1000
    total_training_steps: int = 50000
    lr_scheduler: str = "cosine_with_warmup"
    min_lr_ratio: float = 0.1
    
    # Loss function weights (adaptive)
    loss_weights: Dict[str, float] = field(default_factory=lambda: {
        'note_type': 1.0,
        'timing': 0.8,
        'pattern': 0.6,
        'difficulty': 0.4,
        'density': 0.3
    })
    
    # Optimization settings
    mixed_precision: bool = True
    gradient_checkpointing: bool = True
    compile_model: bool = True  # PyTorch 2.0 compilation
    use_fused_optimizer: bool = True
    
    # Validation and checkpointing
    validation_frequency: int = 500  # Steps
    checkpoint_frequency: int = 2000  # Steps
    early_stopping_patience: int = 10  # Validation cycles
    save_top_k_checkpoints: int = 3
    
    # Data augmentation
    use_data_augmentation: bool = True
    augmentation_probability: float = 0.3
    augmentation_strategies: List[str] = field(default_factory=lambda: [
        "tempo_variation", "pitch_shift", "noise_injection", 
        "time_masking", "frequency_masking", "mixup"
    ])
    
    # Regularization
    dropout_rate: float = 0.1
    label_smoothing: float = 0.1
    weight_noise_std: float = 0.01
    
    # Hardware optimization
    num_workers: int = 4  # Reduced for intensive GPU training
    pin_memory: bool = True
    persistent_workers: bool = True
    prefetch_factor: int = 2
    
    # Advanced training strategies
    use_curriculum_learning: bool = True
    use_adaptive_loss_weighting: bool = True
    use_progressive_resizing: bool = False  # Disabled for sequence data
    use_mixup: bool = True
    mixup_alpha: float = 0.2
    
    # Monitoring and logging
    log_frequency: int = 100
    wandb_project: str = "tja-generation-phase5"
    wandb_tags: List[str] = field(default_factory=lambda: ["phase5", "optimization", "rtx3070"])
    
    def __post_init__(self):
        """Post-initialization validation and setup"""
        # Ensure checkpoint directory exists
        Path(self.checkpoint_dir).mkdir(parents=True, exist_ok=True)
        
        # Validate hardware constraints
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if gpu_memory_gb < 7.0:  # Less than expected RTX 3070 memory
                # Reduce batch size for lower memory GPUs
                self.batch_size = max(1, self.batch_size // 2)
                self.gradient_accumulation_steps *= 2
        
        # Adjust total steps based on effective batch size
        effective_batch_size = self.batch_size * self.gradient_accumulation_steps
        if effective_batch_size < 16:
            # Increase total steps for smaller effective batch sizes
            self.total_training_steps = int(self.total_training_steps * (16 / effective_batch_size))
    
    def get_effective_batch_size(self) -> int:
        """Get effective batch size considering gradient accumulation"""
        return self.batch_size * self.gradient_accumulation_steps
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get integrated model configuration"""
        config = self.base_model_config.copy()
        
        # Apply Phase 5 specific modifications
        config.update({
            "dropout": self.dropout_rate,
            "gradient_checkpointing": self.gradient_checkpointing,
            "mixed_precision": self.mixed_precision
        })
        
        return config
    
    def get_training_config(self) -> Dict[str, Any]:
        """Get integrated training configuration"""
        config = self.base_training_config.copy()
        
        # Update with Phase 5 parameters
        config.update({
            "optimization": {
                "learning_rate": self.learning_rate,
                "weight_decay": self.weight_decay,
                "max_grad_norm": self.max_grad_norm,
                "warmup_steps": self.warmup_steps,
                "scheduler_type": self.lr_scheduler,
                "use_fused_optimizer": self.use_fused_optimizer
            },
            "data": {
                "batch_size": self.batch_size,
                "num_workers": self.num_workers,
                "pin_memory": self.pin_memory,
                "persistent_workers": self.persistent_workers,
                "prefetch_factor": self.prefetch_factor,
                "max_sequence_length": 400,  # Maximum sequence length
                "train_split": 0.7,
                "val_split": 0.2,
                "test_split": 0.1,
                "shuffle": True
            },
            "regularization": {
                "dropout_rate": self.dropout_rate,
                "label_smoothing": self.label_smoothing,
                "weight_noise_std": self.weight_noise_std
            }
        })
        
        return config


@dataclass 
class OptimizationConfig:
    """Configuration for advanced optimization techniques"""
    
    # Hyperparameter optimization
    use_hyperparameter_optimization: bool = True
    optimization_trials: int = 50
    optimization_timeout_hours: int = 24
    
    # Curriculum learning
    curriculum_stages: List[Dict[str, Any]] = field(default_factory=lambda: [
        {
            "name": "basic_patterns",
            "steps": 10000,
            "difficulty_focus": [0],  # Oni 8 only
            "max_sequence_length": 200,
            "loss_weights": {"note_type": 1.0, "timing": 0.5, "pattern": 0.3}
        },
        {
            "name": "intermediate_patterns", 
            "steps": 20000,
            "difficulty_focus": [0, 1],  # Oni 8-9
            "max_sequence_length": 300,
            "loss_weights": {"note_type": 1.0, "timing": 0.7, "pattern": 0.5}
        },
        {
            "name": "advanced_patterns",
            "steps": 20000, 
            "difficulty_focus": [0, 1, 2],  # Oni 8-10
            "max_sequence_length": 400,
            "loss_weights": {"note_type": 1.0, "timing": 0.8, "pattern": 0.6}
        }
    ])
    
    # Adaptive loss weighting
    adaptive_loss_update_frequency: int = 1000
    loss_weight_momentum: float = 0.9
    loss_weight_adaptation_rate: float = 0.1
    
    # Model ensemble
    ensemble_size: int = 3
    ensemble_diversity_strategies: List[str] = field(default_factory=lambda: [
        "different_augmentation",
        "different_loss_weights", 
        "different_architectures"
    ])
    
    # Knowledge distillation
    teacher_model_path: Optional[str] = None
    distillation_temperature: float = 3.0
    distillation_alpha: float = 0.7
    
    # Performance profiling
    enable_profiling: bool = True
    profile_memory: bool = True
    profile_compute: bool = True
    profiling_frequency: int = 1000
    
    def get_current_curriculum_stage(self, current_step: int) -> Dict[str, Any]:
        """Get current curriculum learning stage based on training step"""
        cumulative_steps = 0
        
        for stage in self.curriculum_stages:
            cumulative_steps += stage["steps"]
            if current_step < cumulative_steps:
                return stage
        
        # Return final stage if beyond all stages
        return self.curriculum_stages[-1]


def create_phase5_config(
    experiment_name: str = "phase5_optimization",
    batch_size: Optional[int] = None,
    learning_rate: Optional[float] = None,
    use_curriculum: bool = True,
    use_augmentation: bool = True
) -> Phase5TrainingConfig:
    """
    Create Phase 5 training configuration with optional overrides
    
    Args:
        experiment_name: Name for the training experiment
        batch_size: Override default batch size
        learning_rate: Override default learning rate
        use_curriculum: Enable curriculum learning
        use_augmentation: Enable data augmentation
        
    Returns:
        Configured Phase5TrainingConfig instance
    """
    config = Phase5TrainingConfig(experiment_name=experiment_name)
    
    # Apply overrides
    if batch_size is not None:
        config.batch_size = batch_size
    
    if learning_rate is not None:
        config.learning_rate = learning_rate
    
    config.use_curriculum_learning = use_curriculum
    config.use_data_augmentation = use_augmentation
    
    return config


def validate_hardware_compatibility(config: Phase5TrainingConfig) -> Dict[str, Any]:
    """
    Validate hardware compatibility and suggest optimizations
    
    Args:
        config: Phase 5 training configuration
        
    Returns:
        Dictionary with validation results and recommendations
    """
    validation_results = {
        "compatible": True,
        "warnings": [],
        "recommendations": [],
        "estimated_memory_usage": {}
    }
    
    if not torch.cuda.is_available():
        validation_results["compatible"] = False
        validation_results["warnings"].append("CUDA not available - training will be very slow on CPU")
        return validation_results
    
    # Check GPU memory
    gpu_props = torch.cuda.get_device_properties(0)
    gpu_memory_gb = gpu_props.total_memory / (1024**3)
    
    # Estimate memory usage
    model_params = 1.56e6  # From Phase 3 model
    model_memory = model_params * 4 / (1024**3)  # 4 bytes per float32
    
    batch_memory = (
        config.batch_size * 400 * 201 * 4 +  # Audio features
        config.batch_size * 400 * 4 +        # Note sequences
        config.batch_size * 400 * 4           # Timing sequences
    ) / (1024**3)
    
    gradient_memory = model_memory * 2  # Gradients + optimizer states
    activation_memory = batch_memory * 8  # Rough estimate for activations
    
    total_estimated = model_memory + batch_memory + gradient_memory + activation_memory
    
    validation_results["estimated_memory_usage"] = {
        "model_memory_gb": model_memory,
        "batch_memory_gb": batch_memory,
        "gradient_memory_gb": gradient_memory,
        "activation_memory_gb": activation_memory,
        "total_estimated_gb": total_estimated
    }
    
    # Check if estimated usage exceeds available memory
    if total_estimated > gpu_memory_gb * 0.9:  # 90% threshold
        validation_results["warnings"].append(
            f"Estimated memory usage ({total_estimated:.2f}GB) may exceed available GPU memory ({gpu_memory_gb:.2f}GB)"
        )
        validation_results["recommendations"].append("Consider reducing batch size or enabling gradient checkpointing")
    
    # Check GPU compute capability
    if gpu_props.major < 7:  # Pre-Volta architecture
        validation_results["warnings"].append("GPU compute capability < 7.0 - mixed precision may not be optimal")
        validation_results["recommendations"].append("Consider disabling mixed precision training")
    
    return validation_results
