"""
Unified Audio Processing Module

Consolidates all audio processing functionality from multiple phases into a single,
consistent system eliminating redundancy and implementing SOLID principles.
"""

from .unified_audio_processor import (
    UnifiedAudioProcessor,
    AudioProcessingConfig,
    SpectralFeatureExtractor,
    RhythmicFeatureExtractor,
    TemporalFeatureExtractor,
    AudioFeatureExtractorInterface
)

__all__ = [
    'UnifiedAudioProcessor',
    'AudioProcessingConfig',
    'SpectralFeatureExtractor',
    'RhythmicFeatureExtractor', 
    'TemporalFeatureExtractor',
    'AudioFeatureExtractorInterface'
]
