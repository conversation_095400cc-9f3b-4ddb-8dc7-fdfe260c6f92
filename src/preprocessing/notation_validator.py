"""
Notation Data Validator

Validates notation data quality and training readiness.
Ensures clean, consistent training data without metadata contamination.
"""

from typing import Any, Dict, List
from dataclasses import dataclass
import logging


@dataclass
class ValidationResult:
    """Result of notation data validation"""
    valid: bool
    warnings: List[str]
    errors: List[str]
    quality_score: float
    training_ready: bool
    metrics: Dict[str, Any]


class NotationValidator:
    """Validates notation data for training suitability"""
    
    # Valid note types according to TJA specification
    VALID_NOTE_TYPES = {
        "blank", "don", "ka", "don_big", "ka_big", "drumroll", 
        "drumroll_big", "balloon", "end_roll", "kusudama", 
        "don_both", "ka_both", "adlib"
    }
    
    # Valid timing command types for training
    VALID_TIMING_COMMANDS = {
        "BPMCHANGE", "MEASURE", "SCROLL", "GOGOSTART", "GOGOEND",
        "DELAY", "BRANCHSTART", "BRANCHEND", "N", "E", "M"
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_notation_data(self, notation_data: Dict) -> ValidationResult:
        """
        Validate notation data for training suitability
        
        Args:
            notation_data: Dictionary containing notation data for all difficulties
            
        Returns:
            ValidationResult with validation status and metrics
        """
        warnings = []
        errors = []
        quality_scores = []
        metrics = {}
        
        if not notation_data:
            errors.append("No notation data provided")
            return ValidationResult(
                valid=False,
                warnings=warnings,
                errors=errors,
                quality_score=0.0,
                training_ready=False,
                metrics={}
            )
        
        # Validate each difficulty
        for difficulty, data in notation_data.items():
            diff_result = self._validate_difficulty_data(difficulty, data)
            
            warnings.extend([f"{difficulty}: {w}" for w in diff_result.warnings])
            errors.extend([f"{difficulty}: {e}" for e in diff_result.errors])
            quality_scores.append(diff_result.quality_score)
            metrics[difficulty] = diff_result.metrics
        
        # Calculate overall quality
        overall_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        
        # Determine if valid and training ready
        valid = len(errors) == 0
        training_ready = valid and overall_quality >= 0.8
        
        return ValidationResult(
            valid=valid,
            warnings=warnings,
            errors=errors,
            quality_score=overall_quality,
            training_ready=training_ready,
            metrics=metrics
        )
    
    def _validate_difficulty_data(self, difficulty: str, data: Dict) -> ValidationResult:
        """Validate data for a specific difficulty"""
        warnings = []
        errors = []
        metrics = {}
        
        # Check data structure
        if 'notation' not in data:
            errors.append("Missing notation data")
            return ValidationResult(
                valid=False,
                warnings=warnings,
                errors=errors,
                quality_score=0.0,
                training_ready=False,
                metrics={}
            )
        
        notation = data['notation']
        
        # Validate note sequences
        sequence_result = self._validate_note_sequences(notation.get('note_sequences', []))
        warnings.extend(sequence_result['warnings'])
        errors.extend(sequence_result['errors'])
        metrics['sequences'] = sequence_result['metrics']
        
        # Validate timing commands
        timing_result = self._validate_timing_commands(notation.get('timing_commands', []))
        warnings.extend(timing_result['warnings'])
        errors.extend(timing_result['errors'])
        metrics['timing'] = timing_result['metrics']
        
        # Validate pattern complexity
        pattern_result = self._validate_pattern_complexity(notation.get('pattern_features', {}))
        warnings.extend(pattern_result['warnings'])
        errors.extend(pattern_result['errors'])
        metrics['patterns'] = pattern_result['metrics']
        
        # Calculate difficulty quality score
        quality_score = self._calculate_difficulty_quality_score(
            sequence_result, timing_result, pattern_result
        )
        
        return ValidationResult(
            valid=len(errors) == 0,
            warnings=warnings,
            errors=errors,
            quality_score=quality_score,
            training_ready=len(errors) == 0 and quality_score >= 0.7,
            metrics=metrics
        )
    
    def _validate_note_sequences(self, sequences: List[Dict]) -> Dict:
        """Validate note sequence quality"""
        warnings = []
        errors = []
        metrics = {}
        
        if not sequences:
            errors.append("No note sequences found")
            return {
                'warnings': warnings,
                'errors': errors,
                'metrics': {'total_notes': 0, 'valid_notes': 0, 'invalid_notes': 0}
            }
        
        valid_notes = 0
        invalid_notes = 0
        timing_issues = []
        
        # Validate each note
        for i, note in enumerate(sequences):
            # Check required fields
            required_fields = ['type', 'position', 'measure', 'timing_ms']
            missing_fields = [field for field in required_fields if field not in note]
            
            if missing_fields:
                errors.append(f"Note {i} missing fields: {missing_fields}")
                invalid_notes += 1
                continue
            
            # Validate note type
            note_type = note.get('type')
            if note_type not in self.VALID_NOTE_TYPES:
                warnings.append(f"Unknown note type: {note_type}")
            
            # Validate position (should be 0.0 to 1.0)
            position = note.get('position', 0)
            if not (0.0 <= position <= 1.0):
                warnings.append(f"Note position out of range: {position}")
            
            # Validate timing consistency
            if i > 0:
                prev_timing = sequences[i-1].get('timing_ms', 0)
                current_timing = note.get('timing_ms', 0)
                if current_timing < prev_timing:
                    timing_issues.append(f"Timing regression at note {i}: {current_timing} < {prev_timing}")
            
            valid_notes += 1
        
        # Add timing issues to errors if significant
        if len(timing_issues) > len(sequences) * 0.1:  # More than 10% timing issues
            errors.extend(timing_issues[:5])  # Report first 5
            if len(timing_issues) > 5:
                errors.append(f"... and {len(timing_issues) - 5} more timing issues")
        
        metrics = {
            'total_notes': len(sequences),
            'valid_notes': valid_notes,
            'invalid_notes': invalid_notes,
            'timing_issues': len(timing_issues)
        }
        
        return {
            'warnings': warnings,
            'errors': errors,
            'metrics': metrics
        }
    
    def _validate_timing_commands(self, commands: List[Dict]) -> Dict:
        """Validate timing command consistency"""
        warnings = []
        errors = []
        metrics = {}
        
        valid_commands = 0
        invalid_commands = 0
        bpm_changes = []
        scroll_changes = []
        
        for i, cmd in enumerate(commands):
            # Check required fields
            if 'type' not in cmd:
                errors.append(f"Command {i} missing type field")
                invalid_commands += 1
                continue
            
            cmd_type = cmd['type']
            
            # Validate command type
            if cmd_type not in self.VALID_TIMING_COMMANDS:
                warnings.append(f"Unknown command type: {cmd_type}")
            
            # Validate command-specific values
            if cmd_type == 'BPMCHANGE':
                value = cmd.get('value', 0)
                if not (60 <= value <= 300):  # Reasonable BPM range
                    warnings.append(f"Unusual BPM value: {value}")
                bpm_changes.append(value)
            
            elif cmd_type == 'SCROLL':
                value = cmd.get('value', 1)
                if not (0.1 <= value <= 10):  # Reasonable scroll range
                    warnings.append(f"Unusual scroll value: {value}")
                scroll_changes.append(value)
            
            valid_commands += 1
        
        metrics = {
            'total_commands': len(commands),
            'valid_commands': valid_commands,
            'invalid_commands': invalid_commands,
            'bpm_changes': len(bpm_changes),
            'scroll_changes': len(scroll_changes)
        }
        
        return {
            'warnings': warnings,
            'errors': errors,
            'metrics': metrics
        }
    
    def _validate_pattern_complexity(self, pattern_features: Dict) -> Dict:
        """Validate pattern complexity for training value"""
        warnings = []
        errors = []
        metrics = {}
        
        if not pattern_features:
            warnings.append("No pattern features found")
            return {
                'warnings': warnings,
                'errors': errors,
                'metrics': {'complexity_score': 0.0, 'pattern_count': 0}
            }
        
        complexity_score = pattern_features.get('complexity_score', 0.0)
        common_patterns = pattern_features.get('common_patterns', [])
        
        # Check complexity range
        if complexity_score < 0.1:
            warnings.append(f"Very low pattern complexity: {complexity_score}")
        elif complexity_score > 5.0:
            warnings.append(f"Very high pattern complexity: {complexity_score}")
        
        # Check pattern count
        if len(common_patterns) < 5:
            warnings.append(f"Few patterns detected: {len(common_patterns)}")
        
        optimal_complexity = 0.5 <= complexity_score <= 2.0

        metrics = {
            'complexity_score': complexity_score,
            'pattern_count': len(common_patterns),
            'optimal_complexity': optimal_complexity
        }
        
        return {
            'warnings': warnings,
            'errors': errors,
            'metrics': metrics
        }
    
    def _calculate_difficulty_quality_score(self, sequence_result: Dict, 
                                          timing_result: Dict, pattern_result: Dict) -> float:
        """Calculate overall quality score for a difficulty"""
        scores = []
        
        # Sequence quality (40% weight)
        seq_metrics = sequence_result['metrics']
        if seq_metrics['total_notes'] > 0:
            seq_score = seq_metrics['valid_notes'] / seq_metrics['total_notes']
            # Penalize timing issues
            if seq_metrics['timing_issues'] > 0:
                timing_penalty = min(seq_metrics['timing_issues'] / seq_metrics['total_notes'], 0.5)
                seq_score *= (1.0 - timing_penalty)
            scores.append(seq_score * 0.4)
        
        # Timing quality (30% weight)
        timing_metrics = timing_result['metrics']
        if timing_metrics['total_commands'] > 0:
            timing_score = timing_metrics['valid_commands'] / timing_metrics['total_commands']
            scores.append(timing_score * 0.3)
        else:
            scores.append(0.3)  # No commands is acceptable
        
        # Pattern quality (30% weight)
        pattern_metrics = pattern_result['metrics']
        complexity = pattern_metrics['complexity_score']
        if pattern_metrics['optimal_complexity']:
            pattern_score = 1.0
        else:
            # Score based on distance from optimal range
            if complexity < 0.5:
                pattern_score = complexity / 0.5
            else:  # complexity > 2.0
                pattern_score = max(0.0, 1.0 - (complexity - 2.0) / 3.0)
        scores.append(pattern_score * 0.3)
        
        return sum(scores)
