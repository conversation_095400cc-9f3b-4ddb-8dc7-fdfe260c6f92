from typing import (
    Any,
    Callable,
    Dict,
    List,
    Optional
)
"""
Advanced Performance Optimization System

Implements advanced performance optimizations, caching strategies,
and real-time performance monitoring for the TJA Generator system.
"""

import time
import logging
import threading
from pathlib import Path
from dataclasses import dataclass, field
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor
import pickle
import hashlib

from ..utils.base_classes import BaseProcessor, ProcessingResult
from ..config.unified_config_manager import UnifiedConfigManager
from ..paths.path_manager import PathManager, PathType


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    operation_name: str
    execution_time_seconds: float
    memory_usage_mb: float
    cpu_usage_percent: float
    cache_hit_rate: float
    throughput_items_per_second: float
    timestamp: float = field(default_factory=time.time)


@dataclass
class OptimizationProfile:
    """Optimization profile for different hardware configurations"""
    profile_name: str
    max_workers: int
    batch_size: int
    cache_size_mb: int
    memory_threshold: float
    enable_gpu_acceleration: bool
    enable_multiprocessing: bool
    prefetch_enabled: bool


class PerformanceCache:
    """Advanced caching system with LRU and persistence"""
    
    def __init__(self, max_size_mb: int = 500, cache_dir: Optional[Path] = None):
        self.max_size_mb = max_size_mb
        self.cache_dir = cache_dir or Path("cache/performance")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self._memory_cache = {}
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "current_size_mb": 0
        }
        
        self.logger = logging.getLogger(f"{__name__}.PerformanceCache")
        
        # Load persistent cache
        self._load_persistent_cache()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        # Check memory cache first
        if key in self._memory_cache:
            self._cache_stats["hits"] += 1
            self._memory_cache[key]["last_accessed"] = time.time()
            return self._memory_cache[key]["data"]
        
        # Check persistent cache
        cache_file = self.cache_dir / f"{self._hash_key(key)}.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                
                # Move to memory cache
                self._memory_cache[key] = {
                    "data": data,
                    "size_mb": cache_file.stat().st_size / (1024 * 1024),
                    "last_accessed": time.time()
                }
                
                self._cache_stats["hits"] += 1
                return data
                
            except Exception as e:
                self.logger.warning(f"Failed to load cache file {cache_file}: {e}")
        
        self._cache_stats["misses"] += 1
        return None
    
    def put(self, key: str, data: Any, persist: bool = True):
        """Put item in cache"""
        # Calculate size
        try:
            serialized = pickle.dumps(data)
            size_mb = len(serialized) / (1024 * 1024)
        except Exception:
            self.logger.warning(f"Cannot serialize data for key {key}")
            return
        
        # Check if we need to evict
        self._ensure_cache_space(size_mb)
        
        # Store in memory cache
        self._memory_cache[key] = {
            "data": data,
            "size_mb": size_mb,
            "last_accessed": time.time()
        }
        
        self._cache_stats["current_size_mb"] += size_mb
        
        # Store in persistent cache if requested
        if persist:
            cache_file = self.cache_dir / f"{self._hash_key(key)}.pkl"
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(data, f)
            except Exception as e:
                self.logger.warning(f"Failed to persist cache for key {key}: {e}")
    
    def _ensure_cache_space(self, required_mb: float):
        """Ensure enough cache space by evicting old items"""
        while (self._cache_stats["current_size_mb"] + required_mb) > self.max_size_mb:
            if not self._memory_cache:
                break
            
            # Find least recently used item
            lru_key = min(
                self._memory_cache.keys(),
                key=lambda k: self._memory_cache[k]["last_accessed"]
            )
            
            # Evict item
            evicted_size = self._memory_cache[lru_key]["size_mb"]
            del self._memory_cache[lru_key]
            self._cache_stats["current_size_mb"] -= evicted_size
            self._cache_stats["evictions"] += 1
    
    def _hash_key(self, key: str) -> str:
        """Generate hash for cache key"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def _load_persistent_cache(self):
        """Load persistent cache files"""
        if not self.cache_dir.exists():
            return
        
        for cache_file in self.cache_dir.glob("*.pkl"):
            if self._cache_stats["current_size_mb"] >= self.max_size_mb:
                break
            
            try:
                size_mb = cache_file.stat().st_size / (1024 * 1024)
                if size_mb <= (self.max_size_mb - self._cache_stats["current_size_mb"]):
                    # File will be loaded on first access
                    self._cache_stats["current_size_mb"] += size_mb
            except Exception:
                continue
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self._cache_stats["hits"] + self._cache_stats["misses"]
        hit_rate = self._cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            **self._cache_stats,
            "hit_rate": hit_rate,
            "items_in_memory": len(self._memory_cache)
        }


class PerformanceOptimizer(BaseProcessor):
    """
    Advanced performance optimization system
    
    Provides caching, parallel processing, and performance monitoring
    for all TJA Generator components.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "PerformanceOptimizer")
        
        # Initialize configuration
        self.config_manager = UnifiedConfigManager()
        self.path_manager = PathManager()
        
        # Initialize optimization profile
        self.optimization_profile = self._detect_optimization_profile()
        
        # Initialize performance cache
        cache_size = self.optimization_profile.cache_size_mb
        cache_dir = self.path_manager.get_standardized_path(PathType.CACHE, "performance")
        self.cache = PerformanceCache(cache_size, cache_dir)
        
        # Initialize thread pools
        self.thread_pool = ThreadPoolExecutor(
            max_workers=self.optimization_profile.max_workers
        )
        
        if self.optimization_profile.enable_multiprocessing:
            self.process_pool = ProcessPoolExecutor(
                max_workers=min(4, self.optimization_profile.max_workers // 2)
            )
        else:
            self.process_pool = None
        
        # Performance metrics storage
        self.metrics_history: List[PerformanceMetrics] = []
        self.metrics_lock = threading.Lock()
        
        self.logger.info(f"PerformanceOptimizer initialized with profile: {self.optimization_profile.profile_name}")
    
    def _detect_optimization_profile(self) -> OptimizationProfile:
        """Detect optimal performance profile based on hardware"""
        try:
            from ..utils.hardware_monitor import get_system_info
            system_info = get_system_info()
            
            # Get hardware specs
            memory_gb = system_info.get("memory", {}).get("total_gb", 16)
            cpu_cores = system_info.get("cpu", {}).get("logical_cores", 8)
            gpu_info = system_info.get("gpu", {})
            
            # RTX 3070 optimized profile
            if "RTX 3070" in str(gpu_info.get("name", "")):
                return OptimizationProfile(
                    profile_name="RTX_3070_Optimized",
                    max_workers=min(cpu_cores - 2, 14),
                    batch_size=8,
                    cache_size_mb=1024,  # 1GB cache
                    memory_threshold=0.75,
                    enable_gpu_acceleration=True,
                    enable_multiprocessing=True,
                    prefetch_enabled=True
                )
            
            # High-end system profile
            elif memory_gb >= 32 and cpu_cores >= 12:
                return OptimizationProfile(
                    profile_name="High_End_System",
                    max_workers=min(cpu_cores - 2, 12),
                    batch_size=6,
                    cache_size_mb=512,
                    memory_threshold=0.7,
                    enable_gpu_acceleration=bool(gpu_info),
                    enable_multiprocessing=True,
                    prefetch_enabled=True
                )
            
            # Standard system profile
            else:
                return OptimizationProfile(
                    profile_name="Standard_System",
                    max_workers=min(cpu_cores, 8),
                    batch_size=4,
                    cache_size_mb=256,
                    memory_threshold=0.6,
                    enable_gpu_acceleration=bool(gpu_info),
                    enable_multiprocessing=False,
                    prefetch_enabled=False
                )
                
        except Exception as e:
            self.logger.warning(f"Hardware detection failed: {e}, using default profile")
            return OptimizationProfile(
                profile_name="Default",
                max_workers=4,
                batch_size=2,
                cache_size_mb=128,
                memory_threshold=0.5,
                enable_gpu_acceleration=False,
                enable_multiprocessing=False,
                prefetch_enabled=False
            )
    
    def process(self, input_data: Any) -> ProcessingResult:
        """Process with performance optimization"""
        with self._processing_context("performance_optimization"):
            try:
                # This is a meta-processor that optimizes other processors
                # Implementation would depend on specific optimization needs
                
                return ProcessingResult(
                    success=True,
                    data={"optimization_profile": self.optimization_profile.profile_name},
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
                
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
    
    def cached_operation(self, cache_key: str, operation: Callable, *args, **kwargs):
        """Execute operation with caching"""
        # Check cache first
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Execute operation
        start_time = time.time()
        result = operation(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # Cache result if execution took significant time
        if execution_time > 0.1:  # Cache operations taking > 100ms
            self.cache.put(cache_key, result)
        
        return result
    
    def parallel_batch_process(self, items: List[Any], 
                             processor_func: Callable,
                             batch_size: Optional[int] = None) -> List[Any]:
        """Process items in parallel batches"""
        if not items:
            return []
        
        batch_size = batch_size or self.optimization_profile.batch_size
        results = []
        
        # Split into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        
        # Process batches in parallel
        if self.process_pool and len(batches) > 1:
            # Use process pool for CPU-intensive tasks
            futures = [
                self.process_pool.submit(self._process_batch, batch, processor_func)
                for batch in batches
            ]
            
            for future in futures:
                results.extend(future.result())
        else:
            # Use thread pool for I/O-bound tasks
            futures = [
                self.thread_pool.submit(self._process_batch, batch, processor_func)
                for batch in batches
            ]
            
            for future in futures:
                results.extend(future.result())
        
        return results
    
    def _process_batch(self, batch: List[Any], processor_func: Callable) -> List[Any]:
        """Process a single batch"""
        return [processor_func(item) for item in batch]
    
    def record_performance_metric(self, metric: PerformanceMetrics):
        """Record performance metric"""
        with self.metrics_lock:
            self.metrics_history.append(metric)
            
            # Keep only recent metrics (last 1000)
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        with self.metrics_lock:
            # Always return consistent structure
            base_report = {
                "optimization_profile": self.optimization_profile.profile_name,
                "cache_stats": self.cache.get_stats(),
                "system_utilization": {
                    "thread_pool_active": len(self.thread_pool._threads) if hasattr(self.thread_pool, '_threads') else 0,
                    "process_pool_active": bool(self.process_pool),
                    "cache_hit_rate": self.cache.get_stats()["hit_rate"]
                }
            }

            if not self.metrics_history:
                base_report["performance_metrics"] = {
                    "status": "no_metrics",
                    "average_execution_time_seconds": 0.0,
                    "average_memory_usage_mb": 0.0,
                    "average_throughput_items_per_second": 0.0,
                    "total_operations": 0
                }
            else:
                # Calculate aggregated metrics
                recent_metrics = self.metrics_history[-100:]  # Last 100 operations

                avg_execution_time = sum(m.execution_time_seconds for m in recent_metrics) / len(recent_metrics)
                avg_memory_usage = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
                avg_throughput = sum(m.throughput_items_per_second for m in recent_metrics) / len(recent_metrics)

                base_report["performance_metrics"] = {
                    "average_execution_time_seconds": avg_execution_time,
                    "average_memory_usage_mb": avg_memory_usage,
                    "average_throughput_items_per_second": avg_throughput,
                    "total_operations": len(self.metrics_history)
                }

            return base_report
    
    def optimize_for_operation(self, operation_type: str) -> Dict[str, Any]:
        """Get optimization recommendations for specific operation type"""
        recommendations = {
            "audio_processing": {
                "use_gpu": self.optimization_profile.enable_gpu_acceleration,
                "batch_size": self.optimization_profile.batch_size * 2,
                "parallel_workers": self.optimization_profile.max_workers,
                "enable_caching": True
            },
            "tja_parsing": {
                "use_multiprocessing": self.optimization_profile.enable_multiprocessing,
                "batch_size": self.optimization_profile.batch_size * 4,
                "parallel_workers": self.optimization_profile.max_workers,
                "enable_caching": True
            },
            "model_training": {
                "use_gpu": self.optimization_profile.enable_gpu_acceleration,
                "batch_size": self.optimization_profile.batch_size,
                "memory_threshold": self.optimization_profile.memory_threshold,
                "enable_mixed_precision": True
            }
        }
        
        return recommendations.get(operation_type, {
            "batch_size": self.optimization_profile.batch_size,
            "parallel_workers": self.optimization_profile.max_workers
        })
    
    def cleanup(self):
        """Cleanup resources"""
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
        
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
        
        self.logger.info("PerformanceOptimizer cleanup completed")


def performance_monitor(operation_name: str):
    """Decorator for performance monitoring"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = 0
            
            try:
                # Get memory usage if possible
                if hasattr(args[0], 'memory_monitor'):
                    start_memory = args[0].memory_monitor.get_current_usage()
            except Exception:
                pass
            
            try:
                result = func(*args, **kwargs)
                success = True
            except Exception as e:
                result = e
                success = False
            
            # Calculate metrics
            execution_time = time.time() - start_time
            end_memory = start_memory
            
            try:
                if hasattr(args[0], 'memory_monitor'):
                    end_memory = args[0].memory_monitor.get_current_usage()
            except Exception:
                pass
            
            memory_delta = end_memory - start_memory
            
            # Log performance
            logger = logging.getLogger(f"{func.__module__}.{func.__name__}")
            logger.debug(
                f"Performance: {operation_name} completed in {execution_time:.3f}s, "
                f"memory delta: {memory_delta:.1f}MB, success: {success}"
            )
            
            if not success:
                raise result
            
            return result
        
        return wrapper
    return decorator
