"""
Advanced System Monitoring and Analytics

Comprehensive monitoring system for tracking system health, performance,
and providing real-time analytics for the TJA Generator system.
"""

import time
import logging
import threading
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import statistics

from ..utils.base_classes import BaseProcessor, ProcessingResult
from ..config.unified_config_manager import UnifiedConfigManager


@dataclass
class SystemHealthMetrics:
    """System health metrics snapshot"""
    timestamp: float
    cpu_usage_percent: float
    memory_usage_mb: float
    memory_usage_percent: float
    gpu_usage_percent: float
    gpu_memory_mb: float
    disk_usage_percent: float
    active_threads: int
    active_processes: int
    system_load: float


@dataclass
class ProcessingMetrics:
    """Processing operation metrics"""
    operation_id: str
    operation_type: str
    start_time: float
    end_time: float
    duration_seconds: float
    success: bool
    error_message: Optional[str]
    input_size: int
    output_size: int
    memory_peak_mb: float
    cpu_usage_percent: float


@dataclass
class AlertRule:
    """Alert rule configuration"""
    rule_id: str
    metric_name: str
    threshold_value: float
    comparison_operator: str  # '>', '<', '>=', '<=', '=='
    alert_level: str  # 'info', 'warning', 'error', 'critical'
    cooldown_seconds: int = 300  # 5 minutes default


@dataclass
class SystemAlert:
    """System alert"""
    alert_id: str
    rule_id: str
    timestamp: float
    level: str
    message: str
    metric_value: float
    threshold_value: float
    resolved: bool = False
    resolved_timestamp: Optional[float] = None


class MetricsCollector:
    """Collects system and processing metrics"""
    
    def __init__(self, collection_interval: float = 5.0):
        self.collection_interval = collection_interval
        self.logger = logging.getLogger(f"{__name__}.MetricsCollector")
        
        # Metrics storage
        self.system_metrics: deque = deque(maxlen=1000)  # Last 1000 samples
        self.processing_metrics: deque = deque(maxlen=5000)  # Last 5000 operations
        
        # Collection thread
        self._collection_thread = None
        self._stop_collection = threading.Event()
        
        # Metrics lock
        self._metrics_lock = threading.Lock()
    
    def start_collection(self):
        """Start metrics collection"""
        if self._collection_thread and self._collection_thread.is_alive():
            return
        
        self._stop_collection.clear()
        self._collection_thread = threading.Thread(
            target=self._collect_system_metrics,
            daemon=True
        )
        self._collection_thread.start()
        
        self.logger.info("Metrics collection started")
    
    def stop_collection(self):
        """Stop metrics collection"""
        self._stop_collection.set()
        if self._collection_thread:
            self._collection_thread.join(timeout=5.0)
        
        self.logger.info("Metrics collection stopped")
    
    def _collect_system_metrics(self):
        """Collect system metrics in background thread"""
        while not self._stop_collection.wait(self.collection_interval):
            try:
                metrics = self._get_current_system_metrics()
                
                with self._metrics_lock:
                    self.system_metrics.append(metrics)
                
            except Exception as e:
                self.logger.error(f"Error collecting system metrics: {e}")
    
    def _get_current_system_metrics(self) -> SystemHealthMetrics:
        """Get current system metrics"""
        try:
            import psutil
            import threading
            
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # GPU metrics (if available)
            gpu_usage = 0.0
            gpu_memory = 0.0
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_usage = torch.cuda.utilization()
                    gpu_memory = torch.cuda.memory_allocated() / (1024**2)  # MB
            except Exception:
                pass
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # System load
            try:
                system_load = psutil.getloadavg()[0]  # 1-minute load average
            except AttributeError:
                system_load = cpu_percent / 100.0  # Fallback for Windows
            
            return SystemHealthMetrics(
                timestamp=time.time(),
                cpu_usage_percent=cpu_percent,
                memory_usage_mb=memory.used / (1024**2),
                memory_usage_percent=memory.percent,
                gpu_usage_percent=gpu_usage,
                gpu_memory_mb=gpu_memory,
                disk_usage_percent=disk_percent,
                active_threads=threading.active_count(),
                active_processes=len(psutil.pids()),
                system_load=system_load
            )
            
        except Exception as e:
            self.logger.error(f"Error getting system metrics: {e}")
            return SystemHealthMetrics(
                timestamp=time.time(),
                cpu_usage_percent=0.0,
                memory_usage_mb=0.0,
                memory_usage_percent=0.0,
                gpu_usage_percent=0.0,
                gpu_memory_mb=0.0,
                disk_usage_percent=0.0,
                active_threads=0,
                active_processes=0,
                system_load=0.0
            )
    
    def record_processing_metrics(self, metrics: ProcessingMetrics):
        """Record processing operation metrics"""
        with self._metrics_lock:
            self.processing_metrics.append(metrics)
    
    def get_system_metrics_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get system metrics summary for specified duration"""
        cutoff_time = time.time() - (duration_minutes * 60)
        
        with self._metrics_lock:
            recent_metrics = [
                m for m in self.system_metrics 
                if m.timestamp >= cutoff_time
            ]
        
        if not recent_metrics:
            return {"status": "no_data"}
        
        # Calculate statistics
        cpu_values = [m.cpu_usage_percent for m in recent_metrics]
        memory_values = [m.memory_usage_percent for m in recent_metrics]
        gpu_values = [m.gpu_usage_percent for m in recent_metrics]
        
        return {
            "duration_minutes": duration_minutes,
            "sample_count": len(recent_metrics),
            "cpu_usage": {
                "average": statistics.mean(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values),
                "current": recent_metrics[-1].cpu_usage_percent
            },
            "memory_usage": {
                "average": statistics.mean(memory_values),
                "max": max(memory_values),
                "min": min(memory_values),
                "current": recent_metrics[-1].memory_usage_percent
            },
            "gpu_usage": {
                "average": statistics.mean(gpu_values) if gpu_values else 0,
                "max": max(gpu_values) if gpu_values else 0,
                "min": min(gpu_values) if gpu_values else 0,
                "current": recent_metrics[-1].gpu_usage_percent
            }
        }
    
    def get_processing_metrics_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get processing metrics summary"""
        cutoff_time = time.time() - (duration_minutes * 60)
        
        with self._metrics_lock:
            recent_metrics = [
                m for m in self.processing_metrics 
                if m.start_time >= cutoff_time
            ]
        
        if not recent_metrics:
            return {"status": "no_data"}
        
        # Group by operation type
        by_operation = defaultdict(list)
        for metric in recent_metrics:
            by_operation[metric.operation_type].append(metric)
        
        # Calculate statistics
        summary = {
            "duration_minutes": duration_minutes,
            "total_operations": len(recent_metrics),
            "successful_operations": sum(1 for m in recent_metrics if m.success),
            "failed_operations": sum(1 for m in recent_metrics if not m.success),
            "by_operation_type": {}
        }
        
        for op_type, metrics in by_operation.items():
            durations = [m.duration_seconds for m in metrics]
            success_rate = sum(1 for m in metrics if m.success) / len(metrics)
            
            summary["by_operation_type"][op_type] = {
                "count": len(metrics),
                "success_rate": success_rate,
                "average_duration": statistics.mean(durations),
                "max_duration": max(durations),
                "min_duration": min(durations)
            }
        
        return summary


class AlertManager:
    """Manages system alerts and notifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.AlertManager")
        
        # Alert rules and active alerts
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, SystemAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        
        # Alert cooldowns
        self.alert_cooldowns: Dict[str, float] = {}
        
        # Alert lock
        self._alert_lock = threading.Lock()
        
        # Setup default alert rules
        self._setup_default_alert_rules()
    
    def _setup_default_alert_rules(self):
        """Setup default alert rules"""
        default_rules = [
            AlertRule("high_cpu", "cpu_usage_percent", 90.0, ">=", "warning"),
            AlertRule("high_memory", "memory_usage_percent", 85.0, ">=", "warning"),
            AlertRule("critical_memory", "memory_usage_percent", 95.0, ">=", "critical"),
            AlertRule("high_gpu_usage", "gpu_usage_percent", 95.0, ">=", "warning"),
            AlertRule("high_disk_usage", "disk_usage_percent", 90.0, ">=", "warning"),
            AlertRule("processing_failure_rate", "failure_rate", 0.1, ">=", "error")
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.rule_id] = rule
    
    def add_alert_rule(self, rule: AlertRule):
        """Add custom alert rule"""
        with self._alert_lock:
            self.alert_rules[rule.rule_id] = rule
        
        self.logger.info(f"Added alert rule: {rule.rule_id}")
    
    def check_alerts(self, metrics: SystemHealthMetrics):
        """Check metrics against alert rules"""
        current_time = time.time()
        
        with self._alert_lock:
            for rule_id, rule in self.alert_rules.items():
                # Check cooldown
                if rule_id in self.alert_cooldowns:
                    if current_time < self.alert_cooldowns[rule_id]:
                        continue
                
                # Get metric value
                metric_value = getattr(metrics, rule.metric_name, None)
                if metric_value is None:
                    continue
                
                # Check threshold
                triggered = self._evaluate_threshold(
                    metric_value, rule.threshold_value, rule.comparison_operator
                )
                
                if triggered:
                    self._trigger_alert(rule, metric_value, current_time)
                else:
                    # Check if we should resolve an active alert
                    if rule_id in self.active_alerts:
                        self._resolve_alert(rule_id, current_time)
    
    def _evaluate_threshold(self, value: float, threshold: float, operator: str) -> bool:
        """Evaluate threshold condition"""
        if operator == ">":
            return value > threshold
        elif operator == "<":
            return value < threshold
        elif operator == ">=":
            return value >= threshold
        elif operator == "<=":
            return value <= threshold
        elif operator == "==":
            return abs(value - threshold) < 0.001
        else:
            return False
    
    def _trigger_alert(self, rule: AlertRule, metric_value: float, timestamp: float):
        """Trigger an alert"""
        alert_id = f"{rule.rule_id}_{int(timestamp)}"
        
        alert = SystemAlert(
            alert_id=alert_id,
            rule_id=rule.rule_id,
            timestamp=timestamp,
            level=rule.alert_level,
            message=f"{rule.metric_name} is {metric_value:.2f} (threshold: {rule.threshold_value})",
            metric_value=metric_value,
            threshold_value=rule.threshold_value
        )
        
        self.active_alerts[rule.rule_id] = alert
        self.alert_history.append(alert)
        self.alert_cooldowns[rule.rule_id] = timestamp + rule.cooldown_seconds
        
        # Log alert
        log_level = getattr(logging, rule.alert_level.upper(), logging.INFO)
        self.logger.log(log_level, f"ALERT: {alert.message}")
    
    def _resolve_alert(self, rule_id: str, timestamp: float):
        """Resolve an active alert"""
        if rule_id in self.active_alerts:
            alert = self.active_alerts[rule_id]
            alert.resolved = True
            alert.resolved_timestamp = timestamp
            
            del self.active_alerts[rule_id]
            
            self.logger.info(f"RESOLVED: Alert {rule_id}")
    
    def get_active_alerts(self) -> List[SystemAlert]:
        """Get list of active alerts"""
        with self._alert_lock:
            return list(self.active_alerts.values())
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary"""
        with self._alert_lock:
            recent_alerts = [
                alert for alert in self.alert_history
                if alert.timestamp >= (time.time() - 3600)  # Last hour
            ]
            
            by_level = defaultdict(int)
            for alert in recent_alerts:
                by_level[alert.level] += 1
            
            return {
                "active_alerts": len(self.active_alerts),
                "recent_alerts_1h": len(recent_alerts),
                "alerts_by_level": dict(by_level),
                "alert_rules_count": len(self.alert_rules)
            }


class SystemMonitor(BaseProcessor):
    """
    Advanced system monitoring and analytics
    
    Provides comprehensive monitoring, alerting, and analytics
    for the TJA Generator system.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "SystemMonitor")
        
        # Initialize configuration
        self.config_manager = UnifiedConfigManager()
        self.path_manager = PathManager()
        
        # Initialize components
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        
        # Start monitoring
        self.metrics_collector.start_collection()
        
        # Setup periodic alert checking
        self._setup_alert_checking()
        
        self.logger.info("SystemMonitor initialized and monitoring started")
    
    def _setup_alert_checking(self):
        """Setup periodic alert checking"""
        def check_alerts_periodically():
            while True:
                try:
                    # Get latest system metrics
                    if self.metrics_collector.system_metrics:
                        latest_metrics = self.metrics_collector.system_metrics[-1]
                        self.alert_manager.check_alerts(latest_metrics)
                except Exception as e:
                    self.logger.error(f"Error checking alerts: {e}")
                
                time.sleep(30)  # Check every 30 seconds
        
        alert_thread = threading.Thread(target=check_alerts_periodically, daemon=True)
        alert_thread.start()
    
    def process(self, input_data: Any) -> ProcessingResult:
        """Process monitoring request"""
        with self._processing_context("system_monitoring"):
            try:
                request_type = input_data.get("type", "status") if isinstance(input_data, dict) else "status"
                
                if request_type == "status":
                    result = self.get_system_status()
                elif request_type == "metrics":
                    duration = input_data.get("duration_minutes", 60)
                    result = self.get_metrics_report(duration)
                elif request_type == "alerts":
                    result = self.get_alerts_report()
                else:
                    result = {"error": f"Unknown request type: {request_type}"}
                
                return ProcessingResult(
                    success=True,
                    data=result,
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
                
            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        # Get latest metrics
        system_summary = self.metrics_collector.get_system_metrics_summary(15)  # Last 15 minutes
        processing_summary = self.metrics_collector.get_processing_metrics_summary(60)  # Last hour
        alert_summary = self.alert_manager.get_alert_summary()
        
        # Determine overall health
        health_score = self._calculate_health_score(system_summary, alert_summary)
        
        return {
            "timestamp": time.time(),
            "health_score": health_score,
            "status": self._get_status_from_health_score(health_score),
            "system_metrics": system_summary,
            "processing_metrics": processing_summary,
            "alerts": alert_summary
        }
    
    def get_metrics_report(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get detailed metrics report"""
        return {
            "system_metrics": self.metrics_collector.get_system_metrics_summary(duration_minutes),
            "processing_metrics": self.metrics_collector.get_processing_metrics_summary(duration_minutes),
            "report_generated": time.time(),
            "duration_minutes": duration_minutes
        }
    
    def get_alerts_report(self) -> Dict[str, Any]:
        """Get alerts report"""
        active_alerts = self.alert_manager.get_active_alerts()
        alert_summary = self.alert_manager.get_alert_summary()
        
        return {
            "active_alerts": [asdict(alert) for alert in active_alerts],
            "summary": alert_summary,
            "report_generated": time.time()
        }
    
    def _calculate_health_score(self, system_summary: Dict, alert_summary: Dict) -> float:
        """Calculate overall system health score (0-1)"""
        if system_summary.get("status") == "no_data":
            return 0.5  # Unknown
        
        score = 1.0
        
        # Deduct for high resource usage
        cpu_avg = system_summary.get("cpu_usage", {}).get("average", 0)
        memory_avg = system_summary.get("memory_usage", {}).get("average", 0)
        
        if cpu_avg > 80:
            score -= 0.2
        elif cpu_avg > 60:
            score -= 0.1
        
        if memory_avg > 85:
            score -= 0.3
        elif memory_avg > 70:
            score -= 0.1
        
        # Deduct for active alerts
        active_alerts = alert_summary.get("active_alerts", 0)
        if active_alerts > 0:
            score -= min(0.4, active_alerts * 0.1)
        
        return max(0.0, min(1.0, score))
    
    def _get_status_from_health_score(self, score: float) -> str:
        """Get status string from health score"""
        if score >= 0.8:
            return "healthy"
        elif score >= 0.6:
            return "warning"
        elif score >= 0.4:
            return "degraded"
        else:
            return "critical"
    
    def record_operation_metrics(self, operation_id: str, operation_type: str,
                               start_time: float, end_time: float,
                               success: bool, error_message: Optional[str] = None,
                               input_size: int = 0, output_size: int = 0):
        """Record metrics for a processing operation"""
        metrics = ProcessingMetrics(
            operation_id=operation_id,
            operation_type=operation_type,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=end_time - start_time,
            success=success,
            error_message=error_message,
            input_size=input_size,
            output_size=output_size,
            memory_peak_mb=self.memory_monitor.get_current_usage(),
            cpu_usage_percent=0.0  # Would need psutil to get accurate CPU usage
        )
        
        self.metrics_collector.record_processing_metrics(metrics)
    
    def cleanup(self):
        """Cleanup monitoring resources"""
        self.metrics_collector.stop_collection()
        self.logger.info("SystemMonitor cleanup completed")
