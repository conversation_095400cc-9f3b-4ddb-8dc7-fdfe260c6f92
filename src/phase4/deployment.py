from typing import Any, Dict, Optional
"""
Deployment Manager

Production deployment utilities for the TJA Generator system including
configuration management, health monitoring, and deployment validation.
"""

import os
import sys
import time
import logging
from pathlib import Path
import psutil
import torch

from .config import PHASE_4_CONFIG
from ..utils.memory_monitor import MemoryMonitor


class DeploymentError(Exception):
    """Custom exception for deployment errors"""
    pass


class DeploymentManager:
    """
    Production deployment manager for TJA Generator system
    
    Handles:
    - System validation and requirements checking
    - Configuration management and validation
    - Health monitoring and diagnostics
    - Performance benchmarking
    - Deployment validation
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or PHASE_4_CONFIG
        self.logger = self._setup_logging()
        self.memory_monitor = MemoryMonitor()
        
        # Deployment state
        self.validation_results = {}
        self.benchmark_results = {}
        self.health_status = {}
        
        self.logger.info("Deployment Manager initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup deployment logging"""
        logger = logging.getLogger("deployment")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_dir = Path(self.config["directories"]["logs_dir"])
            log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / "deployment.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def validate_system_requirements(self) -> Dict[str, Any]:
        """Validate system meets deployment requirements"""
        self.logger.info("Validating system requirements...")
        
        validation_results = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "overall_status": "unknown",
            "checks": {}
        }
        
        try:
            # Python version check
            python_version = sys.version_info
            required_python = (3, 8)
            
            validation_results["checks"]["python_version"] = {
                "status": "pass" if python_version >= required_python else "fail",
                "current": f"{python_version.major}.{python_version.minor}.{python_version.micro}",
                "required": f"{required_python[0]}.{required_python[1]}+",
                "message": "Python version check"
            }
            
            # CUDA availability
            cuda_available = torch.cuda.is_available()
            validation_results["checks"]["cuda"] = {
                "status": "pass" if cuda_available else "warning",
                "available": cuda_available,
                "message": "CUDA availability check"
            }
            
            if cuda_available:
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) if gpu_count > 0 else 0
                
                validation_results["checks"]["gpu"] = {
                    "status": "pass" if gpu_memory >= 6.0 else "warning",
                    "count": gpu_count,
                    "name": gpu_name,
                    "memory_gb": gpu_memory,
                    "message": f"GPU: {gpu_name} ({gpu_memory:.1f}GB)"
                }
            
            # System memory
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            
            validation_results["checks"]["system_memory"] = {
                "status": "pass" if memory_gb >= 8.0 else "warning",
                "total_gb": memory_gb,
                "available_gb": memory.available / (1024**3),
                "usage_percent": memory.percent,
                "message": f"System memory: {memory_gb:.1f}GB total"
            }
            
            # CPU check
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            validation_results["checks"]["cpu"] = {
                "status": "pass" if cpu_count >= 4 else "warning",
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "message": f"CPU: {cpu_count} cores"
            }
            
            # Disk space
            disk_usage = psutil.disk_usage('/')
            disk_free_gb = disk_usage.free / (1024**3)
            
            validation_results["checks"]["disk_space"] = {
                "status": "pass" if disk_free_gb >= 10.0 else "warning",
                "free_gb": disk_free_gb,
                "total_gb": disk_usage.total / (1024**3),
                "usage_percent": (disk_usage.used / disk_usage.total) * 100,
                "message": f"Disk space: {disk_free_gb:.1f}GB free"
            }
            
            # Required directories
            required_dirs = [
                self.config["directories"]["data_dir"],
                self.config["directories"]["models_dir"],
                self.config["directories"]["outputs_dir"],
                self.config["directories"]["logs_dir"]
            ]
            
            dir_status = "pass"
            dir_messages = []
            
            for dir_path in required_dirs:
                path = Path(dir_path)
                if not path.exists():
                    try:
                        path.mkdir(parents=True, exist_ok=True)
                        dir_messages.append(f"Created: {dir_path}")
                    except Exception as e:
                        dir_status = "fail"
                        dir_messages.append(f"Failed to create: {dir_path} - {e}")
                else:
                    dir_messages.append(f"Exists: {dir_path}")
            
            validation_results["checks"]["directories"] = {
                "status": dir_status,
                "messages": dir_messages,
                "message": "Directory structure validation"
            }
            
            # Package dependencies
            try:
                import torch as torch_dep
                import numpy as numpy_dep
                import scipy as scipy_dep

                validation_results["checks"]["dependencies"] = {
                    "status": "pass",
                    "torch_version": torch_dep.__version__,
                    "numpy_version": numpy_dep.__version__,
                    "scipy_version": scipy_dep.__version__,
                    "message": "Core dependencies available"
                }
            except ImportError as e:
                validation_results["checks"]["dependencies"] = {
                    "status": "fail",
                    "error": str(e),
                    "message": "Missing core dependencies"
                }
            
            # Overall status
            failed_checks = [check for check in validation_results["checks"].values() 
                           if check["status"] == "fail"]
            warning_checks = [check for check in validation_results["checks"].values() 
                            if check["status"] == "warning"]
            
            if failed_checks:
                validation_results["overall_status"] = "fail"
                validation_results["message"] = f"{len(failed_checks)} critical issues found"
            elif warning_checks:
                validation_results["overall_status"] = "warning"
                validation_results["message"] = f"{len(warning_checks)} warnings found"
            else:
                validation_results["overall_status"] = "pass"
                validation_results["message"] = "All system requirements met"
            
            self.validation_results = validation_results
            
            self.logger.info(f"System validation completed: {validation_results['overall_status']}")
            return validation_results
            
        except Exception as e:
            self.logger.error(f"System validation failed: {e}")
            validation_results["overall_status"] = "error"
            validation_results["error"] = str(e)
            return validation_results
    
    def run_performance_benchmark(self) -> Dict[str, Any]:
        """Run performance benchmark tests"""
        self.logger.info("Running performance benchmarks...")
        
        benchmark_results = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "benchmarks": {}
        }
        
        try:
            # Memory allocation benchmark
            self.logger.info("Testing memory allocation...")
            start_time = time.time()
            
            # Test tensor allocation
            test_tensors = []
            for i in range(10):
                tensor = torch.randn(1000, 1000)
                if torch.cuda.is_available():
                    tensor = tensor.cuda()
                test_tensors.append(tensor)
            
            # Cleanup
            del test_tensors
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            memory_time = time.time() - start_time
            
            benchmark_results["benchmarks"]["memory_allocation"] = {
                "duration_seconds": memory_time,
                "status": "pass" if memory_time < 5.0 else "warning",
                "message": f"Memory allocation test: {memory_time:.2f}s"
            }
            
            # CPU computation benchmark
            self.logger.info("Testing CPU computation...")
            start_time = time.time()
            
            # Matrix multiplication test
            a = torch.randn(1000, 1000)
            b = torch.randn(1000, 1000)
            c = torch.mm(a, b)
            
            cpu_time = time.time() - start_time
            
            benchmark_results["benchmarks"]["cpu_computation"] = {
                "duration_seconds": cpu_time,
                "status": "pass" if cpu_time < 2.0 else "warning",
                "message": f"CPU computation test: {cpu_time:.2f}s"
            }
            
            # GPU computation benchmark (if available)
            if torch.cuda.is_available():
                self.logger.info("Testing GPU computation...")
                start_time = time.time()
                
                a_gpu = torch.randn(1000, 1000).cuda()
                b_gpu = torch.randn(1000, 1000).cuda()
                c_gpu = torch.mm(a_gpu, b_gpu)
                torch.cuda.synchronize()
                
                gpu_time = time.time() - start_time
                
                benchmark_results["benchmarks"]["gpu_computation"] = {
                    "duration_seconds": gpu_time,
                    "status": "pass" if gpu_time < 0.5 else "warning",
                    "speedup": cpu_time / gpu_time if gpu_time > 0 else 0,
                    "message": f"GPU computation test: {gpu_time:.2f}s (speedup: {cpu_time/gpu_time:.1f}x)"
                }
                
                # Cleanup GPU memory
                del a_gpu, b_gpu, c_gpu
                torch.cuda.empty_cache()
            
            # File I/O benchmark
            self.logger.info("Testing file I/O...")
            start_time = time.time()
            
            temp_file = Path(self.config["directories"]["temp_dir"]) / "benchmark_test.pt"
            temp_file.parent.mkdir(exist_ok=True)
            
            # Write test
            test_data = torch.randn(100, 100, 100)
            torch.save(test_data, temp_file)
            
            # Read test
            loaded_data = torch.load(temp_file, map_location='cpu')
            
            # Cleanup
            temp_file.unlink()
            
            io_time = time.time() - start_time
            
            benchmark_results["benchmarks"]["file_io"] = {
                "duration_seconds": io_time,
                "status": "pass" if io_time < 1.0 else "warning",
                "message": f"File I/O test: {io_time:.2f}s"
            }
            
            # Overall benchmark status
            failed_benchmarks = [b for b in benchmark_results["benchmarks"].values() 
                               if b["status"] == "fail"]
            warning_benchmarks = [b for b in benchmark_results["benchmarks"].values() 
                                if b["status"] == "warning"]
            
            if failed_benchmarks:
                benchmark_results["overall_status"] = "fail"
            elif warning_benchmarks:
                benchmark_results["overall_status"] = "warning"
            else:
                benchmark_results["overall_status"] = "pass"
            
            self.benchmark_results = benchmark_results
            
            self.logger.info(f"Performance benchmarks completed: {benchmark_results['overall_status']}")
            return benchmark_results
            
        except Exception as e:
            self.logger.error(f"Performance benchmark failed: {e}")
            benchmark_results["overall_status"] = "error"
            benchmark_results["error"] = str(e)
            return benchmark_results
    
    def check_health_status(self) -> Dict[str, Any]:
        """Check current system health status"""
        health_status = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "status": "unknown",
            "checks": {}
        }
        
        try:
            # Memory usage
            memory_stats = self.memory_monitor.get_memory_stats()
            
            health_status["checks"]["memory"] = {
                "status": "pass" if memory_stats.system_memory_percent < 85 else "warning",
                "system_memory_percent": memory_stats.system_memory_percent,
                "gpu_memory_gb": memory_stats.gpu_reserved_gb,
                "gpu_utilization_percent": memory_stats.gpu_utilization_percent
            }
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            health_status["checks"]["cpu"] = {
                "status": "pass" if cpu_percent < 80 else "warning",
                "usage_percent": cpu_percent
            }
            
            # Disk usage
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            health_status["checks"]["disk"] = {
                "status": "pass" if disk_percent < 90 else "warning",
                "usage_percent": disk_percent,
                "free_gb": disk_usage.free / (1024**3)
            }
            
            # Process status (simplified)
            health_status["checks"]["process"] = {
                "status": "pass",
                "pid": os.getpid(),
                "uptime_seconds": time.time() - psutil.Process().create_time()
            }
            
            # Overall health
            warning_checks = [check for check in health_status["checks"].values() 
                            if check["status"] == "warning"]
            failed_checks = [check for check in health_status["checks"].values() 
                           if check["status"] == "fail"]
            
            if failed_checks:
                health_status["status"] = "unhealthy"
            elif warning_checks:
                health_status["status"] = "degraded"
            else:
                health_status["status"] = "healthy"
            
            self.health_status = health_status
            return health_status
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            health_status["status"] = "error"
            health_status["error"] = str(e)
            return health_status
    
    def validate_deployment(self) -> Dict[str, Any]:
        """Run complete deployment validation"""
        self.logger.info("Running complete deployment validation...")
        
        deployment_validation = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "overall_status": "unknown",
            "validations": {}
        }
        
        try:
            # System requirements
            self.logger.info("Validating system requirements...")
            system_validation = self.validate_system_requirements()
            deployment_validation["validations"]["system_requirements"] = system_validation
            
            # Performance benchmarks
            self.logger.info("Running performance benchmarks...")
            benchmark_results = self.run_performance_benchmark()
            deployment_validation["validations"]["performance_benchmarks"] = benchmark_results
            
            # Health status
            self.logger.info("Checking health status...")
            health_status = self.check_health_status()
            deployment_validation["validations"]["health_status"] = health_status
            
            # Configuration validation
            self.logger.info("Validating configuration...")
            config_validation = self._validate_configuration()
            deployment_validation["validations"]["configuration"] = config_validation
            
            # Overall deployment status
            validation_statuses = [
                system_validation["overall_status"],
                benchmark_results["overall_status"],
                health_status["status"],
                config_validation["status"]
            ]
            
            if "fail" in validation_statuses or "error" in validation_statuses:
                deployment_validation["overall_status"] = "fail"
                deployment_validation["message"] = "Deployment validation failed"
            elif "warning" in validation_statuses or "degraded" in validation_statuses:
                deployment_validation["overall_status"] = "warning"
                deployment_validation["message"] = "Deployment validation passed with warnings"
            else:
                deployment_validation["overall_status"] = "pass"
                deployment_validation["message"] = "Deployment validation successful"
            
            self.logger.info(f"Deployment validation completed: {deployment_validation['overall_status']}")
            return deployment_validation
            
        except Exception as e:
            self.logger.error(f"Deployment validation failed: {e}")
            deployment_validation["overall_status"] = "error"
            deployment_validation["error"] = str(e)
            return deployment_validation
    
    def _validate_configuration(self) -> Dict[str, Any]:
        """Validate system configuration"""
        config_validation = {
            "status": "pass",
            "checks": {},
            "message": "Configuration validation"
        }
        
        try:
            # Check required configuration keys
            required_keys = [
                "directories",
                "pipeline",
                "phase1_integration",
                "phase2_integration", 
                "phase3_integration"
            ]
            
            for key in required_keys:
                if key in self.config:
                    config_validation["checks"][key] = {"status": "pass", "message": f"{key} configuration present"}
                else:
                    config_validation["checks"][key] = {"status": "fail", "message": f"{key} configuration missing"}
                    config_validation["status"] = "fail"
            
            # Check model file existence
            model_path = Path(self.config["phase3_integration"]["model_checkpoint_path"])
            fallback_path = Path(self.config["phase3_integration"]["fallback_model_path"])
            
            if model_path.exists() or fallback_path.exists():
                config_validation["checks"]["model_files"] = {
                    "status": "pass",
                    "message": "Model files available"
                }
            else:
                config_validation["checks"]["model_files"] = {
                    "status": "warning",
                    "message": "No trained model found - will use random weights"
                }
            
            return config_validation
            
        except Exception as e:
            config_validation["status"] = "error"
            config_validation["error"] = str(e)
            return config_validation
    
    def generate_deployment_report(self) -> str:
        """Generate comprehensive deployment report"""
        validation_results = self.validate_deployment()
        
        report_lines = [
            "=" * 80,
            "TJA GENERATOR DEPLOYMENT VALIDATION REPORT",
            "=" * 80,
            f"Generated: {validation_results['timestamp']}",
            f"Overall Status: {validation_results['overall_status'].upper()}",
            ""
        ]
        
        for validation_name, validation_data in validation_results["validations"].items():
            report_lines.append(f"{validation_name.replace('_', ' ').title()}:")
            report_lines.append("-" * 40)
            
            if "checks" in validation_data:
                for check_name, check_data in validation_data["checks"].items():
                    status_symbol = {"pass": "[PASS]", "warning": "[WARN]", "fail": "[FAIL]", "error": "[ERROR]"}.get(check_data["status"], "[?]")
                    report_lines.append(f"  {status_symbol} {check_name}: {check_data.get('message', 'No message')}")
            
            report_lines.append("")
        
        report_lines.extend([
            "=" * 80,
            "END OF REPORT",
            "=" * 80
        ])
        
        return "\n".join(report_lines)
    
    def save_deployment_report(self, output_path: Optional[str] = None) -> str:
        """Save deployment report to file"""
        if output_path is None:
            output_dir = Path(self.config["directories"]["outputs_dir"]) / "deployment"
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"deployment_report_{int(time.time())}.txt"
        
        report_content = self.generate_deployment_report()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.info(f"Deployment report saved: {output_path}")
        return str(output_path)
