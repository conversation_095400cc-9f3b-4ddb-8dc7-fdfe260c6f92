"""
TJA Generation REST API

Production-ready REST API for TJA generation with file upload, progress tracking,
rate limiting, and comprehensive error handling.
"""

import os
import time
import uuid
import tempfile
import logging
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Depends
    from fastapi.responses import JSONResponse, FileResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    # Create dummy classes for when FastAPI is not available
    class BaseModel:
        pass
    class Field:
        def __init__(self, *args, **kwargs):
            pass

from .config import PHASE_4_CONFIG


# Pydantic models for API
class TJAGenerationRequest(BaseModel):
    """Request model for TJA generation"""
    difficulties: List[int] = Field(default=[8, 9, 10], description="Difficulty levels to generate")
    title: Optional[str] = Field(None, description="Song title")
    artist: Optional[str] = Field(None, description="Artist name")
    bpm: Optional[float] = Field(None, description="BPM")
    offset: Optional[float] = Field(None, description="Offset in seconds")
    genre: Optional[str] = Field(None, description="Genre")
    min_quality: float = Field(0.6, description="Minimum quality threshold")


class TJAGenerationResponse(BaseModel):
    """Response model for TJA generation"""
    job_id: str
    status: str
    message: str
    created_at: str
    estimated_completion: Optional[str] = None


class JobStatus(BaseModel):
    """Job status model"""
    job_id: str
    status: str  # pending, processing, completed, failed
    progress: float  # 0.0 to 1.0
    message: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TJAGenerationAPI:
    """REST API for TJA generation"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        if not FASTAPI_AVAILABLE:
            raise ImportError("FastAPI not available. Install with: pip install fastapi uvicorn")
        
        self.config = config or PHASE_4_CONFIG
        self.api_config = self.config["api"]
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="TJA Generator API",
            description="Generate TJA files from audio using deep learning",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # Setup CORS
        if self.api_config["cors_enabled"]:
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
        
        # Initialize components
        self.pipeline = None
        self.jobs: Dict[str, JobStatus] = {}
        self.logger = self._setup_logging()
        
        # Rate limiting
        self.rate_limiter = RateLimiter(
            requests_per_minute=self.api_config["rate_limiting"]["requests_per_minute"],
            burst_size=self.api_config["rate_limiting"]["burst_size"]
        ) if self.api_config["rate_limiting"]["enabled"] else None
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("TJA Generation API initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup API logging"""
        logger = logging.getLogger("tja_api")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """Initialize pipeline on startup"""
            self.logger.info("Starting TJA Generation API...")
            self.pipeline = TJAGenerationPipeline(self.config)
            
            if not self.pipeline.initialize():
                self.logger.error("Pipeline initialization failed")
                raise RuntimeError("Pipeline initialization failed")
            
            self.logger.info("API startup completed")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """Cleanup on shutdown"""
            self.logger.info("Shutting down TJA Generation API...")
            if self.pipeline:
                self.pipeline.cleanup()
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            if self.pipeline and self.pipeline.is_initialized:
                status = self.pipeline.get_pipeline_status()
                return {
                    "status": "healthy",
                    "timestamp": datetime.utcnow().isoformat(),
                    "pipeline_status": status
                }
            else:
                return JSONResponse(
                    status_code=503,
                    content={
                        "status": "unhealthy",
                        "message": "Pipeline not initialized",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
        
        @self.app.get("/metrics")
        async def get_metrics():
            """Get API metrics"""
            if self.pipeline:
                pipeline_stats = self.pipeline.get_pipeline_status()
                return {
                    "pipeline_stats": pipeline_stats,
                    "api_stats": {
                        "total_jobs": len(self.jobs),
                        "completed_jobs": len([j for j in self.jobs.values() if j.status == "completed"]),
                        "failed_jobs": len([j for j in self.jobs.values() if j.status == "failed"]),
                        "pending_jobs": len([j for j in self.jobs.values() if j.status in ["pending", "processing"]])
                    },
                    "timestamp": datetime.utcnow().isoformat()
                }
            else:
                return {"error": "Pipeline not available"}
        
        @self.app.post("/generate", response_model=TJAGenerationResponse)
        async def generate_tja(
            background_tasks: BackgroundTasks,
            audio_file: UploadFile = File(...),
            request: TJAGenerationRequest = Depends()
        ):
            """Generate TJA from uploaded audio file"""
            
            # Rate limiting
            if self.rate_limiter and not self.rate_limiter.allow_request():
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded. Please try again later."
                )
            
            # Validate file
            if not audio_file.filename:
                raise HTTPException(status_code=400, detail="No file provided")
            
            file_ext = Path(audio_file.filename).suffix.lower()
            supported_formats = self.config["file_formats"]["audio"]["supported_formats"]
            
            if file_ext not in supported_formats:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported file format: {file_ext}. Supported: {supported_formats}"
                )
            
            # Check file size
            max_size = self.api_config["file_upload"]["max_file_size_mb"] * 1024 * 1024
            if audio_file.size and audio_file.size > max_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"File too large. Maximum size: {max_size // (1024*1024)}MB"
                )
            
            # Create job
            job_id = str(uuid.uuid4())
            job = JobStatus(
                job_id=job_id,
                status="pending",
                progress=0.0,
                message="Job created, waiting to start processing",
                created_at=datetime.utcnow().isoformat()
            )
            
            self.jobs[job_id] = job
            
            # Save uploaded file temporarily
            temp_dir = Path(tempfile.gettempdir()) / "tja_api"
            temp_dir.mkdir(exist_ok=True)
            
            temp_file_path = temp_dir / f"{job_id}_{audio_file.filename}"
            
            try:
                with open(temp_file_path, "wb") as f:
                    content = await audio_file.read()
                    f.write(content)
                
                # Start background processing
                background_tasks.add_task(
                    self._process_tja_generation,
                    job_id,
                    str(temp_file_path),
                    request
                )
                
                # Estimate completion time
                avg_time = self.pipeline.processing_stats.get("average_processing_time", 60.0)
                estimated_completion = (datetime.utcnow() + timedelta(seconds=avg_time)).isoformat()
                
                return TJAGenerationResponse(
                    job_id=job_id,
                    status="pending",
                    message="Job queued for processing",
                    created_at=job.created_at,
                    estimated_completion=estimated_completion
                )
                
            except Exception as e:
                # Cleanup on error
                if temp_file_path.exists():
                    temp_file_path.unlink()
                
                self.logger.error(f"Failed to process upload: {e}")
                raise HTTPException(status_code=500, detail="Failed to process upload")
        
        @self.app.get("/jobs/{job_id}", response_model=JobStatus)
        async def get_job_status(job_id: str):
            """Get job status"""
            if job_id not in self.jobs:
                raise HTTPException(status_code=404, detail="Job not found")
            
            return self.jobs[job_id]
        
        @self.app.get("/jobs/{job_id}/download")
        async def download_result(job_id: str):
            """Download generated TJA file"""
            if job_id not in self.jobs:
                raise HTTPException(status_code=404, detail="Job not found")
            
            job = self.jobs[job_id]
            
            if job.status != "completed":
                raise HTTPException(status_code=400, detail="Job not completed")
            
            if not job.result or "output_file_path" not in job.result:
                raise HTTPException(status_code=500, detail="Result file not available")
            
            output_path = Path(job.result["output_file_path"])
            
            if not output_path.exists():
                raise HTTPException(status_code=404, detail="Result file not found")
            
            return FileResponse(
                path=str(output_path),
                filename=output_path.name,
                media_type="text/plain"
            )
        
        @self.app.delete("/jobs/{job_id}")
        async def delete_job(job_id: str):
            """Delete job and cleanup files"""
            if job_id not in self.jobs:
                raise HTTPException(status_code=404, detail="Job not found")
            
            job = self.jobs[job_id]
            
            # Cleanup files
            if job.result and "output_file_path" in job.result:
                output_path = Path(job.result["output_file_path"])
                if output_path.exists():
                    output_path.unlink()
            
            # Remove from jobs
            del self.jobs[job_id]
            
            return {"message": "Job deleted successfully"}
        
        @self.app.get("/jobs")
        async def list_jobs():
            """List all jobs"""
            return {
                "jobs": list(self.jobs.values()),
                "total": len(self.jobs)
            }
    
    async def _process_tja_generation(self, job_id: str, audio_file_path: str, 
                                    request: TJAGenerationRequest):
        """Background task for TJA generation"""
        job = self.jobs[job_id]
        temp_file_path = Path(audio_file_path)
        
        try:
            # Update job status
            job.status = "processing"
            job.started_at = datetime.utcnow().isoformat()
            job.message = "Processing audio file..."
            job.progress = 0.1
            
            # Create metadata
            metadata = {}
            if request.title:
                metadata["title"] = request.title
            if request.artist:
                metadata["artist"] = request.artist
            if request.bpm:
                metadata["bpm"] = request.bpm
            if request.offset:
                metadata["offset"] = request.offset
            if request.genre:
                metadata["genre"] = request.genre
            
            # Generate output path
            output_dir = Path(self.config["directories"]["outputs_dir"]) / "api_results"
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"{job_id}.tja"
            
            # Update progress
            job.progress = 0.3
            job.message = "Generating TJA sequences..."
            
            # Generate TJA
            result = self.pipeline.generate_tja(
                audio_file_path=str(temp_file_path),
                difficulty_levels=request.difficulties,
                output_path=str(output_path),
                metadata=metadata
            )
            
            # Check quality
            if "quality_metrics" in result:
                overall_quality = result["quality_metrics"].get("overall_quality", 0.0)
                if overall_quality < request.min_quality:
                    job.status = "failed"
                    job.error = f"Quality score {overall_quality:.3f} below threshold {request.min_quality}"
                    job.completed_at = datetime.utcnow().isoformat()
                    return
            
            # Success
            job.status = "completed"
            job.progress = 1.0
            job.message = "TJA generation completed successfully"
            job.completed_at = datetime.utcnow().isoformat()
            job.result = result
            
            self.logger.info(f"Job {job_id} completed successfully")
            
        except Exception as e:
            job.status = "failed"
            job.error = str(e)
            job.completed_at = datetime.utcnow().isoformat()
            job.message = f"Generation failed: {str(e)}"
            
            self.logger.error(f"Job {job_id} failed: {e}")
            self.logger.debug(traceback.format_exc())
        
        finally:
            # Cleanup temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()
    
    def run(self, host: str = None, port: int = None, **kwargs):
        """Run the API server"""
        host = host or self.api_config["host"]
        port = port or self.api_config["port"]
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info",
            **kwargs
        )


class RateLimiter:
    """Simple rate limiter for API requests"""
    
    def __init__(self, requests_per_minute: int, burst_size: int):
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
        self.requests = []
    
    def allow_request(self) -> bool:
        """Check if request is allowed"""
        now = time.time()
        
        # Remove old requests
        cutoff = now - 60  # 1 minute ago
        self.requests = [req_time for req_time in self.requests if req_time > cutoff]
        
        # Check limits
        if len(self.requests) >= self.requests_per_minute:
            return False
        
        # Check burst
        recent_cutoff = now - 10  # 10 seconds ago
        recent_requests = [req_time for req_time in self.requests if req_time > recent_cutoff]
        
        if len(recent_requests) >= self.burst_size:
            return False
        
        # Allow request
        self.requests.append(now)
        return True


def create_app():
    """Create FastAPI application"""
    if not FASTAPI_AVAILABLE:
        raise ImportError("FastAPI not available")
    api = TJAGenerationAPI()
    return api.app


if __name__ == "__main__":
    if FASTAPI_AVAILABLE:
        api = TJAGenerationAPI()
        api.run()
    else:
        print("FastAPI not available. Install with: pip install fastapi uvicorn")
