"""
Unified TJA Processing Module

Consolidates all TJA parsing, validation, and processing functionality from multiple
phases into a single, consistent system eliminating redundancy and implementing SOLID principles.
"""

    UnifiedTjaProcessor,
    UnifiedTjaParser,
    UnifiedTjaValidator,
    TjaChart,
    TjaCourse,
    TjaNote,
    TjaCommand,
    TjaParserInterface,
    TjaValidatorInterface
)

__all__ = [
    'UnifiedTjaProcessor',
    'UnifiedTjaParser',
    'UnifiedTjaValidator',
    'TjaChart',
    'TjaCourse',
    'TjaNote',
    'TjaCommand',
    'TjaParserInterface',
    'TjaValidatorInterface'
]
