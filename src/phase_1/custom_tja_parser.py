"""
Custom TJA Parser - Developed from scratch based on TJA format specification

Provides clean separation between metadata and notation data for AI training.
Implements specification-compliant WAVE field audio file association.
"""

import re
import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field
from src.shared.utils.encoding_detector import read_tja_file_with_encoding


@dataclass
class TJAMetadata:
    """TJA metadata container with separated reference and notation data"""
    
    # Reference metadata (NOT for training)
    title: Optional[str] = None
    titleen: Optional[str] = None
    titleja: Optional[str] = None
    subtitle: Optional[str] = None
    subtitleen: Optional[str] = None
    subtitleja: Optional[str] = None
    genre: Optional[str] = None
    maker: Optional[str] = None
    wave: Optional[str] = None
    demostart: Optional[float] = None
    songvol: Optional[int] = None
    sevol: Optional[int] = None
    side: Optional[str] = None
    bgimage: Optional[str] = None
    bgmovie: Optional[str] = None
    taikowebskin: Optional[str] = None
    
    # Notation metadata (FOR training)
    bpm: Optional[float] = None
    offset: Optional[float] = None
    
    # Course data
    course_data: Dict[int, Dict] = field(default_factory=dict)
    
    # File metadata
    encoding: Optional[str] = None
    file_path: Optional[str] = None


@dataclass
class TJANote:
    """Individual TJA note with timing and position information"""
    type: Union[int, str]  # Note type (0-9, A, B, F)
    position: float        # Position within measure (0.0 to 1.0)
    measure: int          # Measure number
    timing_ms: float      # Absolute timing in milliseconds
    branch: str = "normal"  # Branch type (normal, expert, master)


@dataclass
class TJACommand:
    """TJA command with timing information"""
    type: str            # Command type (BPMCHANGE, MEASURE, etc.)
    value: Any           # Command value
    timing_ms: float     # Absolute timing in milliseconds
    measure: int         # Measure number
    branch: str = "normal"  # Branch type


@dataclass
class TJACourse:
    """TJA course data for a specific difficulty"""
    
    # Reference data (NOT for training)
    level: Optional[int] = None
    balloon: Optional[str] = None
    scoreinit: Optional[int] = None
    scorediff: Optional[int] = None
    
    # Notation data (FOR training)
    notes: List[TJANote] = field(default_factory=list)
    commands: List[TJACommand] = field(default_factory=list)
    measures: List[str] = field(default_factory=list)
    
    # Metadata
    course_name: str = "oni"
    branch_data: Dict[str, List] = field(default_factory=dict)


class CustomTJAParser:
    """Custom TJA parser developed from scratch based on specification"""
    
    # Difficulty mapping
    DIFFS = {0: "easy", 1: "normal", 2: "hard", 3: "oni", 4: "edit", 5: "tower", 6: "dan"}
    COURSE_NAMES = {"easy": 0, "normal": 1, "hard": 2, "oni": 3, "edit": 4, "ura": 4, "tower": 5, "dan": 6}
    
    # Note type mapping for validation
    VALID_NOTE_TYPES = {
        0: "blank", 1: "don", 2: "ka", 3: "don_big", 4: "ka_big",
        5: "drumroll", 6: "drumroll_big", 7: "balloon", 8: "end_roll",
        9: "kusudama", "A": "don_both", "B": "ka_both", "F": "adlib"
    }
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.metadata = TJAMetadata()
        self.metadata.file_path = str(file_path)
        
        # Read file with encoding detection
        self.lines, self.metadata.encoding = read_tja_file_with_encoding(file_path)
        
        # Parse the TJA file
        self._parse_file()
    
    def _parse_file(self):
        """Parse the entire TJA file"""
        # Clean lines (remove comments and empty lines)
        cleaned_lines = []
        for line in self.lines:
            # Remove comments (everything after //)
            line = line.split('//')[0].strip()
            if line:
                cleaned_lines.append(line)
        
        self.lines = cleaned_lines
        
        # Parse metadata and course data
        self._parse_metadata()
        self._parse_courses()
    
    def _parse_metadata(self):
        """Parse TJA metadata fields"""
        current_course = None
        
        for line in self.lines:
            if line.startswith('#START'):
                break  # Stop at notation section
                
            if ':' not in line:
                continue
                
            key, value = line.split(':', 1)
            key = key.strip().upper()
            value = value.strip()
            
            # Global metadata
            if key == 'TITLE':
                self.metadata.title = value
            elif key == 'TITLEEN':
                self.metadata.titleen = value
            elif key == 'TITLEJA':
                self.metadata.titleja = value
            elif key == 'SUBTITLE':
                self.metadata.subtitle = value
            elif key == 'SUBTITLEEN':
                self.metadata.subtitleen = value
            elif key == 'SUBTITLEJA':
                self.metadata.subtitleja = value
            elif key == 'BPM':
                self.metadata.bpm = float(value) if value else 120.0
            elif key == 'WAVE':
                self.metadata.wave = value
            elif key == 'OFFSET':
                self.metadata.offset = float(value) if value else 0.0
            elif key == 'DEMOSTART':
                self.metadata.demostart = float(value) if value else 0.0
            elif key == 'GENRE':
                self.metadata.genre = value
            elif key == 'MAKER':
                self.metadata.maker = value
            elif key == 'SONGVOL':
                self.metadata.songvol = int(value) if value else 100
            elif key == 'SEVOL':
                self.metadata.sevol = int(value) if value else 100
            elif key == 'SIDE':
                self.metadata.side = value
            elif key == 'BGIMAGE':
                self.metadata.bgimage = value
            elif key == 'BGMOVIE':
                self.metadata.bgmovie = value
            elif key == 'TAIKOWEBSKIN':
                self.metadata.taikowebskin = value
            
            # Course-specific metadata
            elif key == 'COURSE':
                course_name = value.lower()
                if course_name in self.COURSE_NAMES:
                    current_course = self.COURSE_NAMES[course_name]
                    if current_course not in self.metadata.course_data:
                        self.metadata.course_data[current_course] = {}
            elif key == 'LEVEL' and current_course is not None:
                try:
                    self.metadata.course_data[current_course]['LEVEL'] = int(value) if value else 1
                except ValueError:
                    self.metadata.course_data[current_course]['LEVEL'] = 1
            elif key == 'BALLOON' and current_course is not None:
                self.metadata.course_data[current_course]['BALLOON'] = value
            elif key == 'SCOREINIT' and current_course is not None:
                try:
                    # Handle comma-separated values by taking the first number
                    clean_value = value.split(',')[0] if ',' in value else value
                    self.metadata.course_data[current_course]['SCOREINIT'] = int(clean_value) if clean_value else 300
                except ValueError:
                    self.metadata.course_data[current_course]['SCOREINIT'] = 300
            elif key == 'SCOREDIFF' and current_course is not None:
                try:
                    # Handle comma-separated values by taking the first number
                    clean_value = value.split(',')[0] if ',' in value else value
                    self.metadata.course_data[current_course]['SCOREDIFF'] = int(clean_value) if clean_value else 100
                except ValueError:
                    self.metadata.course_data[current_course]['SCOREDIFF'] = 100
    
    def _parse_courses(self):
        """Parse course notation data"""
        in_notation = False
        current_course = None
        notation_lines = []
        
        for line in self.lines:
            if line.startswith('#START'):
                in_notation = True
                # Determine which course this notation belongs to
                if not self.metadata.course_data:
                    # Default to Oni if no course specified
                    current_course = 3
                    self.metadata.course_data[3] = {}
                else:
                    # Use the last defined course
                    current_course = max(self.metadata.course_data.keys())
                continue
            elif line.startswith('#END'):
                in_notation = False
                if current_course is not None and notation_lines:
                    self._parse_course_notation(current_course, notation_lines)
                notation_lines = []
                continue
            
            if in_notation:
                notation_lines.append(line)
    
    def _parse_course_notation(self, course_id: int, notation_lines: List[str]):
        """Parse notation data for a specific course"""
        if course_id not in self.metadata.course_data:
            self.metadata.course_data[course_id] = {}
        
        # Store raw notation lines for later processing
        self.metadata.course_data[course_id]['notation_lines'] = notation_lines
    
    def parse_course_notation(self, course_id: int) -> tuple[List[TJANote], List[TJACommand]]:
        """
        Parse notation data for a specific course into notes and commands
        
        Args:
            course_id: Course difficulty ID (0-6)
            
        Returns:
            Tuple of (notes, commands)
        """
        if course_id not in self.metadata.course_data:
            return [], []
        
        notation_lines = self.metadata.course_data[course_id].get('notation_lines', [])
        if not notation_lines:
            return [], []
        
        notes = []
        commands = []
        current_measure = 0
        current_time_ms = (self.metadata.offset or 0.0) * 1000  # Convert offset to ms
        current_bpm = self.metadata.bpm or 120.0
        time_signature = 4.0  # Default 4/4 time
        current_branch = "normal"
        
        for line in notation_lines:
            line = line.strip()
            
            # Handle commands
            if line.startswith('#'):
                command = self._parse_command(line, current_time_ms, current_measure, current_branch)
                if command:
                    commands.append(command)
                    
                    # Update parser state based on command
                    if command.type == 'BPMCHANGE':
                        current_bpm = float(command.value)
                    elif command.type == 'MEASURE':
                        # Parse time signature (e.g., "4/4")
                        if '/' in str(command.value):
                            num, den = command.value.split('/')
                            time_signature = float(num) / float(den) * 4.0
                    elif command.type in ['N', 'E', 'M']:
                        current_branch = {'N': 'normal', 'E': 'expert', 'M': 'master'}[command.type]
                continue
            
            # Handle measure (ends with comma)
            if line.endswith(','):
                measure_notes = line[:-1]  # Remove comma
                if measure_notes:
                    measure_notes_parsed = self._parse_measure(
                        measure_notes, current_measure, current_time_ms, 
                        current_bpm, time_signature, current_branch
                    )
                    notes.extend(measure_notes_parsed)
                
                # Advance time by one measure
                ms_per_measure = (60000 * time_signature) / current_bpm
                current_time_ms += ms_per_measure
                current_measure += 1
        
        return notes, commands
    
    def _parse_command(self, line: str, timing_ms: float, measure: int, branch: str) -> Optional[TJACommand]:
        """Parse a TJA command line"""
        if not line.startswith('#'):
            return None
        
        # Remove # and split by space
        parts = line[1:].split(' ', 1)
        command_type = parts[0]
        command_value = parts[1] if len(parts) > 1 else None
        
        # Convert value to appropriate type
        if command_value:
            if command_type in ['BPMCHANGE', 'SCROLL', 'DELAY']:
                try:
                    command_value = float(command_value)
                except ValueError:
                    command_value = command_value
            elif command_type == 'MEASURE':
                # Keep as string for time signatures like "4/4"
                pass
        
        return TJACommand(
            type=command_type,
            value=command_value,
            timing_ms=timing_ms,
            measure=measure,
            branch=branch
        )
    
    def _parse_measure(self, measure_data: str, measure_num: int, start_time_ms: float, 
                      bpm: float, time_signature: float, branch: str) -> List[TJANote]:
        """Parse a single measure of notes"""
        notes = []
        
        if not measure_data:
            return notes
        
        # Calculate timing for each note in the measure
        ms_per_measure = (60000 * time_signature) / bpm
        note_count = len(measure_data)
        ms_per_note = ms_per_measure / note_count if note_count > 0 else 0
        
        for i, note_char in enumerate(measure_data):
            if note_char == '0':  # Skip blank notes
                continue
            
            # Convert note character to appropriate type
            note_type = note_char
            if note_char.isdigit():
                note_type = int(note_char)
            
            # Calculate note timing and position
            note_timing = start_time_ms + (i * ms_per_note)
            note_position = i / note_count if note_count > 0 else 0.0
            
            note = TJANote(
                type=note_type,
                position=note_position,
                measure=measure_num,
                timing_ms=note_timing,
                branch=branch
            )
            
            notes.append(note)
        
        return notes
    
    def get_reference_metadata(self) -> Dict:
        """Get reference metadata (NOT for training)"""
        return {
            "title": self.metadata.title or "Unknown",
            "titleen": self.metadata.titleen or "",
            "titleja": self.metadata.titleja or "",
            "subtitle": self.metadata.subtitle or "",
            "subtitleen": self.metadata.subtitleen or "",
            "subtitleja": self.metadata.subtitleja or "",
            "genre": self.metadata.genre or "",
            "maker": self.metadata.maker or "",
            "wave": self.metadata.wave or "",
            "demostart": self.metadata.demostart or 0.0,
            "songvol": self.metadata.songvol or 100,
            "sevol": self.metadata.sevol or 100,
            "side": self.metadata.side or "",
            "bgimage": self.metadata.bgimage or "",
            "bgmovie": self.metadata.bgmovie or "",
            "taikowebskin": self.metadata.taikowebskin or ""
        }
    
    def get_notation_metadata(self) -> Dict:
        """Get notation metadata (FOR training)"""
        return {
            "base_bpm": self.metadata.bpm or 120.0,
            "offset": self.metadata.offset or 0.0,
            "encoding": self.metadata.encoding or "unknown"
        }


def resolve_audio_file_from_wave_field(tja_path: str, wave_field: str) -> Dict:
    """
    Resolve audio file path using WAVE metadata field (specification-compliant)

    Args:
        tja_path: Path to the TJA file
        wave_field: Value from WAVE metadata field

    Returns:
        Dictionary with audio pairing results
    """
    tja_file = Path(tja_path)
    result = {
        "method": "wave_field",
        "wave_metadata": wave_field,
        "resolved_successfully": False,
        "audio_path": None,
        "fallback_used": False,
        "error": None
    }

    # Handle empty WAVE field
    if not wave_field or not wave_field.strip():
        result["error"] = "WAVE field is empty"
        result["method"] = "no_audio_specified"
        return result

    try:
        # Resolve audio file path relative to TJA file directory
        if os.path.isabs(wave_field):
            # Absolute path (rare but possible)
            audio_path = Path(wave_field)
        else:
            # Relative path (standard case)
            audio_path = tja_file.parent / wave_field

        # Normalize and check if file exists
        audio_path = audio_path.resolve()

        if audio_path.exists():
            result["resolved_successfully"] = True
            result["audio_path"] = str(audio_path)
            return result

        # If exact path doesn't exist, try common audio extensions
        audio_extensions = ['.ogg', '.mp3', '.wav']
        base_path = audio_path.with_suffix('')

        for ext in audio_extensions:
            potential_audio = base_path.with_suffix(ext)
            if potential_audio.exists():
                result["resolved_successfully"] = True
                result["audio_path"] = str(potential_audio)
                result["method"] = "wave_field_with_extension_fallback"
                return result

        # WAVE field specified but file not found
        result["error"] = f"Audio file specified in WAVE field not found: {wave_field}"

        # Last resort: try filename-based matching (NOT specification-compliant)
        audio_extensions = ['.ogg', '.mp3', '.wav']
        for ext in audio_extensions:
            fallback_audio = tja_file.with_suffix(ext)
            if fallback_audio.exists():
                result["resolved_successfully"] = True
                result["audio_path"] = str(fallback_audio)
                result["method"] = "filename_fallback"
                result["fallback_used"] = True
                result["error"] = f"WAVE field specified '{wave_field}' but used filename fallback"
                return result

        # No audio file found
        result["error"] = f"No audio file found for WAVE field: {wave_field}"

    except Exception as e:
        result["error"] = f"Error resolving audio file: {str(e)}"

    return result
