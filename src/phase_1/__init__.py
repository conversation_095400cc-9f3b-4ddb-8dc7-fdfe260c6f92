"""
Phase 1: Data Analysis and Preprocessing

This phase implements comprehensive data analysis and preprocessing pipelines
that focus on extracting pure musical notation data for AI model training.
It transforms raw audio files (.ogg) and TJA notation files into structured,
machine-learning-ready datasets.

Key Components:
- TJA parsing with metadata/notation separation
- Audio file pairing using WAVE field specification
- Data validation and quality assessment
- Hardware-optimized processing pipeline

Output: Clean, separated datasets with defined schemas for Phase 2 consumption
"""

from .controller import Phase1Controller
from .data_analyzer import TjaDataAnalyzer
from .metadata_separator import MetadataSeparator
from .notation_validator import NotationValidator
from .tja_processor import TJAProcessor
from .custom_tja_parser import CustomTJAParser, TJAMetadata, TJACourse
from .tja_format_validator import TJAFormatValidator

__all__ = [
    'Phase1Controller',
    'TjaDataAnalyzer',
    'MetadataSeparator',
    'NotationValidator',
    'TJAProcessor',
    'CustomTJAParser',
    'TJAMetadata',
    'TJACourse',
    'TJAFormatValidator'
]
