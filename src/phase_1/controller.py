"""
Phase 1 Controller: Data Analysis and Preprocessing

Refactored controller for Phase 1 implementing unified interface
and enterprise-grade error handling with RTX 3070 optimization.
"""

import argparse
from pathlib import Path
from typing import Any, Dict

from src.phases.base_phase_controller import BasePhaseController
from src.phase_1.data_analyzer import <PERSON><PERSON>D<PERSON>Anal<PERSON><PERSON>
from src.shared.utils.hardware_monitor import get_system_info
from src.shared.utils.hardware_monitor import setup_hardware_optimized_processing
from src.shared.utils.path_manager import PathManager, PathType


class Phase1Controller(BasePhaseController):
    """
    Phase 1: Data Analysis and Preprocessing Controller
    
    Processes ~2,800 TJA files with strict metadata/notation separation
    and hardware optimization for RTX 3070 system.
    """
    
    def __init__(self, config_manager=None, resource_manager=None):
        super().__init__(
            phase_number=1,
            phase_name="Data Analysis and Preprocessing",
            config_manager=config_manager,
            resource_manager=resource_manager
        )
        
        # Initialize path manager
        self.path_manager = PathManager()
        
        # Initialize analyzer (will be created during execution)
        self.analyzer = None
    
    def _validate_prerequisites(self, args: argparse.Namespace) -> bool:
        """Validate Phase 1 prerequisites"""
        self._display_phase_info()
        
        # Validate hardware environment
        if not self._validate_hardware_environment():
            return False
        
        # Determine data directory
        if hasattr(args, 'data_dir') and args.data_dir:
            data_directory = self.path_manager.resolve_path(args.data_dir)
        else:
            data_directory = self.path_manager.get_standardized_path(PathType.DATA_RAW, "ese")
        
        # Validate data directory and discover TJA files
        if not self._validate_and_discover_data(str(data_directory)):
            return False
        
        # Store validated data directory for execution
        self.data_directory = data_directory
        
        return True
    
    def _prepare_execution_params(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Prepare execution parameters from command line arguments"""
        return {
            "data_directory": str(self.data_directory),
            "test_mode": getattr(args, 'test', False),
            "test_count": getattr(args, 'count', 50),
            "validate_only": getattr(args, 'validate_only', False)
        }
    
    def _execute_phase(self, params: Dict[str, Any]) -> bool:
        """Execute Phase 1 processing logic"""
        try:
            # Skip processing if validation only
            if params.get("validate_only", False):
                self.logger.info("Validation complete. Exiting (validate-only mode).")
                return True

            # Initialize analyzer with hardware optimization
            config = setup_hardware_optimized_processing()
            self.analyzer = TjaDataAnalyzer(config)

            self.logger.info(f"Initialized TjaDataAnalyzer with {config['parallel_workers']} workers")
            self.logger.info(f"Memory allocation: {config['memory_per_worker_gb']}GB per worker")
            self.logger.info(f"Batch size: {config['batch_size']} files per batch")

            if params["test_mode"]:
                self.logger.info(f"Test mode: Processing {params['test_count']} files only")

            # Discover TJA files
            tja_files = self.analyzer.discover_tja_files(params["data_directory"])

            if not tja_files:
                self.logger.error("No TJA files found for processing")
                return False

            self.logger.info(f"Processing {len(tja_files)} TJA files...")

            # Process files using the correct method
            results = self.analyzer._process_tja_file_list(
                tja_files,
                test_mode=params["test_mode"],
                test_count=params["test_count"]
            )

            # Generate compliant output structure
            if not self._generate_compliant_output_structure(results):
                self.logger.error("Failed to generate compliant output structure")
                return False

            # Display and log results
            self._display_processing_results(results)

            return True

        except Exception as e:
            self.logger.error(f"Phase 1 execution failed: {e}")
            return False

    def _generate_compliant_output_structure(self, results: Dict[str, Any]) -> bool:
        """Generate documentation-compliant output structure in data/processed/"""
        try:
            from pathlib import Path
            import json
            import time

            # Create data/processed/ directory structure as per documentation
            processed_dir = Path("data/processed")
            processed_dir.mkdir(parents=True, exist_ok=True)

            # Create required subdirectories
            (processed_dir / "reference_metadata").mkdir(exist_ok=True)
            (processed_dir / "notation_data").mkdir(exist_ok=True)
            (processed_dir / "notation_data" / "pure_sequences").mkdir(exist_ok=True)
            (processed_dir / "notation_data" / "timing_structures").mkdir(exist_ok=True)
            (processed_dir / "notation_data" / "pattern_features").mkdir(exist_ok=True)
            (processed_dir / "notation_data" / "difficulty_progressions").mkdir(exist_ok=True)
            (processed_dir / "validation_reports").mkdir(exist_ok=True)

            # Generate catalog.json as per documentation schema
            catalog = self._create_phase1_catalog(results)

            # Write catalog.json
            catalog_path = processed_dir / "catalog.json"
            with open(catalog_path, 'w', encoding='utf-8') as f:
                json.dump(catalog, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Generated compliant catalog.json at {catalog_path}")

            # Generate reference metadata files
            self._generate_reference_metadata(results, processed_dir / "reference_metadata")

            # Generate notation data files
            self._generate_notation_data(results, processed_dir / "notation_data")

            # Generate validation reports
            self._generate_validation_reports(results, processed_dir / "validation_reports")

            self.logger.info("Successfully generated documentation-compliant output structure")
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate compliant output structure: {e}")
            return False

    def _create_phase1_catalog(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create Phase 1 catalog following documentation schema"""
        import time

        # Extract processing statistics from results
        stats = results.get('statistics', {})

        catalog = {
            "phase_metadata": {
                "phase": "1",
                "version": "1.0.0",
                "hardware_optimized": True,
                "processing_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "total_processing_time_seconds": results.get('processing_time', 0),
                "hardware_utilization": {
                    "max_ram_usage_gb": stats.get('max_memory_usage_gb', 0),
                    "cpu_utilization_percent": stats.get('cpu_utilization', 0),
                    "processing_workers": stats.get('parallel_workers', 1)
                }
            },
            "songs": [],
            "processing_statistics": {
                "total_tja_files": stats.get('total_files', 0),
                "wave_field_resolved": stats.get('wave_field_resolved', 0),
                "fallback_filename_matching": stats.get('fallback_matching', 0),
                "missing_audio": stats.get('missing_audio', 0),
                "wave_field_empty": stats.get('wave_field_empty', 0),
                "phase_2_eligible": stats.get('phase_2_eligible', 0)
            },
            "training_statistics": {
                "total_notation_sequences": stats.get('total_sequences', 0),
                "valid_notation_sequences": stats.get('valid_sequences', 0),
                "note_type_distribution": stats.get('note_distribution', {}),
                "difficulty_pattern_distribution": stats.get('difficulty_patterns', {})
            },
            "validation_summary": {
                "overall_success_rate": stats.get('success_rate', 0.0),
                "wave_field_compliance": stats.get('wave_compliance', 0.0),
                "notation_purity_score": stats.get('notation_purity', 0.0),
                "training_readiness": stats.get('training_readiness', 0.0)
            }
        }

        # Add song entries from results
        processed_songs = results.get('processed_songs', [])
        for song_data in processed_songs:
            song_entry = self._create_song_catalog_entry(song_data)
            catalog["songs"].append(song_entry)

        return catalog

    def _create_song_catalog_entry(self, song_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create individual song entry for catalog"""
        return {
            "song_id": song_data.get('song_id', ''),
            "tja_path": song_data.get('tja_path', ''),
            "audio_path": song_data.get('audio_path', ''),
            "audio_pairing": {
                "method": song_data.get('pairing_method', 'wave_field'),
                "wave_metadata": song_data.get('wave_metadata', ''),
                "resolved_successfully": song_data.get('audio_resolved', False),
                "fallback_used": song_data.get('fallback_used', False)
            },
            "reference_metadata": song_data.get('reference_metadata', {}),
            "notation_metadata": song_data.get('notation_metadata', {}),
            "difficulties": song_data.get('difficulties', {}),
            "validation_status": song_data.get('validation_status', 'unknown'),
            "issues": song_data.get('issues', []),
            "phase_2_ready": song_data.get('phase_2_ready', False)
        }

    def _generate_reference_metadata(self, results: Dict[str, Any], ref_dir: Path):
        """Generate reference metadata files"""
        import json

        # Generate song_info.json
        song_info = {}
        for song in results.get('processed_songs', []):
            song_info[song.get('song_id', '')] = song.get('reference_metadata', {})

        with open(ref_dir / "song_info.json", 'w', encoding='utf-8') as f:
            json.dump(song_info, f, indent=2, ensure_ascii=False)

        # Generate genre_mappings.json
        genre_mappings = {}
        for song in results.get('processed_songs', []):
            genre = song.get('reference_metadata', {}).get('genre', '')
            if genre and genre not in genre_mappings:
                genre_mappings[genre] = len(genre_mappings) + 1

        with open(ref_dir / "genre_mappings.json", 'w', encoding='utf-8') as f:
            json.dump(genre_mappings, f, indent=2)

        # Generate creator_info.json
        creator_info = {}
        for song in results.get('processed_songs', []):
            maker = song.get('reference_metadata', {}).get('maker', '')
            if maker and maker not in creator_info:
                creator_info[maker] = {"song_count": 0, "genres": set()}
            if maker:
                creator_info[maker]["song_count"] += 1
                genre = song.get('reference_metadata', {}).get('genre', '')
                if genre:
                    creator_info[maker]["genres"].add(genre)

        # Convert sets to lists for JSON serialization
        for creator in creator_info:
            creator_info[creator]["genres"] = list(creator_info[creator]["genres"])

        with open(ref_dir / "creator_info.json", 'w', encoding='utf-8') as f:
            json.dump(creator_info, f, indent=2, ensure_ascii=False)

        # Generate audio_pairing_report.json
        pairing_report = {
            "wave_field_compliance": results.get('statistics', {}).get('wave_compliance', 0.0),
            "successful_pairings": results.get('statistics', {}).get('wave_field_resolved', 0),
            "fallback_pairings": results.get('statistics', {}).get('fallback_matching', 0),
            "failed_pairings": results.get('statistics', {}).get('missing_audio', 0),
            "empty_wave_fields": results.get('statistics', {}).get('wave_field_empty', 0)
        }

        with open(ref_dir / "audio_pairing_report.json", 'w', encoding='utf-8') as f:
            json.dump(pairing_report, f, indent=2)

    def _generate_notation_data(self, results: Dict[str, Any], notation_dir: Path):
        """Generate notation data files for training"""
        import json

        # Generate pure_sequences data
        pure_seq_dir = notation_dir / "pure_sequences"
        for song in results.get('processed_songs', []):
            song_id = song.get('song_id', '')
            difficulties = song.get('difficulties', {})

            for diff_name, diff_data in difficulties.items():
                notation = diff_data.get('notation', {})
                sequences = notation.get('sequences', [])

                if sequences:
                    seq_file = pure_seq_dir / f"{song_id}_{diff_name}_sequences.json"
                    with open(seq_file, 'w', encoding='utf-8') as f:
                        json.dump(sequences, f, indent=2)

        # Generate timing_structures data
        timing_dir = notation_dir / "timing_structures"
        for song in results.get('processed_songs', []):
            song_id = song.get('song_id', '')
            difficulties = song.get('difficulties', {})

            for diff_name, diff_data in difficulties.items():
                notation = diff_data.get('notation', {})
                timing_commands = notation.get('timing_commands', [])
                measure_structure = notation.get('measure_structure', {})

                timing_data = {
                    "timing_commands": timing_commands,
                    "measure_structure": measure_structure,
                    "base_bpm": song.get('notation_metadata', {}).get('base_bpm', 120.0)
                }

                if timing_commands or measure_structure:
                    timing_file = timing_dir / f"{song_id}_{diff_name}_timing.json"
                    with open(timing_file, 'w', encoding='utf-8') as f:
                        json.dump(timing_data, f, indent=2)

        # Generate pattern_features data
        pattern_dir = notation_dir / "pattern_features"
        pattern_summary = {"total_patterns": 0, "pattern_types": {}}

        for song in results.get('processed_songs', []):
            song_id = song.get('song_id', '')
            difficulties = song.get('difficulties', {})

            for diff_name, diff_data in difficulties.items():
                notation = diff_data.get('notation', {})
                pattern_features = notation.get('pattern_features', {})

                if pattern_features:
                    pattern_file = pattern_dir / f"{song_id}_{diff_name}_patterns.json"
                    with open(pattern_file, 'w', encoding='utf-8') as f:
                        json.dump(pattern_features, f, indent=2)

                    # Update pattern summary
                    pattern_summary["total_patterns"] += len(pattern_features.get('patterns', []))
                    for pattern_type in pattern_features.get('pattern_types', []):
                        pattern_summary["pattern_types"][pattern_type] = \
                            pattern_summary["pattern_types"].get(pattern_type, 0) + 1

        # Save pattern summary
        with open(pattern_dir / "pattern_summary.json", 'w', encoding='utf-8') as f:
            json.dump(pattern_summary, f, indent=2)

        # Generate difficulty_progressions data
        diff_prog_dir = notation_dir / "difficulty_progressions"
        difficulty_analysis = self._analyze_difficulty_progressions(results)

        with open(diff_prog_dir / "difficulty_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(difficulty_analysis, f, indent=2)

    def _analyze_difficulty_progressions(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze difficulty progressions across songs"""
        difficulty_stats = {}

        for song in results.get('processed_songs', []):
            difficulties = song.get('difficulties', {})

            for diff_name, diff_data in difficulties.items():
                if diff_name not in difficulty_stats:
                    difficulty_stats[diff_name] = {
                        "song_count": 0,
                        "avg_note_count": 0,
                        "avg_measure_count": 0,
                        "pattern_complexity": []
                    }

                notation = diff_data.get('notation', {})
                difficulty_stats[diff_name]["song_count"] += 1
                difficulty_stats[diff_name]["avg_note_count"] += notation.get('note_count', 0)
                difficulty_stats[diff_name]["avg_measure_count"] += notation.get('measure_count', 0)

                # Add pattern complexity if available
                pattern_features = notation.get('pattern_features', {})
                complexity = pattern_features.get('complexity_score', 0)
                if complexity > 0:
                    difficulty_stats[diff_name]["pattern_complexity"].append(complexity)

        # Calculate averages
        for diff_name in difficulty_stats:
            stats = difficulty_stats[diff_name]
            if stats["song_count"] > 0:
                stats["avg_note_count"] = stats["avg_note_count"] / stats["song_count"]
                stats["avg_measure_count"] = stats["avg_measure_count"] / stats["song_count"]

                if stats["pattern_complexity"]:
                    stats["avg_pattern_complexity"] = sum(stats["pattern_complexity"]) / len(stats["pattern_complexity"])
                    stats["pattern_complexity"] = []  # Remove raw data to save space

        return difficulty_stats

    def _generate_validation_reports(self, results: Dict[str, Any], validation_dir: Path):
        """Generate validation reports"""
        import json

        # Generate overall validation report
        validation_report = {
            "validation_timestamp": results.get('timestamp', ''),
            "total_files_processed": results.get('statistics', {}).get('total_files', 0),
            "successful_validations": results.get('statistics', {}).get('valid_files', 0),
            "failed_validations": results.get('statistics', {}).get('invalid_files', 0),
            "warnings_count": results.get('statistics', {}).get('warnings_count', 0),
            "errors_count": results.get('statistics', {}).get('errors_count', 0),
            "training_ready_count": results.get('statistics', {}).get('training_ready', 0)
        }

        with open(validation_dir / "validation_summary.json", 'w', encoding='utf-8') as f:
            json.dump(validation_report, f, indent=2)

        # Generate detailed validation issues
        validation_issues = []
        for song in results.get('processed_songs', []):
            if song.get('issues'):
                validation_issues.append({
                    "song_id": song.get('song_id', ''),
                    "tja_path": song.get('tja_path', ''),
                    "validation_status": song.get('validation_status', ''),
                    "issues": song.get('issues', [])
                })

        with open(validation_dir / "validation_issues.json", 'w', encoding='utf-8') as f:
            json.dump(validation_issues, f, indent=2)

        self.logger.info(f"Generated validation reports in {validation_dir}")
    
    def _validate_hardware_environment(self) -> bool:
        """Validate hardware and software environment"""
        self.logger.info("Validating hardware environment...")
        
        system_info = get_system_info()
        
        self.logger.info("Hardware Configuration:")
        self.logger.info(f"  CPU Cores: {system_info['cpu']['physical_cores']} physical, {system_info['cpu']['logical_cores']} logical")
        self.logger.info(f"  Memory: {system_info['memory']['total_gb']:.1f}GB total, {system_info['memory']['available_gb']:.1f}GB available")
        self.logger.info(f"  GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
        
        if system_info['gpu']['cuda_available']:
            self.logger.info(f"    Name: {system_info['gpu']['name']}")
            self.logger.info(f"    Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
        
        # Check requirements and log warnings
        warnings = []
        
        if system_info['cpu']['logical_cores'] < 12:
            warnings.append(f"Recommended 16+ logical cores, found {system_info['cpu']['logical_cores']}")
        
        if system_info['memory']['total_gb'] < 30:
            warnings.append(f"Recommended 32GB RAM, found {system_info['memory']['total_gb']:.1f}GB")
        
        if not system_info['gpu']['cuda_available']:
            warnings.append("CUDA not available - GPU acceleration disabled")
        
        if warnings:
            self.logger.warning("Hardware configuration warnings:")
            for warning in warnings:
                self.logger.warning(f"  {warning}")
        else:
            self.logger.info("Hardware configuration optimal")
        
        return True  # Continue even with warnings
    
    def _validate_and_discover_data(self, data_dir: str) -> bool:
        """Discover and validate TJA data"""
        self.logger.info(f"Discovering and validating data in: {data_dir}")
        
        data_path = Path(data_dir)
        if not data_path.exists():
            self.logger.error(f"Data directory not found: {data_dir}")
            return False
        
        # Discover TJA files
        tja_files = list(data_path.rglob("*.tja"))
        self.logger.info(f"Found {len(tja_files)} TJA files in {data_dir}")
        
        if len(tja_files) == 0:
            self.logger.error("No TJA files found")
            return False
        
        # Sample validation
        self.logger.info("Validating sample files...")
        sample_files = tja_files[:5]  # Validate first 5 files
        
        for i, tja_file in enumerate(sample_files, 1):
            try:
                # Test encoding detection
                from src.shared.utils.encoding_detector import detect_file_encoding
                encoding = detect_file_encoding(str(tja_file))
                
                # Test basic parsing
                from .custom_tja_parser import CustomTJAParser
                parser = CustomTJAParser(str(tja_file))
                
                self.logger.info(f"  {i}. {tja_file.name} - OK (encoding: {encoding})")
                
            except Exception as e:
                self.logger.warning(f"  {i}. {tja_file.name} - Error: {str(e)}")
        
        self.logger.info(f"Data validation complete - {len(tja_files)} files ready for processing")
        return True
    
    def _display_processing_results(self, results: Dict[str, Any]):
        """Display processing results"""
        stats = results['statistics']
        
        self.logger.info("=" * 60)
        self.logger.info("PROCESSING RESULTS")
        self.logger.info("=" * 60)
        self.logger.info(f"Total files: {stats['total_files']}")
        self.logger.info(f"Successful: {stats['successful_files']} ({stats['success_rate']:.1%})")
        self.logger.info(f"Failed: {stats['failed_files']}")
        self.logger.info(f"Speed: {stats['files_per_second']:.1f} files/second")
        self.logger.info(f"WAVE field compliance: {stats['wave_field_compliance']:.1%}")
        self.logger.info(f"Phase 2 eligible: {results['catalog']['processing_statistics']['phase_2_eligible']}")
        
        # Audio pairing statistics
        self.logger.info("Audio Pairing:")
        self.logger.info(f"  WAVE field resolved: {stats['wave_field_resolved']}")
        self.logger.info(f"  Filename fallback: {stats['filename_fallback_used']}")
        self.logger.info(f"  Missing audio: {stats['missing_audio']}")
        self.logger.info(f"  Empty WAVE field: {stats['wave_field_empty']}")
        
        # Training data statistics
        training_stats = results['catalog']['training_statistics']
        self.logger.info("Training Data:")
        self.logger.info(f"  Total note sequences: {training_stats['total_notation_sequences']:,}")
        self.logger.info(f"  Valid sequences: {training_stats['valid_notation_sequences']:,}")
        self.logger.info(f"  Total notes: {training_stats['total_notes']:,}")
        
        # Validation summary
        validation = results['catalog']['validation_summary']
        self.logger.info("Validation Summary:")
        self.logger.info(f"  Overall success rate: {validation['overall_success_rate']:.1%}")
        self.logger.info(f"  Notation purity score: {validation['notation_purity_score']:.1%}")
        self.logger.info(f"  Training readiness: {validation['training_readiness']:.1%}")
        
        self.logger.info("Phase 1 processing complete!")
        self.logger.info("Catalog saved to: data/processed/catalog.json")
        self.logger.info("Statistics saved to: data/processed/processing_statistics.json")
        
        if results['errors']:
            self.logger.warning(f"{len(results['errors'])} errors logged to: data/processed/processing_errors.json")
