"""
TJA Data Analyzer - Phase 1 Processing Pipeline

Refactored data analysis and preprocessing system implementing SOLID principles.
Hardware-optimized for RTX 3070 with consistent naming conventions and error handling.
"""

import json
import time
import logging
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Union
import multiprocessing as mp

from .custom_tja_parser import CustomTJAParser, resolve_audio_file_from_wave_field
from src.shared.utils.base_classes import BaseProcessor, ProcessingResult
from src.shared.config.unified_config_manager import UnifiedConfigManager
from .metadata_separator import MetadataSeparator
from .notation_validator import NotationValidator


@dataclass
class TjaProcessingStatistics:
    """Standardized statistics for TJA processing session"""
    total_files: int = 0
    processed_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    wave_field_resolved: int = 0
    filename_fallback_used: int = 0
    missing_audio: int = 0
    wave_field_empty: int = 0
    processing_time_seconds: float = 0.0
    files_per_second: float = 0.0
    success_rate: float = 0.0
    wave_field_compliance: float = 0.0


@dataclass
class TjaCatalogEntry:
    """Standardized catalog entry for processed TJA song"""
    song_id: str
    tja_file_path: str
    validation_status: str
    processing_timestamp: str
    audio_file_path: Optional[str] = None
    audio_pairing_info: Optional[Dict] = None
    reference_metadata: Optional[Dict] = None
    notation_metadata: Optional[Dict] = None
    difficulty_data: Optional[Dict] = None
    quality_score: float = 0.0
    issues: Optional[List[str]] = None
    phase_2_ready: bool = False

    def __post_init__(self):
        if self.issues is None:
            self.issues = []
        if self.audio_pairing_info is None:
            self.audio_pairing_info = {}
        if self.reference_metadata is None:
            self.reference_metadata = {}
        if self.notation_metadata is None:
            self.notation_metadata = {}
        if self.difficulty_data is None:
            self.difficulty_data = {}


class TjaDataAnalyzer(BaseProcessor):
    """
    Refactored TJA data analysis and preprocessing pipeline

    Implements SOLID principles with standardized error handling,
    consistent naming conventions, and hardware optimization.
    """

    def __init__(self, config: Optional[Dict] = None):
        # Initialize base processor with standardized configuration
        super().__init__(config, "TjaDataAnalyzer")

        # Initialize configuration manager
        self.config_manager = UnifiedConfigManager()
        self.processing_config = self.config_manager.get_phase_config(1)

        # Override config if provided
        if config:
            self.processing_config.update(config)

        # Initialize specialized components
        self.metadata_separator = MetadataSeparator()
        self.notation_validator = NotationValidator()

        # Processing statistics (using new standardized structure)
        self.processing_stats = TjaProcessingStatistics()
        self.start_time = time.time()

        # Setup output directory using config manager
        self.output_directory = self.config_manager.ensure_directory_exists(
            self.processing_config["paths"]["processed_data_directory"]
        )

        self.logger.info("TjaDataAnalyzer initialized with unified configuration")
        self.logger.info(f"Output directory: {self.output_directory}")
        self.logger.info(f"Hardware config: {self.processing_config['hardware']}")

    def process(self, input_data: List[str]) -> ProcessingResult:
        """
        Process TJA files according to BaseProcessor interface

        Args:
            input_data: List of TJA file paths to process

        Returns:
            ProcessingResult with catalog and statistics
        """
        with self._processing_context("tja_file_processing"):
            try:
                # Process TJA files
                catalog_data = self._process_tja_file_list(input_data)

                return ProcessingResult(
                    success=True,
                    data=catalog_data,
                    processing_time_seconds=time.time() - self.start_time,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )

            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=time.time() - self.start_time,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )
    
    def discover_tja_files(self, data_directory: str = "data/raw/ese") -> List[str]:
        """
        Discover all TJA files in the specified data directory

        Args:
            data_directory: Root directory containing TJA files

        Returns:
            List of TJA file paths using standardized path resolution
        """
        # Use config manager for consistent path resolution
        data_path = self.config_manager.resolve_path(data_directory)

        if not data_path.exists():
            raise FileNotFoundError(f"Data directory not found: {data_path}")

        tja_file_paths = []
        for tja_file in data_path.rglob("*.tja"):
            # Ensure consistent path format
            standardized_path = self.config_manager.resolve_path(tja_file)
            tja_file_paths.append(str(standardized_path))

        self.logger.info(f"Discovered {len(tja_file_paths)} TJA files in {data_path}")
        return tja_file_paths
    
    def _process_tja_file_list(self, tja_file_paths: List[str],
                              test_mode: bool = False, test_count: int = 50) -> Dict:
        """
        Process list of TJA files with hardware optimization and error handling

        Args:
            tja_file_paths: List of TJA file paths to process
            test_mode: If True, process only a subset for testing
            test_count: Number of files to process in test mode

        Returns:
            Processing results and catalog data
        """
        if test_mode:
            tja_file_paths = tja_file_paths[:test_count]
            self.logger.info(f"Test mode: Processing {len(tja_file_paths)} files")

        self.processing_stats.total_files = len(tja_file_paths)
        processing_start_time = time.time()

        # Get hardware configuration
        hardware_config = self.processing_config['hardware']
        batch_size = hardware_config.get('batch_size', 8)
        parallel_workers = 1  # Disable parallel processing to avoid pickling issues

        catalog_entries = []
        processing_errors = []

        self.logger.info(f"Starting sequential processing (parallel disabled for stability)")

        # Process files sequentially to avoid pickling issues
        batch_results = []
        for tja_file_path in tja_file_paths:
            result = self._process_single_file(tja_file_path)
            batch_results.append(result)

        # Collect results
        for result in batch_results:
            if result['success']:
                catalog_entries.append(result['catalog_entry'])
                self.processing_stats.successful_files += 1
            else:
                processing_errors.append(result)
                self.processing_stats.failed_files += 1

            self.processing_stats.processed_files += 1

        # Update statistics
        self._update_audio_pairing_stats(batch_results)
            
        # Progress logging
        progress = (self.processing_stats.processed_files / self.processing_stats.total_files) * 100
        self.logger.info(f"Progress: {progress:.1f}% ({self.processing_stats.processed_files}/{self.processing_stats.total_files})")
        
        # Finalize statistics
        self._finalize_statistics()
        
        # Generate catalog
        catalog = self._generate_catalog(catalog_entries)
        
        # Save results
        self._save_results(catalog, processing_errors)
        
        return {
            'catalog': catalog,
            'statistics': asdict(self.processing_stats),
            'errors': processing_errors
        }
    
    def _process_batch_parallel(self, batch_files: List[str], num_workers: int) -> List[Dict]:
        """Process a batch of files in parallel"""
        results = []
        
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_file, file_path): file_path
                for file_path in batch_files
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Log processing time
                    if result['success'] and 'processing_time' in result:
                        self.resource_monitor.log_processing_time(result['processing_time'])
                        
                except Exception as e:
                    self.logger.error(f"Error processing {file_path}: {e}")
                    results.append({
                        'success': False,
                        'file_path': file_path,
                        'error': str(e),
                        'processing_time': 0.0
                    })
        
        return results
    
    def _process_single_file(self, tja_path: str) -> Dict:
        """
        Process a single TJA file
        
        Args:
            tja_path: Path to TJA file
            
        Returns:
            Processing result dictionary
        """
        start_time = time.time()
        
        try:
            # Parse TJA file
            parser = CustomTJAParser(tja_path)
            
            # Get metadata
            reference_metadata = parser.get_reference_metadata()
            notation_metadata = parser.get_notation_metadata()
            
            # Resolve audio file using WAVE field
            wave_field = reference_metadata.get('wave', '')
            audio_pairing = resolve_audio_file_from_wave_field(tja_path, wave_field)
            
            # Process each difficulty
            difficulties = {}
            for course_id, course_data in parser.metadata.course_data.items():
                difficulty_name = parser.DIFFS.get(course_id, f"unknown_{course_id}")
                
                # Parse notation data
                notes, commands = parser.parse_course_notation(course_id)

                # Extract pattern features for validation
                pattern_features = self._extract_pattern_features(notes)

                # Separate metadata and notation
                parser_result = {
                    'reference_metadata': reference_metadata,
                    'notation_metadata': notation_metadata,
                    'notation_data': {
                        difficulty_name: {
                            'reference': {
                                'level': course_data.get('LEVEL', 1),
                                'balloon': course_data.get('BALLOON', ''),
                                'scoreinit': course_data.get('SCOREINIT', 300),
                                'scorediff': course_data.get('SCOREDIFF', 100)
                            },
                            'notation': {
                                'sequences': [asdict(note) for note in notes],
                                'timing_commands': [asdict(cmd) for cmd in commands],
                                'pattern_features': pattern_features,
                                'structure': {
                                    'note_count': len([n for n in notes if n.type != 0]),
                                    'measure_count': max([n.measure for n in notes]) + 1 if notes else 0,
                                    'total_duration_ms': max([n.timing_ms for n in notes]) if notes else 0.0
                                }
                            }
                        }
                    }
                }
                
                # Separate and validate
                separation_result = self.metadata_separator.separate_tja_data(parser_result)
                validation_result = self.notation_validator.validate_notation_data(
                    separation_result.notation_data
                )
                
                difficulties[difficulty_name] = {
                    'reference_data': separation_result.notation_data[difficulty_name]['reference'],
                    'notation_data': separation_result.notation_data[difficulty_name]['notation'],
                    'validation': {
                        'valid': validation_result.valid,
                        'training_ready': validation_result.training_ready,
                        'quality_score': validation_result.quality_score,
                        'warnings': validation_result.warnings,
                        'errors': validation_result.errors,
                        'metrics': validation_result.metrics
                    },
                    'separation_quality': separation_result.separation_quality_score
                }
            
            # Create catalog entry
            song_id = self._generate_song_id(tja_path)

            # Handle relative path calculation safely
            try:
                relative_tja_path = str(Path(tja_path).relative_to(Path.cwd()))
            except ValueError:
                # If relative path fails, use the original path
                relative_tja_path = str(tja_path)

            catalog_entry = CatalogEntry(
                song_id=song_id,
                tja_path=relative_tja_path,
                audio_path=audio_pairing.get('audio_path'),
                audio_pairing=audio_pairing,
                reference_metadata=reference_metadata,
                notation_metadata=notation_metadata,
                difficulties=difficulties,
                validation_status="valid" if all(d['validation']['valid'] for d in difficulties.values()) else "warning",
                issues=[],
                phase_2_ready=audio_pairing['resolved_successfully'] and len(difficulties) > 0
            )
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'catalog_entry': asdict(catalog_entry),
                'processing_time': processing_time,
                'file_path': tja_path
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'processing_time': processing_time,
                'file_path': tja_path
            }
    
    def _generate_song_id(self, tja_path: str) -> str:
        """Generate unique song ID from file path"""
        path = Path(tja_path)
        # Use parent directory name + file name (without extension)
        return f"{path.parent.name}_{path.stem}".replace(' ', '_').replace('・', '_')

    def _extract_pattern_features(self, notes: List) -> Dict:
        """Extract basic pattern features for validation"""
        if not notes:
            return {
                "complexity_score": 0.0,
                "common_patterns": [],
                "note_type_distribution": {}
            }

        # Filter out blank notes
        actual_notes = [n for n in notes if hasattr(n, 'type') and n.type != 0]

        if not actual_notes:
            return {
                "complexity_score": 0.0,
                "common_patterns": [],
                "note_type_distribution": {}
            }

        # Note type distribution
        note_types = []
        for note in actual_notes:
            if hasattr(note, 'type'):
                note_types.append(str(note.type))

        type_counts = {}
        for note_type in note_types:
            type_counts[note_type] = type_counts.get(note_type, 0) + 1

        # Simple complexity calculation
        complexity_score = len(set(note_types)) / 10.0  # Normalize by max note types

        # Extract simple patterns (just note type sequences)
        patterns = []
        for i in range(min(len(note_types) - 3, 20)):  # Limit to 20 patterns
            pattern = note_types[i:i+4]
            patterns.append(pattern)

        return {
            "complexity_score": round(complexity_score, 4),
            "common_patterns": patterns,
            "note_type_distribution": type_counts
        }
    
    def _update_audio_pairing_stats(self, batch_results: List[Dict]):
        """Update audio pairing statistics from batch results"""
        for result in batch_results:
            if not result['success']:
                continue

            catalog_entry = result['catalog_entry']
            audio_pairing = catalog_entry['audio_pairing']

            if audio_pairing['resolved_successfully']:
                if audio_pairing.get('fallback_used', False):
                    self.processing_stats.filename_fallback_used += 1
                else:
                    self.processing_stats.wave_field_resolved += 1
            else:
                if audio_pairing.get('method') == 'no_audio_specified':
                    self.processing_stats.wave_field_empty += 1
                else:
                    self.processing_stats.missing_audio += 1

    def _finalize_statistics(self):
        """Finalize processing statistics"""
        self.processing_stats.processing_time_seconds = time.time() - self.start_time

        if self.processing_stats.processing_time_seconds > 0:
            self.processing_stats.files_per_second = self.processing_stats.processed_files / self.processing_stats.processing_time_seconds

        if self.processing_stats.total_files > 0:
            self.processing_stats.success_rate = self.processing_stats.successful_files / self.processing_stats.total_files

            # Calculate WAVE field compliance
            successful_pairings = self.processing_stats.wave_field_resolved + self.processing_stats.filename_fallback_used
            self.processing_stats.wave_field_compliance = self.processing_stats.wave_field_resolved / self.processing_stats.total_files

    def _generate_catalog(self, catalog_entries: List[Dict]) -> Dict:
        """Generate the main catalog for Phase 2"""
        # Get hardware performance stats (simplified for now)
        performance_stats = {}
        averages = {
            'memory_avg_gb': 0.0,
            'cpu_avg': 0.0,
            'gpu_memory_avg_gb': 0.0
        }

        catalog = {
            "phase_metadata": {
                "phase": "1",
                "version": "1.0.0",
                "hardware_optimized": True,
                "processing_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "total_processing_time_seconds": self.processing_stats.processing_time_seconds,
                "hardware_utilization": {
                    "max_ram_usage_gb": averages.get('memory_avg_gb', 0.0),
                    "cpu_utilization_percent": averages.get('cpu_avg', 0.0),
                    "processing_workers": self.processing_config.get('hardware', {}).get('parallel_workers', 12),
                    "gpu_memory_usage_gb": averages.get('gpu_memory_avg_gb', 0.0)
                }
            },
            "songs": catalog_entries,
            "processing_statistics": {
                "total_tja_files": self.processing_stats.total_files,
                "processed_files": self.processing_stats.processed_files,
                "successful_files": self.processing_stats.successful_files,
                "failed_files": self.processing_stats.failed_files,
                "wave_field_resolved": self.processing_stats.wave_field_resolved,
                "fallback_filename_matching": self.processing_stats.filename_fallback_used,
                "missing_audio": self.processing_stats.missing_audio,
                "wave_field_empty": self.processing_stats.wave_field_empty,
                "phase_2_eligible": sum(1 for entry in catalog_entries if entry['phase_2_ready']),
                "files_per_second": self.processing_stats.files_per_second,
                "success_rate": self.processing_stats.success_rate,
                "wave_field_compliance": self.processing_stats.wave_field_compliance
            },
            "training_statistics": self._calculate_training_statistics(catalog_entries),
            "validation_summary": self._calculate_validation_summary(catalog_entries)
        }

        return catalog

    def _calculate_training_statistics(self, catalog_entries: List[Dict]) -> Dict:
        """Calculate training data statistics"""
        total_sequences = 0
        valid_sequences = 0
        note_type_counts = {}
        difficulty_counts = {}

        for entry in catalog_entries:
            for difficulty, data in entry['difficulties'].items():
                difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1

                notation_data = data.get('notation_data', {})
                sequences = notation_data.get('note_sequences', [])

                total_sequences += len(sequences)
                if data.get('validation', {}).get('valid', False):
                    valid_sequences += len(sequences)

                # Count note types
                for note in sequences:
                    note_type = note.get('type', 'unknown')
                    note_type_counts[note_type] = note_type_counts.get(note_type, 0) + 1

        # Calculate note type distribution percentages
        total_notes = sum(note_type_counts.values())
        note_type_distribution = {}
        if total_notes > 0:
            for note_type, count in note_type_counts.items():
                note_type_distribution[note_type] = (count / total_notes) * 100

        return {
            "total_notation_sequences": total_sequences,
            "valid_notation_sequences": valid_sequences,
            "note_type_distribution": note_type_distribution,
            "difficulty_distribution": difficulty_counts,
            "total_notes": total_notes
        }

    def _calculate_validation_summary(self, catalog_entries: List[Dict]) -> Dict:
        """Calculate validation summary statistics"""
        total_entries = len(catalog_entries)
        if total_entries == 0:
            return {
                "overall_success_rate": 0.0,
                "wave_field_compliance": 0.0,
                "notation_purity_score": 0.0,
                "training_readiness": 0.0
            }

        # Calculate success rates
        valid_entries = sum(1 for entry in catalog_entries if entry['validation_status'] == 'valid')
        phase_2_ready = sum(1 for entry in catalog_entries if entry['phase_2_ready'])
        wave_resolved = sum(1 for entry in catalog_entries if entry['audio_pairing']['resolved_successfully'])

        # Calculate notation purity (average separation quality)
        total_purity = 0.0
        purity_count = 0

        for entry in catalog_entries:
            for difficulty, data in entry['difficulties'].items():
                if 'separation_quality' in data:
                    total_purity += data['separation_quality']
                    purity_count += 1

        notation_purity = total_purity / purity_count if purity_count > 0 else 0.0

        return {
            "overall_success_rate": valid_entries / total_entries,
            "wave_field_compliance": wave_resolved / total_entries,
            "notation_purity_score": notation_purity,
            "training_readiness": phase_2_ready / total_entries
        }

    def _save_results(self, catalog: Dict, errors: List[Dict]):
        """Save processing results to files"""
        # Save main catalog
        catalog_path = self.output_directory / "catalog.json"
        with open(catalog_path, 'w', encoding='utf-8') as f:
            json.dump(catalog, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Catalog saved to {catalog_path}")

        # Save processing errors
        if errors:
            errors_path = self.output_directory / "processing_errors.json"
            with open(errors_path, 'w', encoding='utf-8') as f:
                json.dump(errors, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Processing errors saved to {errors_path}")

        # Save detailed statistics
        stats_path = self.output_directory / "processing_statistics.json"
        detailed_stats = {
            "processing_stats": asdict(self.processing_stats),
            "hardware_performance": {},  # Simplified for now
            "resource_averages": {},     # Simplified for now
            "configuration": self.processing_config
        }

        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_stats, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Detailed statistics saved to {stats_path}")

        # Log summary
        self.logger.info("=" * 60)
        self.logger.info("PROCESSING SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total files: {self.processing_stats.total_files}")
        self.logger.info(f"Successful: {self.processing_stats.successful_files} ({self.processing_stats.success_rate:.1%})")
        self.logger.info(f"Failed: {self.processing_stats.failed_files}")
        self.logger.info(f"Processing time: {self.processing_stats.processing_time_seconds:.1f}s")
        self.logger.info(f"Speed: {self.processing_stats.files_per_second:.1f} files/second")
        self.logger.info(f"WAVE field compliance: {self.processing_stats.wave_field_compliance:.1%}")
        self.logger.info(f"Phase 2 ready: {catalog['processing_statistics']['phase_2_eligible']}")
        self.logger.info("=" * 60)
