"""
TJA Format Validator

Validates TJA files against the official specification for compliance and quality.
"""

import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

from src.shared.utils.encoding_detector import read_tja_file_with_encoding


@dataclass
class ValidationIssue:
    """Single validation issue"""
    severity: str  # 'error', 'warning', 'info'
    message: str
    line_number: Optional[int] = None
    field: Optional[str] = None


@dataclass
class FormatValidationResult:
    """Result of TJA format validation"""
    valid: bool
    compliance_score: float
    issues: List[ValidationIssue]
    metadata_valid: bool
    notation_valid: bool
    encoding_valid: bool


class TJAFormatValidator:
    """Validates TJA files against specification"""
    
    # Required metadata fields
    REQUIRED_FIELDS = {'BPM', 'OFFSET'}
    
    # Valid metadata fields according to specification
    VALID_METADATA_FIELDS = {
        'TITLE', 'TITLEEN', 'TITLEJA', 'TITLECN', 'TITLETW', 'TITLEKO',
        'SUBTITLE', 'SUBTITLEEN', 'SUBTITLEJA', 'SUBTITLECN', 'SUBTITLETW', 'SUBTITLEKO',
        'BPM', 'WAVE', 'OFFSET', 'DEMOSTART', 'GENRE', 'MAKER', 'LYRICS',
        'SONGVOL', 'SEVOL', 'SIDE', 'LIFE', 'GAME', 'HEADSCROLL',
        'BGIMAGE', 'BGMOVIE', 'MOVIEOFFSET', 'TAIKOWEBSKIN'
    }
    
    # Valid course metadata fields
    VALID_COURSE_FIELDS = {
        'COURSE', 'LEVEL', 'BALLOON', 'SCOREINIT', 'SCOREDIFF',
        'BALLOONNOR', 'BALLOONEXP', 'BALLOONMAS', 'STYLE',
        'EXAM1', 'EXAM2', 'EXAM3', 'GAUGEINCR', 'TOTAL', 'HIDDENBRANCH'
    }
    
    # Valid note types
    VALID_NOTE_TYPES = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'F'}
    
    # Valid commands
    VALID_COMMANDS = {
        'START', 'END', 'MEASURE', 'BPMCHANGE', 'DELAY', 'SCROLL',
        'GOGOSTART', 'GOGOEND', 'BARLINEOFF', 'BARLINEON',
        'BRANCHSTART', 'BRANCHEND', 'N', 'E', 'M', 'SECTION', 'LYRIC'
    }
    
    # Valid course names
    VALID_COURSES = {'EASY', 'NORMAL', 'HARD', 'ONI', 'EDIT', 'URA', 'TOWER', 'DAN', '0', '1', '2', '3', '4', '5', '6'}
    
    def __init__(self):
        self.issues = []
    
    def validate_file(self, file_path: str) -> FormatValidationResult:
        """
        Validate a TJA file against the specification
        
        Args:
            file_path: Path to the TJA file
            
        Returns:
            FormatValidationResult with validation details
        """
        self.issues = []
        
        try:
            # Read file with encoding detection
            lines, encoding = read_tja_file_with_encoding(file_path)
            
            # Validate encoding
            encoding_valid = self._validate_encoding(encoding)
            
            # Clean lines (remove comments and empty lines)
            cleaned_lines = []
            for i, line in enumerate(lines, 1):
                # Remove comments
                line = line.split('//')[0].strip()
                if line:
                    cleaned_lines.append((i, line))
            
            # Validate structure
            metadata_valid = self._validate_metadata(cleaned_lines)
            notation_valid = self._validate_notation(cleaned_lines)
            
            # Calculate compliance score
            compliance_score = self._calculate_compliance_score()
            
            # Determine overall validity
            valid = (encoding_valid and metadata_valid and notation_valid and 
                    not any(issue.severity == 'error' for issue in self.issues))
            
            return FormatValidationResult(
                valid=valid,
                compliance_score=compliance_score,
                issues=self.issues,
                metadata_valid=metadata_valid,
                notation_valid=notation_valid,
                encoding_valid=encoding_valid
            )
            
        except Exception as e:
            self.issues.append(ValidationIssue(
                severity='error',
                message=f"Failed to validate file: {str(e)}"
            ))
            
            return FormatValidationResult(
                valid=False,
                compliance_score=0.0,
                issues=self.issues,
                metadata_valid=False,
                notation_valid=False,
                encoding_valid=False
            )
    
    def _validate_encoding(self, encoding: str) -> bool:
        """Validate file encoding"""
        valid_encodings = {'utf-8', 'utf-8-sig', 'shift-jis', 'cp932'}
        
        if encoding not in valid_encodings:
            if encoding == 'unknown':
                self.issues.append(ValidationIssue(
                    severity='error',
                    message="Could not detect file encoding"
                ))
            else:
                self.issues.append(ValidationIssue(
                    severity='warning',
                    message=f"Unusual encoding detected: {encoding}"
                ))
            return False
        
        return True
    
    def _validate_metadata(self, lines: List[Tuple[int, str]]) -> bool:
        """Validate metadata section"""
        metadata_valid = True
        found_fields = set()
        in_notation = False
        
        for line_num, line in lines:
            if line.startswith('#START'):
                in_notation = True
                break
            
            if ':' not in line:
                continue
            
            key, value = line.split(':', 1)
            key = key.strip().upper()
            value = value.strip()
            
            found_fields.add(key)
            
            # Validate field name
            if key not in self.VALID_METADATA_FIELDS and key not in self.VALID_COURSE_FIELDS:
                self.issues.append(ValidationIssue(
                    severity='warning',
                    message=f"Unknown metadata field: {key}",
                    line_number=line_num,
                    field=key
                ))
            
            # Validate specific fields
            if key == 'BPM':
                if not self._validate_bpm(value, line_num):
                    metadata_valid = False
            elif key == 'OFFSET':
                if not self._validate_offset(value, line_num):
                    metadata_valid = False
            elif key == 'LEVEL':
                if not self._validate_level(value, line_num):
                    metadata_valid = False
            elif key == 'COURSE':
                if not self._validate_course(value, line_num):
                    metadata_valid = False
        
        # Check required fields
        for required_field in self.REQUIRED_FIELDS:
            if required_field not in found_fields:
                self.issues.append(ValidationIssue(
                    severity='error',
                    message=f"Missing required field: {required_field}",
                    field=required_field
                ))
                metadata_valid = False
        
        return metadata_valid
    
    def _validate_notation(self, lines: List[Tuple[int, str]]) -> bool:
        """Validate notation section"""
        notation_valid = True
        in_notation = False
        start_found = False
        end_found = False
        
        for line_num, line in lines:
            if line.startswith('#START'):
                in_notation = True
                start_found = True
                continue
            elif line.startswith('#END'):
                in_notation = False
                end_found = True
                continue
            
            if not in_notation:
                continue
            
            # Validate commands
            if line.startswith('#'):
                if not self._validate_command(line, line_num):
                    notation_valid = False
            
            # Validate measures (lines ending with comma)
            elif line.endswith(','):
                measure_data = line[:-1]  # Remove comma
                if not self._validate_measure(measure_data, line_num):
                    notation_valid = False
            
            # Invalid notation line
            else:
                self.issues.append(ValidationIssue(
                    severity='error',
                    message=f"Invalid notation line: {line}",
                    line_number=line_num
                ))
                notation_valid = False
        
        # Check for START/END markers
        if not start_found:
            self.issues.append(ValidationIssue(
                severity='error',
                message="Missing #START marker"
            ))
            notation_valid = False
        
        if not end_found:
            self.issues.append(ValidationIssue(
                severity='error',
                message="Missing #END marker"
            ))
            notation_valid = False
        
        return notation_valid
    
    def _validate_bpm(self, value: str, line_num: int) -> bool:
        """Validate BPM value"""
        try:
            bpm = float(value)
            if not (30 <= bpm <= 400):  # Reasonable BPM range
                self.issues.append(ValidationIssue(
                    severity='warning',
                    message=f"Unusual BPM value: {bpm}",
                    line_number=line_num,
                    field='BPM'
                ))
            return True
        except ValueError:
            self.issues.append(ValidationIssue(
                severity='error',
                message=f"Invalid BPM value: {value}",
                line_number=line_num,
                field='BPM'
            ))
            return False
    
    def _validate_offset(self, value: str, line_num: int) -> bool:
        """Validate OFFSET value"""
        try:
            offset = float(value)
            if abs(offset) > 10:  # Reasonable offset range
                self.issues.append(ValidationIssue(
                    severity='warning',
                    message=f"Large offset value: {offset}",
                    line_number=line_num,
                    field='OFFSET'
                ))
            return True
        except ValueError:
            self.issues.append(ValidationIssue(
                severity='error',
                message=f"Invalid OFFSET value: {value}",
                line_number=line_num,
                field='OFFSET'
            ))
            return False
    
    def _validate_level(self, value: str, line_num: int) -> bool:
        """Validate LEVEL value"""
        try:
            level = int(value)
            if not (1 <= level <= 10):
                self.issues.append(ValidationIssue(
                    severity='warning',
                    message=f"Level out of range (1-10): {level}",
                    line_number=line_num,
                    field='LEVEL'
                ))
            return True
        except ValueError:
            self.issues.append(ValidationIssue(
                severity='error',
                message=f"Invalid LEVEL value: {value}",
                line_number=line_num,
                field='LEVEL'
            ))
            return False
    
    def _validate_course(self, value: str, line_num: int) -> bool:
        """Validate COURSE value"""
        if value.upper() not in self.VALID_COURSES:
            self.issues.append(ValidationIssue(
                severity='error',
                message=f"Invalid COURSE value: {value}",
                line_number=line_num,
                field='COURSE'
            ))
            return False
        return True
    
    def _validate_command(self, line: str, line_num: int) -> bool:
        """Validate notation command"""
        # Remove # and split by space
        parts = line[1:].split(' ', 1)
        command = parts[0]
        
        if command not in self.VALID_COMMANDS:
            self.issues.append(ValidationIssue(
                severity='warning',
                message=f"Unknown command: #{command}",
                line_number=line_num
            ))
            return False
        
        return True
    
    def _validate_measure(self, measure_data: str, line_num: int) -> bool:
        """Validate measure data"""
        if not measure_data:
            return True  # Empty measures are valid
        
        for char in measure_data:
            if char not in self.VALID_NOTE_TYPES:
                self.issues.append(ValidationIssue(
                    severity='error',
                    message=f"Invalid note type: {char}",
                    line_number=line_num
                ))
                return False
        
        return True
    
    def _calculate_compliance_score(self) -> float:
        """Calculate specification compliance score (0.0 to 1.0)"""
        if not self.issues:
            return 1.0
        
        # Weight issues by severity
        error_weight = 1.0
        warning_weight = 0.3
        info_weight = 0.1
        
        total_penalty = 0.0
        for issue in self.issues:
            if issue.severity == 'error':
                total_penalty += error_weight
            elif issue.severity == 'warning':
                total_penalty += warning_weight
            elif issue.severity == 'info':
                total_penalty += info_weight
        
        # Calculate score (max penalty of 10 for normalization)
        score = max(0.0, 1.0 - (total_penalty / 10.0))
        return score
