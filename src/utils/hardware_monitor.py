"""
Hardware Monitoring and Optimization Utilities

Real-time resource monitoring and hardware optimization for RTX 3070 system.
"""

import psutil
import time
import torch
import multiprocessing as mp
from collections import deque
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import logging


@dataclass
class ResourceSnapshot:
    """Single point-in-time resource measurement"""
    timestamp: float
    cpu_percent: float
    memory_gb: float
    memory_percent: float
    gpu_memory_gb: float
    gpu_utilization: float
    disk_io_read_mb: float
    disk_io_write_mb: float


class ResourceMonitor:
    """Real-time resource monitoring for optimization"""
    
    def __init__(self, history_size: int = 100):
        self.cpu_history = deque(maxlen=history_size)
        self.memory_history = deque(maxlen=history_size)
        self.gpu_history = deque(maxlen=history_size)
        self.processing_times = deque(maxlen=1000)
        self.start_time = time.time()
        self.last_disk_io = psutil.disk_io_counters()
        
    def log_usage(self) -> ResourceSnapshot:
        """Log current resource usage and return snapshot"""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        memory_gb = memory.used / (1024**3)
        
        # GPU monitoring (if available)
        gpu_memory_gb = 0.0
        gpu_utilization = 0.0
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.memory_allocated() / (1024**3)
            # Note: GPU utilization requires nvidia-ml-py for detailed stats
            
        # Disk I/O
        current_disk_io = psutil.disk_io_counters()
        disk_read_mb = 0.0
        disk_write_mb = 0.0
        if self.last_disk_io:
            disk_read_mb = (current_disk_io.read_bytes - self.last_disk_io.read_bytes) / (1024**2)
            disk_write_mb = (current_disk_io.write_bytes - self.last_disk_io.write_bytes) / (1024**2)
        self.last_disk_io = current_disk_io
        
        snapshot = ResourceSnapshot(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_gb=memory_gb,
            memory_percent=memory.percent,
            gpu_memory_gb=gpu_memory_gb,
            gpu_utilization=gpu_utilization,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb
        )
        
        # Update histories
        self.cpu_history.append(cpu_percent)
        self.memory_history.append(memory_gb)
        self.gpu_history.append(gpu_memory_gb)
        
        return snapshot
        
    def get_averages(self) -> Dict[str, float]:
        """Get average resource usage over history"""
        return {
            "cpu_avg": sum(self.cpu_history) / len(self.cpu_history) if self.cpu_history else 0.0,
            "memory_avg_gb": sum(self.memory_history) / len(self.memory_history) if self.memory_history else 0.0,
            "gpu_memory_avg_gb": sum(self.gpu_history) / len(self.gpu_history) if self.gpu_history else 0.0,
            "uptime_minutes": (time.time() - self.start_time) / 60
        }
        
    def check_thresholds(self) -> List[str]:
        """Check if resource usage exceeds safe thresholds"""
        warnings = []
        current = self.log_usage()
        
        # CPU threshold (90% sustained)
        if current.cpu_percent > 90:
            warnings.append(f"High CPU usage: {current.cpu_percent:.1f}%")
            
        # Memory threshold (25GB of 32GB)
        if current.memory_gb > 25:
            warnings.append(f"High memory usage: {current.memory_gb:.1f}GB")
            
        # GPU memory threshold (6GB of 8GB)
        if current.gpu_memory_gb > 6:
            warnings.append(f"High GPU memory usage: {current.gpu_memory_gb:.1f}GB")
            
        return warnings
        
    def log_processing_time(self, processing_time: float):
        """Log processing time for performance analysis"""
        self.processing_times.append(processing_time)
        
    def get_performance_stats(self) -> Dict[str, float]:
        """Get processing performance statistics"""
        if not self.processing_times:
            return {"avg_time": 0.0, "min_time": 0.0, "max_time": 0.0, "total_processed": 0}
            
        times = list(self.processing_times)
        return {
            "avg_time": sum(times) / len(times),
            "min_time": min(times),
            "max_time": max(times),
            "total_processed": len(times),
            "files_per_second": len(times) / (time.time() - self.start_time) if time.time() > self.start_time else 0.0
        }


def setup_hardware_optimized_processing() -> Dict:
    """Configure processing pipeline for RTX 3070 system"""
    
    # Verify hardware environment
    if not torch.cuda.is_available():
        logging.warning("CUDA not available - GPU acceleration disabled")
    
    # Check GPU model
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        if "RTX 3070" not in gpu_name:
            logging.warning(f"Expected RTX 3070, found: {gpu_name}")
    
    # Check RAM
    memory = psutil.virtual_memory()
    total_ram_gb = memory.total / (1024**3)
    if total_ram_gb < 30:
        logging.warning(f"Expected 32GB RAM, found: {total_ram_gb:.1f}GB")
    
    # Configure PyTorch for optimal performance
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        torch.set_num_threads(8)  # Physical cores
    
    # Configure multiprocessing for Windows
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass  # Already set
    
    # Hardware-optimized configuration
    processing_config = {
        "parallel_workers": 12,           # Use 12 of 16 logical cores
        "memory_per_worker_gb": 1.5,      # 1.5GB per worker
        "batch_size": 8,                  # Process 8 songs per batch
        "cache_enabled": True,
        "cache_size_gb": 16,              # Use 16GB for caching
        "io_optimization": True,
        "concurrent_reads": 4,            # 4 concurrent file reads
        "write_buffering": True,
        "compression_enabled": True,
        "target_cpu_utilization": 0.75,  # 75% CPU utilization
        "memory_monitoring": True,
        "performance_logging": True,
        "hardware_verified": {
            "cuda_available": torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "None",
            "total_ram_gb": total_ram_gb,
            "cpu_count": psutil.cpu_count(),
            "logical_cpu_count": psutil.cpu_count(logical=True)
        }
    }
    
    return processing_config


def optimize_memory():
    """Trigger garbage collection and clear GPU cache"""
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


def get_system_info() -> Dict:
    """Get comprehensive system information"""
    memory = psutil.virtual_memory()
    
    system_info = {
        "cpu": {
            "physical_cores": psutil.cpu_count(logical=False),
            "logical_cores": psutil.cpu_count(logical=True),
            "frequency": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
        },
        "memory": {
            "total_gb": memory.total / (1024**3),
            "available_gb": memory.available / (1024**3),
            "used_gb": memory.used / (1024**3),
            "percent": memory.percent
        },
        "gpu": {
            "cuda_available": torch.cuda.is_available(),
            "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        }
    }
    
    if torch.cuda.is_available():
        system_info["gpu"].update({
            "name": torch.cuda.get_device_name(0),
            "memory_total_gb": torch.cuda.get_device_properties(0).total_memory / (1024**3),
            "memory_allocated_gb": torch.cuda.memory_allocated(0) / (1024**3),
            "memory_reserved_gb": torch.cuda.memory_reserved(0) / (1024**3)
        })
    
    return system_info


if __name__ == "__main__":
    # Test hardware monitoring
    print("Hardware Monitoring Test")
    print("=" * 40)
    
    system_info = get_system_info()
    print("System Information:")
    for category, info in system_info.items():
        print(f"\n{category.upper()}:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    print("\n" + "=" * 40)
    print("Processing Configuration:")
    config = setup_hardware_optimized_processing()
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 40)
    print("Resource Monitor Test (5 seconds):")
    monitor = ResourceMonitor()
    
    for i in range(5):
        snapshot = monitor.log_usage()
        print(f"  CPU: {snapshot.cpu_percent:.1f}%, Memory: {snapshot.memory_gb:.1f}GB, GPU: {snapshot.gpu_memory_gb:.1f}GB")
        time.sleep(1)
    
    averages = monitor.get_averages()
    print(f"\nAverages: CPU: {averages['cpu_avg']:.1f}%, Memory: {averages['memory_avg_gb']:.1f}GB")
