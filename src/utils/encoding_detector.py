"""
Encoding Detection Utility

Detects TJA file encoding (UTF-8 with BOM, UTF-8, or Shift-JIS) according to specification.
"""

import chardet
from pathlib import Path
from typing import List, <PERSON><PERSON>


def detect_file_encoding(file_path: str) -> str:
    """
    Detect TJA file encoding (UTF-8 with BOM, UTF-8, or Shift-JIS)
    
    Args:
        file_path: Path to the TJA file
        
    Returns:
        String indicating detected encoding: 'utf-8-sig', 'utf-8', 'shift-jis', or 'unknown'
    """
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(1024)  # Read first 1KB for detection
        
        # Check for UTF-8 BOM (Byte Order Mark)
        if raw_data.startswith(b'\xef\xbb\xbf'):
            return "utf-8-sig"
        
        # Try UTF-8 decoding
        try:
            raw_data.decode('utf-8')
            return "utf-8"
        except UnicodeDecodeError:
            pass
        
        # Use chardet for more sophisticated detection
        detection_result = chardet.detect(raw_data)
        if detection_result['encoding']:
            encoding = detection_result['encoding'].lower()
            confidence = detection_result['confidence']
            
            # Map common encodings to our standard names
            if 'utf-8' in encoding:
                return "utf-8"
            elif 'shift' in encoding or 'sjis' in encoding:
                return "shift-jis"
            elif confidence > 0.8:  # High confidence in other encoding
                return encoding
        
        # Fallback: Try Shift-JIS for Japanese TJA files
        try:
            raw_data.decode('shift-jis')
            return "shift-jis"
        except UnicodeDecodeError:
            pass
        
        return "unknown"
        
    except Exception as e:
        print(f"Error detecting encoding for {file_path}: {e}")
        return "unknown"


def read_tja_file_with_encoding(file_path: str) -> Tuple[List[str], str]:
    """
    Read TJA file with automatic encoding detection
    
    Args:
        file_path: Path to the TJA file
        
    Returns:
        Tuple of (lines, detected_encoding)
    """
    encoding = detect_file_encoding(file_path)
    
    if encoding == "unknown":
        # Try common encodings in order
        for fallback_encoding in ['utf-8-sig', 'utf-8', 'shift-jis', 'cp932']:
            try:
                with open(file_path, 'r', encoding=fallback_encoding) as f:
                    lines = f.readlines()
                return lines, fallback_encoding
            except UnicodeDecodeError:
                continue
        
        # If all fail, use utf-8 with error handling
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            lines = f.readlines()
        return lines, 'utf-8-errors'
    
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            lines = f.readlines()
        return lines, encoding
    except UnicodeDecodeError:
        # Fallback to utf-8 with error replacement
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            lines = f.readlines()
        return lines, 'utf-8-errors'


def validate_encoding_detection():
    """
    Validate encoding detection on sample files
    """
    print("Encoding Detection Validation")
    print("=" * 40)
    
    # Test with a sample file from the dataset
    sample_files = [
        "data/raw/ese/01 Pop/1・2・3 ～Koi ga Hajimaru～/1 2 3 Koi ga Hajimaru.tja"
    ]
    
    for file_path in sample_files:
        if Path(file_path).exists():
            encoding = detect_file_encoding(file_path)
            print(f"File: {Path(file_path).name}")
            print(f"Detected encoding: {encoding}")
            
            # Test reading
            try:
                lines, actual_encoding = read_tja_file_with_encoding(file_path)
                print(f"Successfully read {len(lines)} lines with encoding: {actual_encoding}")
                print(f"First line preview: {lines[0][:50]}...")
            except Exception as e:
                print(f"Error reading file: {e}")
            print("-" * 40)


if __name__ == "__main__":
    validate_encoding_detection()
