from typing import Any, Dict, Optional
"""
Phase 6 Configuration Management

Comprehensive configuration system for inference pipeline validation
with hardware optimization and production deployment settings.
"""

import os
import json
import torch
import psutil
from pathlib import Path
from dataclasses import dataclass, field
import logging


@dataclass
class ResourceConfig:
    """Resource management configuration"""
    # Memory limits (auto-detected if not specified)
    max_system_memory_gb: Optional[float] = None
    max_gpu_memory_gb: Optional[float] = None

    # Memory management (optimized for 70-80% utilization target)
    enable_memory_monitoring: bool = True
    memory_warning_threshold: float = 0.80  # 80% usage warning (less conservative)
    memory_critical_threshold: float = 0.90  # 90% usage critical (allows higher utilization)
    cleanup_interval_seconds: float = 30.0

    # Dynamic allocation
    enable_dynamic_batch_sizing: bool = True
    min_batch_size: int = 1
    max_batch_size: int = 8
    memory_per_sample_mb: float = 50.0  # Estimated memory per sample

    # Emergency handling
    enable_emergency_cleanup: bool = True
    oom_retry_attempts: int = 3
    oom_retry_delay_seconds: float = 5.0


@dataclass
class InferenceConfig:
    """Configuration for inference pipeline"""

    # Model settings
    model_path: str = "outputs/phase5_training/final_model.pt"
    device: str = "auto"  # "auto", "cuda", "cpu"

    # Chunked inference settings for long sequences
    use_chunked_inference: bool = True
    max_chunk_size: int = 1000  # Reduced for memory efficiency
    chunk_overlap: int = 100    # Reduced for memory efficiency

    # Performance optimization (conservative defaults)
    use_mixed_precision: bool = False  # Disabled by default for stability
    compile_model: bool = False  # PyTorch 2.0 compilation
    use_tensorrt: bool = False   # TensorRT optimization
    export_onnx: bool = False    # ONNX export

    # Memory management - optimized for RTX 3070
    max_gpu_memory_gb: float = 6.8  # RTX 3070 optimized (85% of 8GB)
    enable_memory_optimization: bool = True
    clear_cache_between_inferences: bool = True
    force_cpu_fallback: bool = True  # Fallback to CPU if GPU memory insufficient
    
    # Preprocessing settings (memory-optimized)
    preprocessing: Dict = field(default_factory=lambda: {
        "sample_rate": 22050,  # Reduced for memory efficiency
        "frame_rate": 25,      # Reduced for memory efficiency
        "normalize_audio": True,
        "apply_preemphasis": True,
        "feature_extraction": {
            "spectral_features": 40,  # Reduced from 80
            "rhythmic_features": 30,  # Reduced from 60
            "temporal_features": 30   # Reduced from 61
        },
        "max_audio_duration_seconds": 300,  # 5 minute limit
        "enable_feature_caching": True
    })
    
    # Post-processing settings
    postprocessing: Dict = field(default_factory=lambda: {
        "note_threshold": 0.5,
        "density_smoothing": True,
        "pattern_coherence_check": True,
        "quantization_enabled": True,
        "minimum_note_gap": 0.05,  # seconds
        "max_notes_per_measure": 32
    })
    
    # Validation settings
    validation: Dict = field(default_factory=lambda: {
        "check_timing_consistency": True,
        "check_difficulty_appropriateness": True,
        "check_musical_coherence": True,
        "generate_quality_score": True,
        "validate_tja_format": True
    })
    
    # Performance settings (memory-conservative)
    batch_size: int = 1  # Start with minimal batch size
    num_workers: int = 1  # Reduced for memory efficiency
    pin_memory: bool = False  # Disabled to save memory


@dataclass
class ValidationConfig:
    """Configuration for validation framework"""
    
    # Validation components
    enable_format_validation: bool = True
    enable_musical_validation: bool = True
    enable_difficulty_validation: bool = True
    enable_timing_validation: bool = True
    
    # Quality thresholds
    minimum_quality_score: float = 0.6
    minimum_playability_score: float = 0.7
    minimum_musical_fit_score: float = 0.65
    
    # Validation weights
    validation_weights: Dict[str, float] = field(default_factory=lambda: {
        "format_validation": 0.2,
        "musical_validation": 0.3,
        "difficulty_validation": 0.3,
        "timing_validation": 0.2
    })
    
    # Reference data paths
    reference_patterns_path: str = "data/reference_patterns.json"
    difficulty_benchmarks_path: str = "data/difficulty_benchmarks.json"


@dataclass
class BenchmarkConfig:
    """Configuration for performance benchmarking"""
    
    # Benchmark categories
    enable_speed_benchmark: bool = True
    enable_memory_benchmark: bool = True
    enable_accuracy_benchmark: bool = True
    enable_scalability_benchmark: bool = True
    enable_robustness_benchmark: bool = True
    
    # Performance targets
    target_inference_time_seconds: float = 5.0
    target_memory_usage_gb: float = 4.0
    target_realtime_factor: float = 10.0
    target_quality_score: float = 0.8
    
    # Test dataset configuration
    test_dataset_path: str = "data/test_dataset"
    max_test_cases: int = 50
    benchmark_iterations: int = 3


@dataclass
class DeploymentConfig:
    """Configuration for production deployment"""
    
    # API settings
    api_host: str = "0.0.0.0"
    api_port: int = 8080
    enable_cors: bool = True
    max_file_size_mb: int = 100
    request_timeout_seconds: int = 300
    
    # Rate limiting
    rate_limit_requests_per_minute: int = 60
    rate_limit_concurrent_requests: int = 5
    
    # Authentication
    require_authentication: bool = False
    api_key_header: str = "X-API-Key"
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    enable_health_checks: bool = True
    health_check_interval_seconds: int = 30
    
    # Optimization
    enable_model_quantization: bool = False
    enable_onnx_export: bool = False
    enable_tensorrt: bool = False


@dataclass
class Phase6Config:
    """
    Comprehensive Phase 6 configuration

    Complete configuration for inference pipeline validation including
    inference settings, validation framework, benchmarking, and deployment.
    """

    # Component configurations
    resource: ResourceConfig = field(default_factory=ResourceConfig)
    inference: InferenceConfig = field(default_factory=InferenceConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    benchmark: BenchmarkConfig = field(default_factory=BenchmarkConfig)
    deployment: DeploymentConfig = field(default_factory=DeploymentConfig)
    
    # General settings
    experiment_name: str = "phase6_inference_validation"
    debug_mode: bool = False
    log_level: str = "INFO"
    
    # Directory settings
    data_directory: str = "data"
    output_directory: str = "outputs/phase6"
    temp_directory: str = "temp/phase6"
    models_directory: str = "models"
    
    # Integration settings
    phase5_model_path: str = "outputs/phase5_training/final_model.pt"
    phase4_quality_config: str = "src/phase4/config.py"
    
    def __post_init__(self):
        """Post-initialization validation and setup"""
        # Create directories
        for directory in [self.output_directory, self.temp_directory]:
            Path(directory).mkdir(parents=True, exist_ok=True)

        # Update model path in inference config
        if Path(self.phase5_model_path).exists():
            self.inference.model_path = self.phase5_model_path

        # Auto-detect resource limits if not specified
        self._auto_detect_resources()

        # Validate configuration
        self._validate_config()

    def _auto_detect_resources(self):
        """Auto-detect system resources and set optimized limits for RTX 3070"""
        # System memory detection with optimized allocation
        if self.resource.max_system_memory_gb is None:
            memory_info = psutil.virtual_memory()
            total_memory_gb = memory_info.total / (1024**3)
            available_memory_gb = memory_info.available / (1024**3)

            # Use 75% of available memory for optimal performance (user preference: 70-80%)
            # Cap at 25GB for system stability while allowing high utilization
            target_memory_gb = available_memory_gb * 0.75
            self.resource.max_system_memory_gb = min(target_memory_gb, 25.0)

            logging.info(f"Auto-detected system memory limit: {self.resource.max_system_memory_gb:.1f}GB "
                        f"(75% of {available_memory_gb:.1f}GB available)")

        # GPU memory detection with RTX 3070 optimization
        if self.resource.max_gpu_memory_gb is None:
            if torch.cuda.is_available():
                gpu_props = torch.cuda.get_device_properties(0)
                gpu_memory_gb = gpu_props.total_memory / (1024**3)
                gpu_name = gpu_props.name

                # RTX 3070 specific optimization
                if "RTX 3070" in gpu_name:
                    # Use 85% of 8GB VRAM = 6.8GB for optimal RTX 3070 performance
                    self.resource.max_gpu_memory_gb = 6.8
                    logging.info(f"RTX 3070 detected: optimized GPU memory limit set to {self.resource.max_gpu_memory_gb:.1f}GB")
                else:
                    # For other GPUs, use 80% of available memory, capped at 6GB
                    self.resource.max_gpu_memory_gb = min(gpu_memory_gb * 0.8, 6.0)
                    logging.info(f"GPU {gpu_name}: memory limit set to {self.resource.max_gpu_memory_gb:.1f}GB "
                               f"(80% of {gpu_memory_gb:.1f}GB)")
            else:
                self.resource.max_gpu_memory_gb = 0.0
                logging.info("No GPU detected, GPU memory limit set to 0")

        # Adjust inference config based on detected resources
        self.inference.max_gpu_memory_gb = min(
            self.inference.max_gpu_memory_gb,
            self.resource.max_gpu_memory_gb
        )

        # Adjust preprocessing based on memory constraints
        if self.resource.max_system_memory_gb < 4.0:
            # Very low memory system - reduce feature dimensions further
            self.inference.preprocessing["feature_extraction"]["spectral_features"] = 20
            self.inference.preprocessing["feature_extraction"]["rhythmic_features"] = 15
            self.inference.preprocessing["feature_extraction"]["temporal_features"] = 15
            self.inference.preprocessing["sample_rate"] = 16000
            self.inference.preprocessing["frame_rate"] = 20
            logging.warning("Applied ultra-low memory preprocessing configuration")

    def _validate_config(self):
        """Validate configuration parameters"""
        # Resource validation
        current_memory_gb = psutil.virtual_memory().total / (1024**3)
        if self.resource.max_system_memory_gb > current_memory_gb:
            logging.warning(f"Requested system memory ({self.resource.max_system_memory_gb:.1f}GB) "
                          f"exceeds available ({current_memory_gb:.1f}GB)")
            self.resource.max_system_memory_gb = current_memory_gb * 0.8

        # Hardware validation
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if self.inference.max_gpu_memory_gb > gpu_memory_gb:
                logging.warning(f"Requested GPU memory ({self.inference.max_gpu_memory_gb}GB) "
                              f"exceeds available ({gpu_memory_gb:.1f}GB)")
                self.inference.max_gpu_memory_gb = gpu_memory_gb * 0.8

        # Model path validation
        if not Path(self.inference.model_path).exists():
            logging.warning(f"Model path does not exist: {self.inference.model_path}")

        # Threshold validation
        if not (0.0 <= self.validation.minimum_quality_score <= 1.0):
            raise ValueError("Quality score thresholds must be between 0.0 and 1.0")

        # Performance target validation
        if self.benchmark.target_inference_time_seconds <= 0:
            raise ValueError("Target inference time must be positive")

        # Resource threshold validation
        if not (0.0 <= self.resource.memory_warning_threshold <= 1.0):
            raise ValueError("Memory warning threshold must be between 0.0 and 1.0")

        if not (0.0 <= self.resource.memory_critical_threshold <= 1.0):
            raise ValueError("Memory critical threshold must be between 0.0 and 1.0")

        if self.resource.memory_warning_threshold >= self.resource.memory_critical_threshold:
            raise ValueError("Memory warning threshold must be less than critical threshold")
    
    def get_resource_config(self) -> Dict[str, Any]:
        """Get resource configuration as dictionary"""
        return {
            "max_system_memory_gb": self.resource.max_system_memory_gb,
            "max_gpu_memory_gb": self.resource.max_gpu_memory_gb,
            "enable_memory_monitoring": self.resource.enable_memory_monitoring,
            "memory_warning_threshold": self.resource.memory_warning_threshold,
            "memory_critical_threshold": self.resource.memory_critical_threshold,
            "cleanup_interval_seconds": self.resource.cleanup_interval_seconds,
            "enable_dynamic_batch_sizing": self.resource.enable_dynamic_batch_sizing,
            "min_batch_size": self.resource.min_batch_size,
            "max_batch_size": self.resource.max_batch_size,
            "memory_per_sample_mb": self.resource.memory_per_sample_mb,
            "enable_emergency_cleanup": self.resource.enable_emergency_cleanup,
            "oom_retry_attempts": self.resource.oom_retry_attempts,
            "oom_retry_delay_seconds": self.resource.oom_retry_delay_seconds
        }

    def get_inference_config(self) -> Dict[str, Any]:
        """Get inference configuration as dictionary"""
        return {
            "model_path": self.inference.model_path,
            "device": self.inference.device,
            "use_chunked_inference": self.inference.use_chunked_inference,
            "max_chunk_size": self.inference.max_chunk_size,
            "chunk_overlap": self.inference.chunk_overlap,
            "use_mixed_precision": self.inference.use_mixed_precision,
            "compile_model": self.inference.compile_model,
            "max_gpu_memory_gb": self.inference.max_gpu_memory_gb,
            "force_cpu_fallback": self.inference.force_cpu_fallback,
            "preprocessing": self.inference.preprocessing,
            "postprocessing": self.inference.postprocessing,
            "validation": self.inference.validation
        }
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration as dictionary"""
        return {
            "enable_format_validation": self.validation.enable_format_validation,
            "enable_musical_validation": self.validation.enable_musical_validation,
            "enable_difficulty_validation": self.validation.enable_difficulty_validation,
            "enable_timing_validation": self.validation.enable_timing_validation,
            "minimum_quality_score": self.validation.minimum_quality_score,
            "validation_weights": self.validation.validation_weights,
            "reference_patterns_path": self.validation.reference_patterns_path,
            "difficulty_benchmarks_path": self.validation.difficulty_benchmarks_path
        }
    
    def get_benchmark_config(self) -> Dict[str, Any]:
        """Get benchmark configuration as dictionary"""
        return {
            "enable_speed_benchmark": self.benchmark.enable_speed_benchmark,
            "enable_memory_benchmark": self.benchmark.enable_memory_benchmark,
            "enable_accuracy_benchmark": self.benchmark.enable_accuracy_benchmark,
            "target_inference_time_seconds": self.benchmark.target_inference_time_seconds,
            "target_memory_usage_gb": self.benchmark.target_memory_usage_gb,
            "target_realtime_factor": self.benchmark.target_realtime_factor,
            "test_dataset_path": self.benchmark.test_dataset_path,
            "max_test_cases": self.benchmark.max_test_cases,
            "benchmark_iterations": self.benchmark.benchmark_iterations
        }
    
    def save_config(self, file_path: str):
        """Save configuration to JSON file"""
        config_dict = {
            "resource": self.resource.__dict__,
            "inference": self.inference.__dict__,
            "validation": self.validation.__dict__,
            "benchmark": self.benchmark.__dict__,
            "deployment": self.deployment.__dict__,
            "experiment_name": self.experiment_name,
            "debug_mode": self.debug_mode,
            "log_level": self.log_level,
            "data_directory": self.data_directory,
            "output_directory": self.output_directory,
            "temp_directory": self.temp_directory,
            "models_directory": self.models_directory,
            "phase5_model_path": self.phase5_model_path,
            "phase4_quality_config": self.phase4_quality_config
        }
        
        with open(file_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    @classmethod
    def load_config(cls, file_path: str) -> 'Phase6Config':
        """Load configuration from JSON file"""
        with open(file_path, 'r') as f:
            config_dict = json.load(f)

        # Create component configs
        resource = ResourceConfig(**config_dict.get("resource", {}))
        inference = InferenceConfig(**config_dict.get("inference", {}))
        validation = ValidationConfig(**config_dict.get("validation", {}))
        benchmark = BenchmarkConfig(**config_dict.get("benchmark", {}))
        deployment = DeploymentConfig(**config_dict.get("deployment", {}))

        # Create main config
        return cls(
            resource=resource,
            inference=inference,
            validation=validation,
            benchmark=benchmark,
            deployment=deployment,
            experiment_name=config_dict.get("experiment_name", "phase6_inference_validation"),
            debug_mode=config_dict.get("debug_mode", False),
            log_level=config_dict.get("log_level", "INFO"),
            data_directory=config_dict.get("data_directory", "data"),
            output_directory=config_dict.get("output_directory", "outputs/phase6"),
            temp_directory=config_dict.get("temp_directory", "temp/phase6"),
            models_directory=config_dict.get("models_directory", "models"),
            phase5_model_path=config_dict.get("phase5_model_path", "outputs/phase5_training/final_model.pt"),
            phase4_quality_config=config_dict.get("phase4_quality_config", "src/phase4/config.py")
        )


def create_phase6_config(
    experiment_name: str = "phase6_inference_validation",
    model_path: Optional[str] = None,
    enable_chunked_inference: bool = True,
    enable_validation: bool = True,
    enable_benchmarking: bool = True,
    debug_mode: bool = False,
    max_system_memory_gb: Optional[float] = None,
    max_gpu_memory_gb: Optional[float] = None
) -> Phase6Config:
    """
    Create Phase 6 configuration with common settings

    Args:
        experiment_name: Name of the experiment
        model_path: Path to trained model (uses Phase 5 output if None)
        enable_chunked_inference: Enable chunked inference for long sequences
        enable_validation: Enable comprehensive validation
        enable_benchmarking: Enable performance benchmarking
        debug_mode: Enable debug mode
        max_system_memory_gb: Maximum system memory to use (auto-detected if None)
        max_gpu_memory_gb: Maximum GPU memory to use (auto-detected if None)

    Returns:
        Configured Phase6Config instance
    """

    # Resource configuration
    resource_config = ResourceConfig(
        max_system_memory_gb=max_system_memory_gb,
        max_gpu_memory_gb=max_gpu_memory_gb,
        enable_memory_monitoring=True,
        enable_dynamic_batch_sizing=True,
        enable_emergency_cleanup=True
    )

    # Inference configuration (conservative settings for memory efficiency)
    inference_config = InferenceConfig(
        use_chunked_inference=enable_chunked_inference,
        compile_model=False,  # Disabled for memory efficiency
        use_mixed_precision=False,  # Disabled for stability
        force_cpu_fallback=True
    )
    
    if model_path:
        inference_config.model_path = model_path
    
    # Validation configuration
    validation_config = ValidationConfig(
        enable_format_validation=enable_validation,
        enable_musical_validation=enable_validation,
        enable_difficulty_validation=enable_validation,
        enable_timing_validation=enable_validation
    )
    
    # Benchmark configuration
    benchmark_config = BenchmarkConfig(
        enable_speed_benchmark=enable_benchmarking,
        enable_memory_benchmark=enable_benchmarking,
        enable_accuracy_benchmark=enable_benchmarking
    )
    
    # Deployment configuration
    deployment_config = DeploymentConfig(
        enable_metrics=True,
        enable_health_checks=True
    )
    
    return Phase6Config(
        resource=resource_config,
        inference=inference_config,
        validation=validation_config,
        benchmark=benchmark_config,
        deployment=deployment_config,
        experiment_name=experiment_name,
        debug_mode=debug_mode,
        log_level="DEBUG" if debug_mode else "INFO"
    )


def validate_environment() -> Dict[str, Any]:
    """Validate Phase 6 environment and dependencies"""
    validation_results = {
        "valid": True,
        "warnings": [],
        "errors": [],
        "hardware": {},
        "dependencies": {}
    }
    
    # Check Python version
    import sys
    if sys.version_info < (3, 8):
        validation_results["errors"].append("Python 3.8+ required")
        validation_results["valid"] = False
    
    # Check PyTorch and CUDA
    validation_results["dependencies"]["pytorch"] = {
        "available": True,
        "version": torch.__version__,
        "cuda_available": torch.cuda.is_available()
    }
    
    if torch.cuda.is_available():
        gpu_props = torch.cuda.get_device_properties(0)
        validation_results["hardware"]["gpu"] = {
            "name": gpu_props.name,
            "memory_gb": gpu_props.total_memory / (1024**3),
            "compute_capability": f"{gpu_props.major}.{gpu_props.minor}"
        }
        
        # Check if RTX 3070 or compatible
        if "RTX 3070" not in gpu_props.name:
            validation_results["warnings"].append(f"GPU {gpu_props.name} may not be optimally supported. RTX 3070 recommended.")
    else:
        validation_results["warnings"].append("CUDA not available. GPU acceleration disabled.")
    
    # Check optional dependencies
    optional_deps = {
        "librosa": "Audio processing",
        "fastapi": "API server",
        "uvicorn": "ASGI server",
        "prometheus_client": "Metrics collection",
        "numpy": "Numerical computing",
        "scipy": "Scientific computing"
    }
    
    for dep, description in optional_deps.items():
        try:
            __import__(dep)
            validation_results["dependencies"][dep] = {"available": True, "description": description}
        except ImportError:
            validation_results["dependencies"][dep] = {"available": False, "description": description}
            if dep in ["librosa", "numpy"]:
                validation_results["errors"].append(f"Required dependency '{dep}' not available: {description}")
                validation_results["valid"] = False
            else:
                validation_results["warnings"].append(f"Optional dependency '{dep}' not available: {description}")
    
    return validation_results
