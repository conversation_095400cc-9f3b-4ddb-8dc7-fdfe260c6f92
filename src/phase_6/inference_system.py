from typing import Any, Dict, List
"""
TJA Inference System

Complete end-to-end inference system for TJA chart generation with
chunked processing, memory optimization, and comprehensive validation.
"""

import torch
import torch.nn as nn
import time
import traceback
import logging
import gc
from pathlib import Path
import numpy as np

from .config import InferenceConfig, Phase6Config
from .audio_preprocessing import AudioPreprocessor
from .tja_postprocessing import TJAPostProcessor
from .validation_framework import TJAValidator
# Avoid circular import - import InferencePerformanceMonitor when needed
from src.phase_4.tja_generator import TJAGeneratorModel
from src.shared.utils.memory_monitor import MemoryMonitor
from src.shared.utils.resource_manager import ResourceManager, memory_efficient


class TJAInferenceSystem:
    """
    Complete TJA chart generation inference system
    
    Provides end-to-end inference pipeline with chunked processing,
    memory optimization, and comprehensive validation for production use.
    """
    
    def __init__(self, config: Phase6Config):
        self.config = config
        self.inference_config = config.inference
        self.logger = logging.getLogger(__name__)

        # Initialize resource manager
        from ..utils.resource_manager import ResourceLimits
        resource_limits = ResourceLimits(
            max_system_memory_gb=config.resource.max_system_memory_gb or 4.0,
            max_gpu_memory_gb=config.resource.max_gpu_memory_gb or 2.0,
            memory_warning_threshold=config.resource.memory_warning_threshold,
            memory_critical_threshold=config.resource.memory_critical_threshold,
            cleanup_interval_seconds=config.resource.cleanup_interval_seconds
        )
        self.resource_manager = ResourceManager(resource_limits)

        # Initialize device with resource constraints
        self.device = self._setup_device()

        # Initialize components
        self.model = None
        self.preprocessor = None
        self.postprocessor = None
        self.validator = None
        self.performance_monitor = None
        self.memory_monitor = MemoryMonitor()
        
        # Performance tracking
        self.inference_stats = {
            "total_inferences": 0,
            "successful_inferences": 0,
            "failed_inferences": 0,
            "total_inference_time": 0.0,
            "average_inference_time": 0.0
        }
        
        # Initialize system
        self._initialize_system()
        
        self.logger.info("TJA Inference System initialized")
    
    def _setup_device(self) -> torch.device:
        """Setup computation device with resource constraints"""
        if self.inference_config.device == "auto":
            # Check GPU availability and memory
            if torch.cuda.is_available():
                gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                required_gpu_memory = self.inference_config.max_gpu_memory_gb

                if gpu_memory_gb >= required_gpu_memory:
                    device = torch.device("cuda")
                    # Set conservative memory fraction
                    memory_fraction = min(0.7, required_gpu_memory / gpu_memory_gb)
                    if hasattr(torch.cuda, 'set_memory_fraction'):
                        torch.cuda.set_memory_fraction(memory_fraction)
                    self.logger.info(f"Using GPU with {memory_fraction:.1%} memory fraction")
                else:
                    device = torch.device("cpu")
                    self.logger.warning(f"GPU memory insufficient ({gpu_memory_gb:.1f}GB < {required_gpu_memory:.1f}GB), using CPU")
            else:
                device = torch.device("cpu")
                self.logger.info("CUDA not available, using CPU")
        else:
            device = torch.device(self.inference_config.device)

        # Force CPU fallback if enabled and memory is very limited
        if (self.inference_config.force_cpu_fallback and
            device.type == "cuda" and
            self.resource_manager.limits.max_gpu_memory_gb < 1.0):
            device = torch.device("cpu")
            self.logger.warning("Forced CPU fallback due to very limited GPU memory")

        self.logger.info(f"Using device: {device}")
        return device
    
    def _initialize_system(self):
        """Initialize all system components"""
        try:
            # Load model
            self.model = self._load_model()
            
            # Initialize preprocessing pipeline
            self.preprocessor = AudioPreprocessor(self.inference_config.preprocessing)
            
            # Initialize post-processing pipeline
            self.postprocessor = TJAPostProcessor(self.inference_config.postprocessing)
            
            # Initialize validation framework
            self.validator = TJAValidator(self.config.get_validation_config())
            
            # Initialize performance monitoring (import here to avoid circular import)
            try:
                from .performance_benchmark import InferencePerformanceMonitor
                self.performance_monitor = InferencePerformanceMonitor()
            except ImportError:
                self.performance_monitor = None
            
            self.logger.info("All system components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize system: {e}")
            raise
    
    def _load_model(self) -> nn.Module:
        """Load trained model from checkpoint"""
        model_path = Path(self.inference_config.model_path)
        
        if not model_path.exists():
            raise FileNotFoundError(f"Model not found: {model_path}")
        
        self.logger.info(f"Loading model from: {model_path}")
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Extract model configuration
            if "model_config" in checkpoint:
                model_config = checkpoint["model_config"]
            else:
                # Use default configuration
                model_config = {
                    "vocab_size": 8,
                    "hidden_size": 512,
                    "num_layers": 6,
                    "num_heads": 8,
                    "dropout": 0.1
                }
                self.logger.warning("Model config not found in checkpoint, using defaults")
            
            # Create model
            model = TJAGeneratorModel(model_config)
            
            # Load state dict
            if "model_state_dict" in checkpoint:
                model.load_state_dict(checkpoint["model_state_dict"])
            else:
                model.load_state_dict(checkpoint)
            
            # Move to device and set to eval mode
            model = model.to(self.device)
            model.eval()
            
            # Apply optimizations
            model = self._optimize_model(model)
            
            self.logger.info("Model loaded and optimized successfully")
            return model
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise
    
    def _optimize_model(self, model: nn.Module) -> nn.Module:
        """Apply model optimizations for inference"""
        try:
            # Model compilation (PyTorch 2.0)
            if self.inference_config.compile_model and hasattr(torch, 'compile'):
                self.logger.info("Compiling model with PyTorch 2.0")
                model = torch.compile(model, mode='reduce-overhead')
            
            # Mixed precision optimization
            if self.inference_config.use_mixed_precision and self.device.type == 'cuda':
                self.logger.info("Enabling mixed precision inference")
                # Note: Mixed precision is handled in forward pass with autocast
            
            # TensorRT optimization (if available and enabled)
            if self.inference_config.use_tensorrt:
                model = self._optimize_with_tensorrt(model)
            
            return model
            
        except Exception as e:
            self.logger.warning(f"Model optimization failed: {e}")
            return model
    
    def _optimize_with_tensorrt(self, model: nn.Module) -> nn.Module:
        """Optimize model with TensorRT"""
        try:
            import torch_tensorrt
            
            self.logger.info("Optimizing model with TensorRT")
            
            # Create sample inputs for compilation
            sample_audio = torch.randn(1, 2000, 201).to(self.device)
            sample_difficulty = torch.tensor([0]).to(self.device)
            
            # Compile with TensorRT
            compiled_model = torch_tensorrt.compile(
                model,
                inputs=[
                    torch_tensorrt.Input(
                        min_shape=[1, 1000, 201],
                        opt_shape=[1, 2000, 201],
                        max_shape=[1, 4000, 201],
                        dtype=torch.float32
                    ),
                    torch_tensorrt.Input(
                        shape=[1],
                        dtype=torch.long
                    )
                ],
                enabled_precisions={torch.float, torch.half}
            )
            
            self.logger.info("TensorRT optimization completed")
            return compiled_model
            
        except ImportError:
            self.logger.warning("TensorRT not available, skipping optimization")
            return model
        except Exception as e:
            self.logger.warning(f"TensorRT optimization failed: {e}")
            return model
    
    @memory_efficient(memory_limit_gb=2.0)
    def generate_chart(self,
                      audio_path: str,
                      bpm: float,
                      offset: float,
                      difficulty_level: int,
                      course_type: str = "oni") -> Dict[str, Any]:
        """
        Generate TJA chart from audio input
        
        Args:
            audio_path: Path to audio file
            bpm: Beats per minute
            offset: Chart offset in seconds
            difficulty_level: Target difficulty (8, 9, or 10)
            course_type: Course type ("oni" or "edit")
            
        Returns:
            Dictionary containing generated chart and validation results
        """
        start_time = time.time()

        # Update statistics
        self.inference_stats["total_inferences"] += 1

        try:
            # Check resource availability before starting
            required_memory = 1.0  # Estimate 1GB system memory needed
            required_gpu_memory = 0.5 if self.device.type == 'cuda' else 0.0

            available, message = self.resource_manager.monitor.check_resource_availability(
                required_memory, required_gpu_memory
            )

            if not available:
                raise MemoryError(f"Insufficient resources for inference: {message}")

            # Allocate resources
            success, alloc_message = self.resource_manager.allocate_resources(
                f"inference_{self.inference_stats['total_inferences']}",
                required_memory, required_gpu_memory
            )

            if not success:
                raise MemoryError(f"Failed to allocate resources: {alloc_message}")

            # Clear GPU cache if enabled
            if self.inference_config.clear_cache_between_inferences and self.device.type == 'cuda':
                torch.cuda.empty_cache()
                gc.collect()

            # Log memory status
            memory_before = self.memory_monitor.get_memory_stats()
            self.logger.debug(f"Memory before inference: GPU {memory_before.gpu_reserved_gb:.2f}GB, RAM {memory_before.system_memory_percent:.1f}%")
            
            # Step 1: Preprocess audio
            preprocessing_start = time.time()
            audio_features, timing_grid = self.preprocessor.process_audio(
                audio_path, bpm, offset
            )
            preprocessing_time = time.time() - preprocessing_start
            
            # Step 2: Model inference
            inference_start = time.time()
            model_outputs = self._model_inference(
                audio_features, timing_grid, difficulty_level
            )
            inference_time = time.time() - inference_start
            
            # Step 3: Post-processing
            postprocessing_start = time.time()
            tja_chart = self.postprocessor.generate_tja(
                model_outputs, timing_grid, bpm, offset, difficulty_level, course_type
            )
            postprocessing_time = time.time() - postprocessing_start
            
            # Step 4: Validation
            validation_start = time.time()
            validation_results = self.validator.validate_generated_chart(
                tja_chart, {
                    "bpm": bpm,
                    "offset": offset,
                    "difficulty_level": difficulty_level,
                    "course_type": course_type,
                    "audio_path": audio_path
                }
            )
            validation_time = time.time() - validation_start
            
            total_time = time.time() - start_time
            
            # Log memory status after inference
            memory_after = self.memory_monitor.get_memory_stats()
            self.logger.debug(f"Memory after inference: GPU {memory_after.gpu_reserved_gb:.2f}GB, RAM {memory_after.system_memory_percent:.1f}%")
            
            # Performance metrics
            audio_duration = len(audio_features) / self.inference_config.preprocessing["frame_rate"]
            realtime_factor = audio_duration / total_time if total_time > 0 else 0
            
            performance_metrics = {
                "total_time": total_time,
                "preprocessing_time": preprocessing_time,
                "inference_time": inference_time,
                "postprocessing_time": postprocessing_time,
                "validation_time": validation_time,
                "audio_duration": audio_duration,
                "realtime_factor": realtime_factor,
                "memory_usage": {
                    "gpu_before_gb": memory_before.gpu_reserved_gb,
                    "gpu_after_gb": memory_after.gpu_reserved_gb,
                    "gpu_delta_gb": memory_after.gpu_reserved_gb - memory_before.gpu_reserved_gb,
                    "ram_percent": memory_after.system_memory_percent
                }
            }
            
            # Update statistics
            self.inference_stats["successful_inferences"] += 1
            self.inference_stats["total_inference_time"] += total_time
            self.inference_stats["average_inference_time"] = (
                self.inference_stats["total_inference_time"] / 
                self.inference_stats["total_inferences"]
            )
            
            # Record performance metrics
            if self.performance_monitor:
                self.performance_monitor.record_inference(performance_metrics)
            
            self.logger.info(f"Chart generation completed in {total_time:.2f}s (realtime factor: {realtime_factor:.1f}x)")
            
            return {
                "tja_chart": tja_chart,
                "validation_results": validation_results,
                "performance_metrics": performance_metrics,
                "model_outputs": model_outputs,
                "success": True
            }

        except MemoryError as e:
            # Handle out-of-memory errors specifically
            self.inference_stats["failed_inferences"] += 1

            self.logger.error(f"Out of memory during inference: {e}")

            # Emergency cleanup
            self.resource_manager.cleanup_memory()

            # Try with reduced settings if configured
            if self.config.resource.oom_retry_attempts > 0:
                self.logger.info("Attempting inference with reduced memory settings")
                return self._retry_with_reduced_memory(
                    audio_path, bpm, offset, difficulty_level, course_type, start_time
                )

            return {
                "success": False,
                "error": f"Out of memory: {str(e)}",
                "error_type": "MemoryError",
                "inference_time": time.time() - start_time,
                "recommendations": [
                    "Reduce audio file length",
                    "Use CPU-only inference",
                    "Increase system memory",
                    "Close other applications"
                ]
            }

        except Exception as e:
            # Update failure statistics
            self.inference_stats["failed_inferences"] += 1

            error_info = {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "inference_time": time.time() - start_time
            }

            self.logger.error(f"Chart generation failed: {e}")
            self.logger.debug(f"Full traceback: {error_info['traceback']}")

            return error_info

        finally:
            # Always deallocate resources
            self.resource_manager.deallocate_resources(
                f"inference_{self.inference_stats['total_inferences']}"
            )

    def _retry_with_reduced_memory(self, audio_path: str, bpm: float, offset: float,
                                  difficulty_level: int, course_type: str,
                                  start_time: float) -> Dict[str, Any]:
        """Retry inference with reduced memory settings"""
        for attempt in range(self.config.resource.oom_retry_attempts):
            try:
                self.logger.info(f"Memory retry attempt {attempt + 1}/{self.config.resource.oom_retry_attempts}")

                # Wait before retry
                time.sleep(self.config.resource.oom_retry_delay_seconds)

                # Apply increasingly aggressive memory reductions
                if attempt == 0:
                    # First attempt: reduce chunk size
                    original_chunk_size = self.inference_config.max_chunk_size
                    self.inference_config.max_chunk_size = max(500, original_chunk_size // 2)
                    self.logger.info(f"Reduced chunk size to {self.inference_config.max_chunk_size}")

                elif attempt == 1:
                    # Second attempt: force CPU and reduce chunk size further
                    self.device = torch.device("cpu")
                    self.inference_config.max_chunk_size = max(250, self.inference_config.max_chunk_size // 2)
                    self.logger.info(f"Switched to CPU, chunk size: {self.inference_config.max_chunk_size}")

                else:
                    # Final attempt: minimal settings
                    self.inference_config.max_chunk_size = 100
                    self.inference_config.use_mixed_precision = False
                    self.inference_config.compile_model = False
                    self.logger.info("Applied minimal memory settings")

                # Emergency cleanup before retry
                self.resource_manager.cleanup_memory()

                # Recursive call with reduced settings
                return self.generate_chart(audio_path, bpm, offset, difficulty_level, course_type)

            except MemoryError as e:
                self.logger.warning(f"Retry attempt {attempt + 1} failed: {e}")
                continue
            except Exception as e:
                self.logger.error(f"Retry attempt {attempt + 1} failed with non-memory error: {e}")
                break

        # All retries failed
        return {
            "success": False,
            "error": "All memory retry attempts failed",
            "error_type": "MemoryError",
            "inference_time": time.time() - start_time,
            "retry_attempts": self.config.resource.oom_retry_attempts,
            "recommendations": [
                "Use a shorter audio file (< 2 minutes)",
                "Increase available system memory",
                "Close other memory-intensive applications",
                "Use a system with more RAM"
            ]
        }
    
    def _model_inference(self, 
                        audio_features: torch.Tensor, 
                        timing_grid: Dict, 
                        difficulty_level: int) -> Dict[str, Any]:
        """Perform model inference with optimizations"""
        
        # Prepare inputs
        audio_features = audio_features.unsqueeze(0).to(self.device)  # Add batch dimension
        difficulty_tensor = torch.tensor([difficulty_level - 8]).to(self.device)  # 0, 1, 2 for levels 8, 9, 10
        
        # Create attention mask for variable length sequences
        seq_len = audio_features.shape[1]
        attention_mask = torch.ones(1, seq_len, dtype=torch.bool).to(self.device)
        
        # Model inference with memory optimization
        with torch.no_grad():
            if self.inference_config.use_mixed_precision and self.device.type == 'cuda':
                with torch.autocast(device_type='cuda', dtype=torch.float16):
                    outputs = self._run_inference(audio_features, difficulty_tensor, attention_mask, seq_len)
            else:
                outputs = self._run_inference(audio_features, difficulty_tensor, attention_mask, seq_len)
        
        # Move outputs to CPU and convert for post-processing
        cpu_outputs = {}
        for key, value in outputs.items():
            if torch.is_tensor(value):
                cpu_outputs[key] = value.squeeze(0).cpu()  # Remove batch dimension
            else:
                cpu_outputs[key] = value
        
        return cpu_outputs
    
    def _run_inference(self, 
                      audio_features: torch.Tensor,
                      difficulty_tensor: torch.Tensor,
                      attention_mask: torch.Tensor,
                      seq_len: int) -> Dict[str, Any]:
        """Run model inference with chunking if needed"""
        
        # Check if chunked inference is needed
        if (self.inference_config.use_chunked_inference and 
            seq_len > self.inference_config.max_chunk_size):
            
            self.logger.debug(f"Using chunked inference for sequence length {seq_len}")
            return self._chunked_inference(audio_features, difficulty_tensor, attention_mask)
        else:
            # Standard inference
            return self.model(
                audio_features=audio_features,
                difficulty=difficulty_tensor,
                attention_mask=attention_mask,
                return_loss=False
            )
    
    def _chunked_inference(self, 
                          audio_features: torch.Tensor,
                          difficulty_tensor: torch.Tensor,
                          attention_mask: torch.Tensor) -> Dict[str, Any]:
        """Perform inference on long sequences using chunking"""
        
        seq_len = audio_features.shape[1]
        chunk_size = self.inference_config.max_chunk_size
        overlap = self.inference_config.chunk_overlap
        
        chunked_outputs = []
        
        for start_idx in range(0, seq_len, chunk_size - overlap):
            end_idx = min(start_idx + chunk_size, seq_len)
            
            # Extract chunk
            chunk_audio = audio_features[:, start_idx:end_idx]
            chunk_mask = attention_mask[:, start_idx:end_idx]
            
            # Inference on chunk
            chunk_outputs = self.model(
                audio_features=chunk_audio,
                difficulty=difficulty_tensor,
                attention_mask=chunk_mask,
                return_loss=False
            )
            
            # Handle overlap blending
            if start_idx > 0 and len(chunked_outputs) > 0:
                chunk_outputs = self._blend_chunk_overlap(
                    chunked_outputs[-1], chunk_outputs, overlap
                )
            
            chunked_outputs.append(chunk_outputs)
        
        # Concatenate chunks
        return self._concatenate_chunks(chunked_outputs, overlap)
    
    def _blend_chunk_overlap(self, 
                           prev_chunk: Dict[str, torch.Tensor],
                           curr_chunk: Dict[str, torch.Tensor],
                           overlap: int) -> Dict[str, torch.Tensor]:
        """Blend overlapping regions between chunks"""
        
        blended_chunk = {}
        overlap_start = overlap // 2
        
        for key, curr_value in curr_chunk.items():
            if torch.is_tensor(curr_value) and len(curr_value.shape) > 1:
                # Apply fade-in to current chunk
                fade_in = torch.linspace(0, 1, overlap_start).unsqueeze(-1).to(curr_value.device)
                curr_value[:, :overlap_start] *= fade_in
                
                # Add blended overlap from previous chunk
                if key in prev_chunk:
                    prev_value = prev_chunk[key]
                    fade_out = torch.linspace(1, 0, overlap_start).unsqueeze(-1).to(prev_value.device)
                    prev_overlap = prev_value[:, -overlap_start:] * fade_out
                    curr_value[:, :overlap_start] += prev_overlap
            
            blended_chunk[key] = curr_value
        
        return blended_chunk
    
    def _concatenate_chunks(self, 
                           chunked_outputs: List[Dict[str, torch.Tensor]],
                           overlap: int) -> Dict[str, torch.Tensor]:
        """Concatenate chunks with proper overlap handling"""
        
        if not chunked_outputs:
            return {}
        
        if len(chunked_outputs) == 1:
            return chunked_outputs[0]
        
        final_outputs = {}
        
        for key in chunked_outputs[0].keys():
            if torch.is_tensor(chunked_outputs[0][key]):
                # Handle overlapping concatenation
                concatenated_values = []
                
                for i, chunk_output in enumerate(chunked_outputs):
                    value = chunk_output[key]
                    
                    if i > 0:
                        # Remove overlap from all chunks except the first
                        value = value[:, overlap//2:]
                    if i < len(chunked_outputs) - 1:
                        # Remove overlap from all chunks except the last
                        value = value[:, :-overlap//2]
                    
                    concatenated_values.append(value)
                
                final_outputs[key] = torch.cat(concatenated_values, dim=1)
            else:
                final_outputs[key] = chunked_outputs[0][key]
        
        return final_outputs
    
    def get_inference_statistics(self) -> Dict[str, Any]:
        """Get inference statistics"""
        return {
            **self.inference_stats,
            "success_rate": (
                self.inference_stats["successful_inferences"] / 
                max(1, self.inference_stats["total_inferences"])
            ),
            "performance_monitor": (
                self.performance_monitor.get_statistics() 
                if self.performance_monitor else None
            )
        }
    
    def reset_statistics(self):
        """Reset inference statistics"""
        self.inference_stats = {
            "total_inferences": 0,
            "successful_inferences": 0,
            "failed_inferences": 0,
            "total_inference_time": 0.0,
            "average_inference_time": 0.0
        }
        
        if self.performance_monitor:
            self.performance_monitor.reset_statistics()
