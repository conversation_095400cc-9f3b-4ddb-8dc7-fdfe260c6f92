from typing import (
    Any,
    Dict,
    List,
    Optional
)
"""
Production-Ready TJA Inference System

Complete implementation based on Phase 6 RFP specifications for RTX 3070 optimization.
Provides end-to-end inference pipeline with chunked processing, advanced validation,
and comprehensive performance monitoring.

Hardware Optimization Targets:
- GPU: RTX 3070 (8GB VRAM, 6.8GB usable)
- CPU: 16 cores, 8 workers for real-time processing
- RAM: 31.8GB total, 28GB available, 16GB cache, 8GB inference
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import time
import traceback
import logging
import gc
import os
import psutil
from pathlib import Path
from dataclasses import dataclass, field
import numpy as np

from .config import Phase6Config
from .audio_preprocessing import AudioPreprocessor
from .validation_framework import TJAValidator
from src.shared.utils.resource_manager import ResourceManager, memory_efficient
from src.shared.utils.memory_monitor import MemoryMonitor


@dataclass
class InferenceMetrics:
    """Comprehensive inference performance metrics"""
    total_time: float = 0.0
    preprocessing_time: float = 0.0
    inference_time: float = 0.0
    postprocessing_time: float = 0.0
    validation_time: float = 0.0
    audio_duration: float = 0.0
    realtime_factor: float = 0.0
    memory_usage: Dict[str, float] = field(default_factory=dict)
    gpu_utilization: float = 0.0
    cpu_utilization: float = 0.0
    quality_score: float = 0.0
    success: bool = False
    error_message: Optional[str] = None


class TJAInferenceSystem:
    """
    Production-ready TJA chart generation inference system
    
    Implements complete end-to-end pipeline optimized for RTX 3070 hardware
    with comprehensive validation, performance monitoring, and error handling.
    """
    
    def __init__(self, config: Phase6Config):
        self.config = config
        self.inference_config = config.inference
        self.logger = logging.getLogger(__name__)
        
        # Initialize resource management
        self.resource_manager = ResourceManager()
        self.memory_monitor = MemoryMonitor()
        
        # Performance tracking
        self.inference_stats = {
            "total_inferences": 0,
            "successful_inferences": 0,
            "failed_inferences": 0,
            "average_inference_time": 0.0,
            "average_realtime_factor": 0.0,
            "peak_memory_usage": 0.0,
            "total_processing_time": 0.0
        }
        
        # Initialize device with hardware optimization
        self.device = self._setup_optimized_device()
        
        # Initialize components
        self.model = None
        self.preprocessor = None
        self.postprocessor = None
        self.validator = None
        
        # Model optimization flags
        self.model_optimized = False
        self.tensorrt_enabled = False
        
        self.logger.info(f"TJA Inference System initialized for device: {self.device}")
        self.logger.info(f"Resource limits: RAM {self.resource_manager.limits.max_system_memory_gb:.1f}GB, "
                        f"GPU {self.resource_manager.limits.max_gpu_memory_gb:.1f}GB")
    
    def _setup_optimized_device(self) -> torch.device:
        """Setup optimized device for RTX 3070 hardware"""
        if self.inference_config.device == "auto":
            if torch.cuda.is_available():
                # RTX 3070 specific optimizations
                device = torch.device("cuda")
                
                # Get GPU properties
                gpu_props = torch.cuda.get_device_properties(0)
                gpu_memory_gb = gpu_props.total_memory / (1024**3)
                
                self.logger.info(f"GPU detected: {gpu_props.name}")
                self.logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB")
                
                # Set memory fraction for RTX 3070 (6.8GB usable from 8GB)
                if "RTX 3070" in gpu_props.name:
                    memory_fraction = 0.85  # 6.8GB / 8GB
                    torch.cuda.set_memory_fraction(memory_fraction)
                    self.logger.info(f"RTX 3070 optimizations applied: {memory_fraction:.1%} memory fraction")
                else:
                    # Conservative setting for other GPUs
                    memory_fraction = min(0.8, self.inference_config.max_gpu_memory_gb / gpu_memory_gb)
                    torch.cuda.set_memory_fraction(memory_fraction)
                
                # Enable optimizations for RTX 3070
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                
                return device
            else:
                self.logger.warning("CUDA not available, using CPU")
                return torch.device("cpu")
        else:
            return torch.device(self.inference_config.device)
    
    def initialize_components(self):
        """Initialize all inference pipeline components"""
        try:
            self.logger.info("Initializing inference pipeline components...")
            
            # Initialize preprocessor
            self.preprocessor = AudioPreprocessor(self.inference_config.preprocessing)
            self.logger.info("Audio preprocessor initialized")
            
            # Initialize postprocessor
            self.postprocessor = TJAPostProcessor(self.inference_config.postprocessing)
            self.logger.info("TJA postprocessor initialized")
            
            # Initialize validator
            self.validator = TJAValidator(self.config.get_validation_config())
            self.logger.info("TJA validator initialized")
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise
    
    def load_model(self, model_path: str):
        """Load and optimize trained model"""
        try:
            self.logger.info(f"Loading model from: {model_path}")
            
            if not Path(model_path).exists():
                raise FileNotFoundError(f"Model file not found: {model_path}")
            
            # Load model checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Extract model configuration
            model_config = checkpoint.get("model_config", {})
            
            # Initialize model architecture
            self.model = TJAGeneratorModel(model_config)
            
            # Load model weights
            if "model_state_dict" in checkpoint:
                self.model.load_state_dict(checkpoint["model_state_dict"])
            else:
                self.model.load_state_dict(checkpoint)
            
            # Move to device and set to eval mode
            self.model = self.model.to(self.device)
            self.model.eval()
            
            # Apply model optimizations
            self._optimize_model()
            
            self.logger.info("Model loaded and optimized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise
    
    def _optimize_model(self):
        """Apply production optimizations to model"""
        try:
            # PyTorch 2.0 compilation
            if hasattr(torch, 'compile') and self.inference_config.get('compile_model', False):
                self.logger.info("Applying PyTorch 2.0 compilation...")
                self.model = torch.compile(self.model, mode='reduce-overhead')
                self.model_optimized = True
            
            # Mixed precision setup
            if self.inference_config.use_mixed_precision and self.device.type == 'cuda':
                self.logger.info("Mixed precision enabled")
            
            # TensorRT optimization (if available)
            if self.inference_config.get('use_tensorrt', False):
                self._apply_tensorrt_optimization()
            
            self.logger.info("Model optimization completed")
            
        except Exception as e:
            self.logger.warning(f"Model optimization failed: {e}")
    
    def _apply_tensorrt_optimization(self):
        """Apply TensorRT optimization for RTX 3070"""
        try:
            import torch_tensorrt
            
            self.logger.info("Applying TensorRT optimization...")
            
            # Create sample inputs for TensorRT compilation
            sample_audio = torch.randn(1, 2000, 201).to(self.device)  # 201-dimensional features
            sample_difficulty = torch.tensor([1]).to(self.device)  # Difficulty 9 (index 1)
            
            # Compile with TensorRT
            self.model = torch_tensorrt.compile(
                self.model,
                inputs=[
                    torch_tensorrt.Input(shape=[1, -1, 201], dtype=torch.float32),  # Variable length audio
                    torch_tensorrt.Input(shape=[1], dtype=torch.long)  # Difficulty
                ],
                enabled_precisions={torch.float32, torch.half},
                workspace_size=1 << 30,  # 1GB workspace
                max_batch_size=1
            )
            
            self.tensorrt_enabled = True
            self.logger.info("TensorRT optimization applied successfully")
            
        except ImportError:
            self.logger.warning("TensorRT not available, skipping optimization")
        except Exception as e:
            self.logger.warning(f"TensorRT optimization failed: {e}")
    
    @memory_efficient(memory_limit_gb=4.0)
    def generate_chart(self, 
                      audio_path: str, 
                      bpm: float, 
                      offset: float,
                      difficulty_level: int, 
                      course_type: str = "oni",
                      metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate TJA chart from audio input with comprehensive validation
        
        Args:
            audio_path: Path to audio file (.ogg format)
            bpm: Beats per minute
            offset: Chart offset in seconds
            difficulty_level: Target difficulty (8, 9, or 10)
            course_type: Course type ("oni" or "edit")
            metadata: Additional metadata for chart generation
            
        Returns:
            Complete inference result with chart, validation, and metrics
        """
        start_time = time.time()
        metrics = InferenceMetrics()
        
        # Update statistics
        self.inference_stats["total_inferences"] += 1
        
        try:
            # Validate inputs
            self._validate_inputs(audio_path, bpm, offset, difficulty_level, course_type)
            
            # Check resource availability
            self._check_resource_availability(audio_path)
            
            # Allocate resources
            resource_id = f"inference_{self.inference_stats['total_inferences']}"
            self.resource_manager.allocate_resources(resource_id, memory_gb=2.0, gpu_memory_gb=1.0)
            
            try:
                # Step 1: Audio preprocessing
                preprocessing_start = time.time()
                self.logger.debug("Starting audio preprocessing...")
                
                audio_features, timing_grid = self.preprocessor.process_audio(
                    audio_path, bpm, offset
                )
                
                metrics.preprocessing_time = time.time() - preprocessing_start
                metrics.audio_duration = len(audio_features) / self.inference_config.preprocessing["frame_rate"]
                
                self.logger.debug(f"Preprocessing completed: {audio_features.shape} features, "
                                f"{metrics.audio_duration:.1f}s audio")
                
                # Step 2: Model inference
                inference_start = time.time()
                self.logger.debug("Starting model inference...")
                
                with torch.no_grad():
                    model_outputs = self._model_inference(
                        audio_features, timing_grid, difficulty_level
                    )
                
                metrics.inference_time = time.time() - inference_start
                self.logger.debug(f"Model inference completed in {metrics.inference_time:.3f}s")
                
                # Step 3: Post-processing
                postprocessing_start = time.time()
                self.logger.debug("Starting post-processing...")
                
                tja_chart = self.postprocessor.generate_tja(
                    model_outputs, timing_grid, bpm, offset, difficulty_level, course_type,
                    metadata=metadata
                )
                
                metrics.postprocessing_time = time.time() - postprocessing_start
                self.logger.debug(f"Post-processing completed in {metrics.postprocessing_time:.3f}s")
                
                # Step 4: Comprehensive validation
                validation_start = time.time()
                self.logger.debug("Starting validation...")
                
                validation_results = self.validator.validate_generated_chart(
                    tja_chart, {"bpm": bpm, "difficulty_level": difficulty_level, "audio_duration": metrics.audio_duration}
                )
                
                metrics.validation_time = time.time() - validation_start
                metrics.quality_score = validation_results.get("overall_score", 0.0)
                
                self.logger.debug(f"Validation completed: score {metrics.quality_score:.3f}")
                
                # Calculate final metrics
                metrics.total_time = time.time() - start_time
                metrics.realtime_factor = metrics.audio_duration / metrics.total_time if metrics.total_time > 0 else 0
                metrics.memory_usage = self.memory_monitor.get_memory_stats().__dict__
                metrics.success = True
                
                # Update statistics
                self.inference_stats["successful_inferences"] += 1
                self._update_performance_stats(metrics)
                
                self.logger.info(f"Chart generation successful: {metrics.realtime_factor:.1f}x realtime, "
                               f"quality {metrics.quality_score:.3f}")
                
                return {
                    "success": True,
                    "tja_chart": tja_chart,
                    "validation_results": validation_results,
                    "performance_metrics": metrics.__dict__,
                    "model_outputs": model_outputs,
                    "timing_grid": timing_grid
                }
                
            finally:
                # Always deallocate resources
                self.resource_manager.deallocate_resources(resource_id)
                
        except Exception as e:
            # Handle errors
            metrics.total_time = time.time() - start_time
            metrics.success = False
            metrics.error_message = str(e)
            
            self.inference_stats["failed_inferences"] += 1
            
            self.logger.error(f"Chart generation failed: {e}")
            
            # Check if it's a memory error and attempt recovery
            if isinstance(e, (RuntimeError, MemoryError)) and "memory" in str(e).lower():
                return self._handle_memory_error(audio_path, bpm, offset, difficulty_level, course_type, start_time)
            
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "performance_metrics": metrics.__dict__,
                "recommendations": self._generate_error_recommendations(e)
            }

    def _validate_inputs(self, audio_path: str, bpm: float, offset: float,
                        difficulty_level: int, course_type: str):
        """Validate input parameters"""
        if not Path(audio_path).exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        if not audio_path.lower().endswith(('.ogg', '.wav', '.mp3')):
            raise ValueError("Audio file must be .ogg, .wav, or .mp3 format")

        if not (60 <= bpm <= 300):
            raise ValueError(f"BPM must be between 60 and 300, got {bpm}")

        if not (-10.0 <= offset <= 10.0):
            raise ValueError(f"Offset must be between -10.0 and 10.0 seconds, got {offset}")

        if difficulty_level not in [8, 9, 10]:
            raise ValueError(f"Difficulty level must be 8, 9, or 10, got {difficulty_level}")

        if course_type not in ["oni", "edit"]:
            raise ValueError(f"Course type must be 'oni' or 'edit', got {course_type}")

    def _check_resource_availability(self, audio_path: str):
        """Check if sufficient resources are available for inference"""
        # Estimate resource requirements based on audio file size
        file_size_mb = Path(audio_path).stat().st_size / (1024 * 1024)
        estimated_memory_gb = max(1.0, file_size_mb / 50.0)  # Rough estimate
        estimated_gpu_memory_gb = max(0.5, file_size_mb / 100.0) if self.device.type == 'cuda' else 0.0

        available, message = self.resource_manager.monitor.check_resource_availability(
            estimated_memory_gb, estimated_gpu_memory_gb
        )

        if not available:
            raise MemoryError(f"Insufficient resources for inference: {message}")

    def _model_inference(self, audio_features: torch.Tensor, timing_grid: Dict,
                        difficulty_level: int) -> Dict[str, torch.Tensor]:
        """Perform optimized model inference with chunking support"""
        # Prepare inputs
        audio_features = audio_features.unsqueeze(0).to(self.device)  # Add batch dimension
        difficulty_tensor = torch.tensor([difficulty_level - 8]).to(self.device)  # 0, 1, 2 for levels 8, 9, 10

        # Create attention mask for variable length sequences
        seq_len = audio_features.shape[1]
        attention_mask = torch.ones(1, seq_len, dtype=torch.bool).to(self.device)

        # Use mixed precision if enabled
        if self.inference_config.use_mixed_precision and self.device.type == 'cuda':
            with torch.cuda.amp.autocast():
                if self.inference_config.use_chunked_inference and seq_len > self.inference_config.max_chunk_size:
                    outputs = self._chunked_inference(audio_features, difficulty_tensor, attention_mask)
                else:
                    outputs = self.model(audio_features, difficulty_tensor, attention_mask=attention_mask)
        else:
            if self.inference_config.use_chunked_inference and seq_len > self.inference_config.max_chunk_size:
                outputs = self._chunked_inference(audio_features, difficulty_tensor, attention_mask)
            else:
                outputs = self.model(audio_features, difficulty_tensor, attention_mask=attention_mask)

        # Move outputs to CPU for post-processing
        cpu_outputs = {}
        for key, value in outputs.items():
            if torch.is_tensor(value):
                cpu_outputs[key] = value.squeeze(0).cpu()  # Remove batch dimension
            else:
                cpu_outputs[key] = value

        return cpu_outputs

    def _chunked_inference(self, audio_features: torch.Tensor, difficulty_tensor: torch.Tensor,
                          attention_mask: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Perform inference on long sequences using optimized chunking"""
        seq_len = audio_features.shape[1]
        chunk_size = self.inference_config.max_chunk_size
        overlap = self.inference_config.chunk_overlap

        chunked_outputs = []

        for start_idx in range(0, seq_len, chunk_size - overlap):
            end_idx = min(start_idx + chunk_size, seq_len)

            # Extract chunk
            chunk_audio = audio_features[:, start_idx:end_idx]
            chunk_mask = attention_mask[:, start_idx:end_idx]

            # Inference on chunk
            chunk_outputs = self.model(chunk_audio, difficulty_tensor, attention_mask=chunk_mask)

            # Handle overlap blending for smooth transitions
            if start_idx > 0 and overlap > 0:
                self._blend_chunk_overlap(chunked_outputs[-1], chunk_outputs, overlap)

            chunked_outputs.append(chunk_outputs)

        # Concatenate chunks with proper overlap handling
        return self._concatenate_chunks(chunked_outputs, overlap)

    def _blend_chunk_overlap(self, prev_chunk: Dict, curr_chunk: Dict, overlap: int):
        """Blend overlapping regions between chunks for smooth transitions"""
        overlap_start = overlap // 2

        for key, value in curr_chunk.items():
            if torch.is_tensor(value) and len(value.shape) > 1:
                # Apply fade-in to current chunk
                fade_in = torch.linspace(0, 1, overlap_start).unsqueeze(-1).to(value.device)
                value[:, :overlap_start] *= fade_in

                # Apply fade-out to previous chunk
                if key in prev_chunk:
                    prev_value = prev_chunk[key]
                    fade_out = torch.linspace(1, 0, overlap_start).unsqueeze(-1).to(prev_value.device)
                    prev_value[:, -overlap_start:] *= fade_out

                    # Blend overlapping regions
                    value[:, :overlap_start] += prev_value[:, -overlap_start:]

    def _concatenate_chunks(self, chunked_outputs: List[Dict], overlap: int) -> Dict[str, torch.Tensor]:
        """Concatenate chunked outputs with proper overlap handling"""
        if not chunked_outputs:
            return {}

        final_outputs = {}

        for key in chunked_outputs[0].keys():
            if torch.is_tensor(chunked_outputs[0][key]):
                concatenated_values = []

                for i, chunk_output in enumerate(chunked_outputs):
                    value = chunk_output[key]

                    # Remove overlap from intermediate chunks
                    if i > 0:
                        value = value[:, overlap//2:]
                    if i < len(chunked_outputs) - 1:
                        value = value[:, :-overlap//2]

                    concatenated_values.append(value)

                final_outputs[key] = torch.cat(concatenated_values, dim=1)
            else:
                final_outputs[key] = chunked_outputs[0][key]

        return final_outputs

    def _handle_memory_error(self, audio_path: str, bpm: float, offset: float,
                           difficulty_level: int, course_type: str, start_time: float) -> Dict[str, Any]:
        """Handle out-of-memory errors with graceful degradation"""
        self.logger.warning("Memory error detected, attempting recovery with reduced settings")

        # Emergency cleanup
        self.resource_manager.cleanup_memory()

        # Try with reduced settings
        for attempt in range(self.config.resource.oom_retry_attempts):
            try:
                self.logger.info(f"Memory recovery attempt {attempt + 1}")

                # Apply increasingly aggressive memory reductions
                if attempt == 0:
                    # Reduce chunk size
                    original_chunk_size = self.inference_config.max_chunk_size
                    self.inference_config.max_chunk_size = max(500, original_chunk_size // 2)

                elif attempt == 1:
                    # Force CPU inference
                    if self.device.type == 'cuda':
                        self.model = self.model.cpu()
                        self.device = torch.device('cpu')
                        self.inference_config.max_chunk_size = max(250, self.inference_config.max_chunk_size // 2)

                else:
                    # Minimal settings
                    self.inference_config.max_chunk_size = 100
                    self.inference_config.use_mixed_precision = False

                # Wait before retry
                time.sleep(self.config.resource.oom_retry_delay_seconds)

                # Recursive call with reduced settings
                return self.generate_chart(audio_path, bpm, offset, difficulty_level, course_type)

            except Exception as e:
                self.logger.warning(f"Recovery attempt {attempt + 1} failed: {e}")
                continue

        # All recovery attempts failed
        return {
            "success": False,
            "error": "Out of memory - all recovery attempts failed",
            "error_type": "MemoryError",
            "inference_time": time.time() - start_time,
            "retry_attempts": self.config.resource.oom_retry_attempts,
            "recommendations": [
                "Use a shorter audio file (< 3 minutes)",
                "Increase available system memory",
                "Close other memory-intensive applications",
                "Use CPU-only inference mode"
            ]
        }

    def _update_performance_stats(self, metrics: InferenceMetrics):
        """Update running performance statistics"""
        n = self.inference_stats["successful_inferences"]

        # Running averages
        self.inference_stats["average_inference_time"] = (
            (self.inference_stats["average_inference_time"] * (n - 1) + metrics.total_time) / n
        )

        self.inference_stats["average_realtime_factor"] = (
            (self.inference_stats["average_realtime_factor"] * (n - 1) + metrics.realtime_factor) / n
        )

        # Peak values
        current_memory = metrics.memory_usage.get("system_memory_gb", 0)
        self.inference_stats["peak_memory_usage"] = max(
            self.inference_stats["peak_memory_usage"], current_memory
        )

        self.inference_stats["total_processing_time"] += metrics.total_time

    def _generate_error_recommendations(self, error: Exception) -> List[str]:
        """Generate actionable recommendations based on error type"""
        recommendations = []

        error_str = str(error).lower()

        if "memory" in error_str or "cuda out of memory" in error_str:
            recommendations.extend([
                "Reduce audio file length (< 3 minutes recommended)",
                "Close other GPU-intensive applications",
                "Enable CPU fallback mode",
                "Reduce chunk size in configuration"
            ])

        elif "file not found" in error_str:
            recommendations.extend([
                "Check audio file path is correct",
                "Ensure audio file exists and is readable",
                "Verify file format is supported (.ogg, .wav, .mp3)"
            ])

        elif "invalid" in error_str or "value" in error_str:
            recommendations.extend([
                "Check input parameters are within valid ranges",
                "BPM should be between 60-300",
                "Difficulty should be 8, 9, or 10",
                "Offset should be between -10.0 and 10.0 seconds"
            ])

        else:
            recommendations.extend([
                "Check system resources and close unnecessary applications",
                "Verify model file is not corrupted",
                "Try with a different audio file",
                "Check log files for detailed error information"
            ])

        return recommendations

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            "inference_statistics": self.inference_stats.copy(),
            "resource_summary": self.resource_manager.get_resource_summary(),
            "hardware_info": {
                "device": str(self.device),
                "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "N/A",
                "gpu_memory_gb": torch.cuda.get_device_properties(0).total_memory / (1024**3) if torch.cuda.is_available() else 0,
                "system_memory_gb": psutil.virtual_memory().total / (1024**3),
                "cpu_cores": psutil.cpu_count()
            },
            "optimization_status": {
                "model_optimized": self.model_optimized,
                "tensorrt_enabled": self.tensorrt_enabled,
                "mixed_precision": self.inference_config.use_mixed_precision,
                "chunked_inference": self.inference_config.use_chunked_inference
            }
        }

    def cleanup(self):
        """Cleanup resources and prepare for shutdown"""
        self.logger.info("Cleaning up inference system...")

        # Cleanup model
        if self.model is not None:
            del self.model
            self.model = None

        # Cleanup GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        # Cleanup resource manager
        self.resource_manager.cleanup_memory()

        # Force garbage collection
        gc.collect()

        self.logger.info("Inference system cleanup completed")

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.cleanup()

        if exc_type is not None:
            self.logger.error(f"Exception during inference: {exc_type.__name__}: {exc_val}")

        return False  # Don't suppress exceptions
