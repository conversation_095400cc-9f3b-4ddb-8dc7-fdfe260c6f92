"""
Shared Utilities and Components

This module contains shared utilities, configurations, and components that are 
used across multiple phases of the TJA generation system. It provides common 
functionality for file I/O, monitoring, validation, and system management.

Key Components:
- Configuration management
- File I/O operations
- System monitoring and resource management
- Data validation schemas
- Hardware optimization utilities
- Path management

These components are designed to be phase-agnostic and provide consistent 
interfaces across the entire system.
"""

# Import all shared modules
from .utils import *
from .config import *
from .file_io import *
from .monitoring import *
from .data_validation import *
from .schemas import *

__all__ = [
    # Utils
    'BaseClasses',
    'EncodingDetector',
    'HardwareMonitor',
    'MemoryMonitor',
    'ResourceManager',
    
    # Config
    'UnifiedConfigManager',
    
    # File I/O
    'UnifiedIOManager',
    
    # Monitoring
    'SystemMonitor',
    
    # Data Validation
    'AlignmentValidator',
    'FeatureValidator',
    'SystemValidator',
    
    # Schemas
    'OutputSchemas',
    'ValidationSchemas'
]
