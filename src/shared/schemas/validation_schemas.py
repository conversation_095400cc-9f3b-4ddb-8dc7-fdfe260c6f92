"""
Schema Validation and Test Output Generation

Comprehensive validation framework for standardized output schemas
with minimal test output generation for system integrity verification.
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from .output_schemas import (
    StandardizedOutput, PhaseResult, ProcessingMetrics, 
    ValidationMetrics, HardwareMetrics, create_minimal_output_sample
)


class SchemaValidator:
    """Validates output schemas against standardized formats"""
    
    def __init__(self):
        self.required_fields = {
            "phase_metadata": ["phase_number", "phase_name", "success", "timestamp", "version"],
            "metrics": ["processing", "validation", "hardware"],
            "outputs": ["files", "directory"]
        }
        
        self.metric_fields = {
            "processing": ["processing_time_seconds", "memory_usage_mb"],
            "validation": ["is_valid", "quality_score"],
            "hardware": ["target_hardware", "optimization_enabled"]
        }
    
    def validate_structure(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate basic structure of output data"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "missing_fields": []
        }
        
        # Check top-level structure
        for section, fields in self.required_fields.items():
            if section not in output_data:
                validation_result["errors"].append(f"Missing required section: {section}")
                validation_result["valid"] = False
                continue
            
            # Check required fields in each section
            for field in fields:
                if field not in output_data[section]:
                    validation_result["missing_fields"].append(f"{section}.{field}")
                    validation_result["warnings"].append(f"Missing field: {section}.{field}")
        
        # Validate metrics structure
        if "metrics" in output_data:
            for metric_type, fields in self.metric_fields.items():
                if metric_type in output_data["metrics"]:
                    for field in fields:
                        if field not in output_data["metrics"][metric_type]:
                            validation_result["warnings"].append(f"Missing metric: {metric_type}.{field}")
        
        return validation_result
    
    def validate_data_types(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data types of output fields"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Type validations
        type_checks = [
            ("phase_metadata.phase_number", int),
            ("phase_metadata.success", bool),
            ("phase_metadata.version", str),
            ("outputs.files", list)
        ]
        
        for field_path, expected_type in type_checks:
            try:
                keys = field_path.split('.')
                value = output_data
                for key in keys:
                    value = value[key]
                
                if not isinstance(value, expected_type):
                    validation_result["errors"].append(
                        f"Field {field_path} should be {expected_type.__name__}, got {type(value).__name__}"
                    )
                    validation_result["valid"] = False
                    
            except KeyError:
                # Field missing - already caught by structure validation
                pass
            except Exception as e:
                validation_result["errors"].append(f"Error validating {field_path}: {e}")
                validation_result["valid"] = False
        
        return validation_result
    
    def validate_ranges(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate value ranges for numeric fields"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Range validations
        try:
            # Quality scores should be 0.0 to 1.0
            if "metrics" in output_data and "validation" in output_data["metrics"]:
                validation_metrics = output_data["metrics"]["validation"]
                
                if "quality_score" in validation_metrics:
                    score = validation_metrics["quality_score"]
                    if not (0.0 <= score <= 1.0):
                        validation_result["warnings"].append(
                            f"Quality score {score} outside expected range [0.0, 1.0]"
                        )
                
                if "confidence_score" in validation_metrics:
                    score = validation_metrics["confidence_score"]
                    if not (0.0 <= score <= 1.0):
                        validation_result["warnings"].append(
                            f"Confidence score {score} outside expected range [0.0, 1.0]"
                        )
            
            # Processing time should be positive
            if "metrics" in output_data and "processing" in output_data["metrics"]:
                processing_metrics = output_data["metrics"]["processing"]
                
                if "processing_time_seconds" in processing_metrics:
                    time_val = processing_metrics["processing_time_seconds"]
                    if time_val < 0:
                        validation_result["errors"].append(
                            f"Processing time cannot be negative: {time_val}"
                        )
                        validation_result["valid"] = False
                        
        except Exception as e:
            validation_result["errors"].append(f"Error validating ranges: {e}")
            validation_result["valid"] = False
        
        return validation_result


class OutputValidator:
    """High-level output validation orchestrator"""
    
    def __init__(self):
        self.schema_validator = SchemaValidator()
    
    def validate_output(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive output validation"""
        start_time = time.time()
        
        # Run all validation checks
        structure_result = self.schema_validator.validate_structure(output_data)
        types_result = self.schema_validator.validate_data_types(output_data)
        ranges_result = self.schema_validator.validate_ranges(output_data)
        
        # Combine results
        combined_result = {
            "overall_valid": (
                structure_result["valid"] and 
                types_result["valid"] and 
                ranges_result["valid"]
            ),
            "validation_time_seconds": time.time() - start_time,
            "structure_validation": structure_result,
            "types_validation": types_result,
            "ranges_validation": ranges_result,
            "summary": {
                "total_errors": (
                    len(structure_result["errors"]) + 
                    len(types_result["errors"]) + 
                    len(ranges_result["errors"])
                ),
                "total_warnings": (
                    len(structure_result["warnings"]) + 
                    len(types_result["warnings"]) + 
                    len(ranges_result["warnings"])
                )
            }
        }
        
        return combined_result
    
    def validate_phase_output_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Validate output file against schema"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                output_data = json.load(f)
            
            validation_result = self.validate_output(output_data)
            validation_result["file_path"] = str(file_path)
            validation_result["file_valid"] = True
            
            return validation_result
            
        except FileNotFoundError:
            return {
                "overall_valid": False,
                "file_path": str(file_path),
                "file_valid": False,
                "error": f"File not found: {file_path}"
            }
        except json.JSONDecodeError as e:
            return {
                "overall_valid": False,
                "file_path": str(file_path),
                "file_valid": False,
                "error": f"Invalid JSON: {e}"
            }
        except Exception as e:
            return {
                "overall_valid": False,
                "file_path": str(file_path),
                "file_valid": False,
                "error": f"Validation error: {e}"
            }


def validate_phase_output(output_data: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function for output validation"""
    validator = OutputValidator()
    return validator.validate_output(output_data)


def create_minimal_test_output(phase_number: int, output_dir: str = "test_outputs") -> str:
    """Create minimal test output for validation purposes"""
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Generate minimal test data
    test_data = create_minimal_output_sample(phase_number)
    
    # Save to file
    output_file = output_path / f"phase{phase_number}_minimal_test.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    return str(output_file)


def generate_all_test_outputs(output_dir: str = "test_outputs") -> List[str]:
    """Generate minimal test outputs for all phases"""
    output_files = []
    
    for phase in range(1, 7):
        output_file = create_minimal_test_output(phase, output_dir)
        output_files.append(output_file)
    
    return output_files


def validate_all_test_outputs(output_dir: str = "test_outputs") -> Dict[str, Any]:
    """Validate all generated test outputs"""
    validator = OutputValidator()
    results = {
        "overall_valid": True,
        "phase_results": {},
        "summary": {
            "total_phases": 6,
            "valid_phases": 0,
            "total_errors": 0,
            "total_warnings": 0
        }
    }
    
    for phase in range(1, 7):
        test_file = Path(output_dir) / f"phase{phase}_minimal_test.json"
        
        if test_file.exists():
            phase_result = validator.validate_phase_output_file(test_file)
            results["phase_results"][f"phase_{phase}"] = phase_result
            
            if phase_result["overall_valid"]:
                results["summary"]["valid_phases"] += 1
            else:
                results["overall_valid"] = False
            
            results["summary"]["total_errors"] += phase_result.get("summary", {}).get("total_errors", 0)
            results["summary"]["total_warnings"] += phase_result.get("summary", {}).get("total_warnings", 0)
        else:
            results["phase_results"][f"phase_{phase}"] = {
                "overall_valid": False,
                "error": f"Test file not found: {test_file}"
            }
            results["overall_valid"] = False
    
    return results
