"""
TJA Generator Utilities

Hardware monitoring, encoding detection, resource management utilities,
and standardized base classes implementing SOLID principles.
"""

from .encoding_detector import detect_file_encoding, validate_encoding_detection
from .hardware_monitor import get_system_info, setup_hardware_optimized_processing, ResourceMonitor
from .memory_monitor import MemoryMonitor
from .resource_manager import ResourceManager
from .base_classes import (
    BaseProcessor, BaseValidator, BaseConfigManager,
    ProcessingResult, ValidationResult
)

__all__ = [
    'detect_file_encoding',
    'validate_encoding_detection',
    'get_system_info',
    'setup_hardware_optimized_processing',
    'ResourceMonitor',
    'MemoryMonitor',
    'ResourceManager',
    'BaseProcessor',
    'BaseValidator',
    'BaseConfigManager',
    'ProcessingResult',
    'ValidationResult'
]
