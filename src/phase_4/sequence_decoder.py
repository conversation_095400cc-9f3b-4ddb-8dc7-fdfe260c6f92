"""
Sequence Decoder

Transformer-based decoder for generating TJA note sequences from encoded audio features.
Includes difficulty-aware generation and pattern-guided decoding.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List

from .attention_modules import MultiHeadAttention, TemporalAttention
from .pattern_library import PatternLibrary


class TJANoteEmbedding(nn.Module):
    """Embedding layer for TJA notes with timing and difficulty information"""
    
    def __init__(self, vocab_size: int = 8, d_model: int = 256, max_seq_len: int = 2000):
        super().__init__()
        
        self.d_model = d_model
        self.vocab_size = vocab_size
        
        # Ensure embedding dimensions are at least 1
        note_embed_dim = max(1, d_model // 2)
        timing_embed_dim = max(1, d_model // 4)
        diff_embed_dim = max(1, d_model // 4)

        # Note type embedding (0=blank, 1=don, 2=ka, 3=don_big, 4=ka_big, 5=drumroll, 6=end_roll, 7=special)
        self.note_embedding = nn.Embedding(vocab_size, note_embed_dim)

        # Timing embedding (relative timing within measure)
        self.timing_embedding = nn.Embedding(64, timing_embed_dim)  # 64 subdivisions per measure

        # Difficulty embedding
        self.difficulty_embedding = nn.Embedding(3, diff_embed_dim)  # Oni levels 8-10
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(max_seq_len, d_model))
        
        # Projection layer (note_emb + timing_emb + diff_emb)
        combined_dim = note_embed_dim + timing_embed_dim + diff_embed_dim
        self.projection = nn.Linear(combined_dim, d_model)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, note_tokens: torch.Tensor, timing_tokens: torch.Tensor,
                difficulty: torch.Tensor, positions: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Embed TJA note sequence
        
        Args:
            note_tokens: [batch, seq_len] note type tokens
            timing_tokens: [batch, seq_len] timing tokens
            difficulty: [batch] difficulty level
            positions: [batch, seq_len] position indices
            
        Returns:
            embeddings: [batch, seq_len, d_model] note embeddings
        """
        batch_size, seq_len = note_tokens.shape
        
        # Get embeddings
        note_emb = self.note_embedding(note_tokens.clamp(0, self.vocab_size - 1))
        timing_emb = self.timing_embedding(timing_tokens.clamp(0, 63))
        
        # Expand difficulty embedding
        diff_emb = self.difficulty_embedding(difficulty.clamp(0, 2))
        diff_emb = diff_emb.unsqueeze(1).expand(-1, seq_len, -1)
        
        # Combine embeddings
        combined = torch.cat([note_emb, timing_emb, diff_emb], dim=-1)

        # Project to model dimension
        projected = self.projection(combined)

        # Add positional encoding
        if positions is None:
            positions = torch.arange(seq_len, device=note_tokens.device)
            positions = positions.unsqueeze(0).expand(batch_size, -1)

        pos_emb = self.pos_encoding[positions.clamp(0, self.pos_encoding.shape[0] - 1)]

        # Final embedding
        embeddings = projected + pos_emb
        embeddings = self.dropout(embeddings)
        
        return embeddings


class DecoderLayer(nn.Module):
    """Single transformer decoder layer with cross-attention to audio features"""
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        
        self.d_model = d_model
        
        # Self-attention (masked for autoregressive generation)
        self.self_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.self_attn_norm = nn.LayerNorm(d_model)
        
        # Cross-attention to audio features
        self.cross_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.cross_attn_norm = nn.LayerNorm(d_model)
        
        # Feed-forward network (memory optimized)
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
        self.ff_norm = nn.LayerNorm(d_model)

        # Enable gradient checkpointing
        self.use_gradient_checkpointing = True
        
    def forward(self, x: torch.Tensor, audio_features: torch.Tensor,
                self_attn_mask: Optional[torch.Tensor] = None,
                cross_attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass of decoder layer with gradient checkpointing

        Args:
            x: [batch, seq_len, d_model] input sequence
            audio_features: [batch, audio_len, d_model] encoded audio features
            self_attn_mask: [batch, seq_len, seq_len] self-attention mask
            cross_attn_mask: [batch, seq_len, audio_len] cross-attention mask

        Returns:
            output: [batch, seq_len, d_model] decoded features
        """
        if self.use_gradient_checkpointing and self.training:
            from torch.utils.checkpoint import checkpoint
            return checkpoint(
                self._forward_impl,
                x, audio_features, self_attn_mask, cross_attn_mask,
                use_reentrant=False
            )
        else:
            return self._forward_impl(x, audio_features, self_attn_mask, cross_attn_mask)

    def _forward_impl(self, x: torch.Tensor, audio_features: torch.Tensor,
                     self_attn_mask: Optional[torch.Tensor] = None,
                     cross_attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Implementation of forward pass"""
        # Self-attention with residual connection
        self_attn_out, _ = self.self_attention(x, x, x, self_attn_mask)
        x = self.self_attn_norm(x + self_attn_out)

        # Cross-attention with residual connection
        cross_attn_out, _ = self.cross_attention(x, audio_features, audio_features, cross_attn_mask)
        x = self.cross_attn_norm(x + cross_attn_out)

        # Feed-forward with residual connection
        ff_out = self.feed_forward(x)
        x = self.ff_norm(x + ff_out)

        return x


class SequenceDecoder(nn.Module):
    """
    Transformer-based sequence decoder for TJA generation
    
    Generates TJA note sequences autoregressively from encoded audio features
    with difficulty-aware and pattern-guided generation.
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        self.d_model = config.get("hidden_dims", 256)                # 256
        self.num_layers = config.get("num_decoder_layers", 6)        # 6
        self.num_heads = config.get("num_attention_heads", 8)        # 8
        self.vocab_size = config.get("note_types", 8)               # 8
        self.max_seq_len = config.get("max_sequence_length", 2000)  # 2000
        self.dropout = config.get("dropout", 0.1)                   # 0.1

        # Enable gradient checkpointing for memory efficiency
        self.use_gradient_checkpointing = True

        # Note embedding
        self.note_embedding = TJANoteEmbedding(
            vocab_size=self.vocab_size,
            d_model=self.d_model,
            max_seq_len=self.max_seq_len
        )
        
        # Decoder layers (memory optimized)
        self.decoder_layers = nn.ModuleList([
            DecoderLayer(
                d_model=self.d_model,
                num_heads=self.num_heads,
                d_ff=self.d_model * 2,  # Reduced from 4x to 2x for memory
                dropout=self.dropout
            ) for _ in range(self.num_layers)
        ])
        
        # Temporal attention for musical structure
        self.temporal_attention = TemporalAttention(
            d_model=self.d_model,
            num_heads=self.num_heads,
            dropout=self.dropout,
            max_sequence_length=self.max_seq_len
        )
        
        # Pattern library for pattern-guided generation (disabled for memory optimization)
        # self.pattern_library = PatternLibrary(config)
        self.pattern_library = None
        
        # Output projection
        self.output_norm = nn.LayerNorm(self.d_model)
        self.output_projection = nn.Linear(self.d_model, self.vocab_size)
        
        # Difficulty conditioning
        self.difficulty_projection = nn.Linear(3, self.d_model)
        
        # Special tokens
        self.start_token = 0  # Start of sequence token
        self.pad_token = 0    # Padding token
        
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.02)
    
    def _create_causal_mask(self, seq_len: int, device: torch.device) -> torch.Tensor:
        """Create causal mask for autoregressive generation"""
        mask = torch.triu(torch.ones(seq_len, seq_len, device=device), diagonal=1)
        return mask == 0  # True for allowed positions
    
    def forward(self, audio_features: torch.Tensor, 
                target_sequence: Optional[torch.Tensor] = None,
                target_timing: Optional[torch.Tensor] = None,
                difficulty: Optional[torch.Tensor] = None,
                audio_mask: Optional[torch.Tensor] = None,
                pattern_guidance: bool = True) -> Dict[str, torch.Tensor]:
        """
        Forward pass for training (teacher forcing) or inference
        
        Args:
            audio_features: [batch, audio_len, d_model] encoded audio features
            target_sequence: [batch, seq_len] target note sequence (for training)
            target_timing: [batch, seq_len] target timing sequence (for training)
            difficulty: [batch] difficulty level (0-2 for Oni 8-10)
            audio_mask: [batch, audio_len] audio attention mask
            pattern_guidance: Whether to use pattern-guided generation
            
        Returns:
            Dictionary containing logits, attention weights, and other outputs
        """
        batch_size, audio_len, _ = audio_features.shape
        
        # Set default difficulty if not provided
        if difficulty is None:
            difficulty = torch.ones(batch_size, dtype=torch.long, device=audio_features.device)
        
        # Training mode (teacher forcing)
        if target_sequence is not None:
            return self._forward_training(
                audio_features, target_sequence, target_timing, 
                difficulty, audio_mask, pattern_guidance
            )
        
        # Inference mode (autoregressive generation)
        else:
            return self._forward_inference(
                audio_features, difficulty, audio_mask, pattern_guidance
            )
    
    def _forward_training(self, audio_features: torch.Tensor,
                         target_sequence: torch.Tensor,
                         target_timing: torch.Tensor,
                         difficulty: torch.Tensor,
                         audio_mask: Optional[torch.Tensor],
                         pattern_guidance: bool) -> Dict[str, torch.Tensor]:
        """Forward pass for training with teacher forcing"""
        batch_size, seq_len = target_sequence.shape
        
        # Embed target sequence (shifted right for teacher forcing)
        input_sequence = torch.cat([
            torch.full((batch_size, 1), self.start_token, 
                      dtype=target_sequence.dtype, device=target_sequence.device),
            target_sequence[:, :-1]
        ], dim=1)
        
        input_timing = torch.cat([
            torch.zeros((batch_size, 1), dtype=target_timing.dtype, device=target_timing.device),
            target_timing[:, :-1]
        ], dim=1)
        
        # Embed input sequence
        embedded_sequence = self.note_embedding(
            input_sequence, input_timing, difficulty
        )
        
        # Add difficulty conditioning
        difficulty_one_hot = F.one_hot(difficulty, num_classes=3).float()
        difficulty_emb = self.difficulty_projection(difficulty_one_hot)
        difficulty_emb = difficulty_emb.unsqueeze(1).expand(-1, seq_len, -1)
        embedded_sequence = embedded_sequence + difficulty_emb
        
        # Create causal mask for self-attention
        causal_mask = self._create_causal_mask(seq_len, embedded_sequence.device)
        causal_mask = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Create cross-attention mask
        if audio_mask is not None:
            audio_seq_len = audio_features.shape[1]
            cross_mask = torch.ones(batch_size, seq_len, audio_seq_len, device=embedded_sequence.device)
            cross_mask = cross_mask * audio_mask.unsqueeze(1)
        else:
            cross_mask = None
        
        # Apply decoder layers with memory optimization
        hidden_states = embedded_sequence
        attention_weights = []

        # Use gradient checkpointing for decoder layers if enabled
        if hasattr(self, 'use_gradient_checkpointing') and self.use_gradient_checkpointing and self.training:
            from torch.utils.checkpoint import checkpoint
            for layer in self.decoder_layers:
                hidden_states = checkpoint(
                    layer,
                    hidden_states, audio_features, causal_mask, cross_mask,
                    use_reentrant=False
                )
        else:
            for layer in self.decoder_layers:
                hidden_states = layer(
                    hidden_states, audio_features, causal_mask, cross_mask
                )
        
        # Apply temporal attention for musical structure
        temporal_output = self.temporal_attention(hidden_states)
        hidden_states = temporal_output["attended_features"]
        
        # Pattern-guided generation (disabled for memory optimization)
        if pattern_guidance and self.pattern_library is not None:
            # Extract patterns from current sequence
            pattern_embeddings = self.pattern_library.encode_sequence_patterns(
                input_sequence, input_timing, difficulty
            )

            # Apply pattern guidance
            if pattern_embeddings.shape[1] > 0:
                hidden_states = self.pattern_library.pattern_guided_attention(
                    hidden_states, pattern_embeddings
                )
        
        # Output projection
        hidden_states = self.output_norm(hidden_states)
        logits = self.output_projection(hidden_states)
        
        return {
            "logits": logits,
            "hidden_states": hidden_states,
            "attention_weights": attention_weights,
            "temporal_attention": temporal_output,
            "embedded_sequence": embedded_sequence
        }
    
    def _forward_inference(self, audio_features: torch.Tensor,
                          difficulty: torch.Tensor,
                          audio_mask: Optional[torch.Tensor],
                          pattern_guidance: bool,
                          max_length: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Forward pass for autoregressive inference"""
        batch_size, audio_len, _ = audio_features.shape
        device = audio_features.device
        
        if max_length is None:
            # TJA sequences should be much shorter than audio length
            # Use a reasonable default based on audio length
            max_length = min(audio_len // 4, self.max_seq_len // 2, 100)
        
        # Initialize with start token
        generated_sequence = torch.full(
            (batch_size, 1), self.start_token, dtype=torch.long, device=device
        )
        generated_timing = torch.zeros(
            (batch_size, 1), dtype=torch.long, device=device
        )
        
        # Store outputs
        all_logits = []
        all_hidden_states = []
        
        for step in range(max_length):
            # Embed current sequence
            embedded_sequence = self.note_embedding(
                generated_sequence, generated_timing, difficulty
            )
            
            # Add difficulty conditioning
            seq_len = embedded_sequence.shape[1]
            difficulty_one_hot = F.one_hot(difficulty, num_classes=3).float()
            difficulty_emb = self.difficulty_projection(difficulty_one_hot)
            difficulty_emb = difficulty_emb.unsqueeze(1).expand(-1, seq_len, -1)
            embedded_sequence = embedded_sequence + difficulty_emb
            
            # Create causal mask
            causal_mask = self._create_causal_mask(seq_len, device)
            causal_mask = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)
            
            # Create cross-attention mask
            if audio_mask is not None:
                cross_mask = torch.ones(batch_size, seq_len, audio_len, device=device)
                cross_mask = cross_mask * audio_mask.unsqueeze(1)
            else:
                cross_mask = None
            
            # Apply decoder layers
            hidden_states = embedded_sequence
            
            for layer in self.decoder_layers:
                hidden_states = layer(
                    hidden_states, audio_features, causal_mask, cross_mask
                )
            
            # Apply temporal attention
            temporal_output = self.temporal_attention(hidden_states)
            hidden_states = temporal_output["attended_features"]
            
            # Pattern guidance (disabled for memory optimization)
            if pattern_guidance and step > 0 and self.pattern_library is not None:
                pattern_embeddings = self.pattern_library.encode_sequence_patterns(
                    generated_sequence, generated_timing, difficulty
                )

                if pattern_embeddings.shape[1] > 0:
                    hidden_states = self.pattern_library.pattern_guided_attention(
                        hidden_states, pattern_embeddings
                    )
            
            # Output projection
            hidden_states = self.output_norm(hidden_states)
            logits = self.output_projection(hidden_states)
            
            # Get next token (only the last position)
            next_logits = logits[:, -1, :]  # [batch, vocab_size]
            
            # Sample next token
            next_token = torch.multinomial(F.softmax(next_logits, dim=-1), 1)
            
            # Generate timing (simplified - could be more sophisticated)
            next_timing = torch.randint(0, 16, (batch_size, 1), device=device)
            
            # Append to sequence
            generated_sequence = torch.cat([generated_sequence, next_token], dim=1)
            generated_timing = torch.cat([generated_timing, next_timing], dim=1)
            
            # Store outputs
            all_logits.append(next_logits)
            all_hidden_states.append(hidden_states[:, -1, :])
        
        return {
            "generated_sequence": generated_sequence[:, 1:],  # Remove start token
            "generated_timing": generated_timing[:, 1:],
            "logits": torch.stack(all_logits, dim=1),
            "hidden_states": torch.stack(all_hidden_states, dim=1)
        }
    
    def beam_search_decode(self, audio_features: torch.Tensor,
                          difficulty: torch.Tensor,
                          beam_size: int = 5,
                          max_length: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Beam search decoding for better generation quality"""
        # Simplified beam search implementation
        # In practice, this would be more sophisticated
        return self._forward_inference(audio_features, difficulty, None, True, max_length)
