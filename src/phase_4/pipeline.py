from typing import (
    Any,
    Dict,
    List,
    Optional
)
"""
TJA Generation Pipeline

End-to-end pipeline that integrates Phase 1 (parsing), Phase 2 (audio analysis),
and Phase 3 (deep learning) into a complete TJA generation system.
"""

import os
import time
import logging
import traceback
from pathlib import Path
import torch
import numpy as np

from .config import PHASE_4_CONFIG
from .quality_assessment import QualityAssessment, TJAQualityMetrics
from src.shared.utils.memory_monitor import MemoryMonitor, MemoryContext
from .tja_generator import TJAGeneratorModel
from src.phase_2.unified_audio_processor import UnifiedAudioProcessor as TjaAudioFeatureAnalyzer


class TJAGenerationError(Exception):
    """Custom exception for TJA generation errors"""
    pass


class TJAGenerationPipeline:
    """
    Complete end-to-end TJA generation pipeline
    
    Integrates all phases:
    1. Audio file validation and preprocessing
    2. Audio feature extraction (Phase 2)
    3. Deep learning inference (Phase 3)
    4. TJA file generation and validation
    5. Quality assessment and optimization
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the TJA generation pipeline
        
        Args:
            config: Optional configuration override
        """
        self.config = config or PHASE_4_CONFIG
        self.logger = self._setup_logging()
        
        # Initialize components
        self.memory_monitor = MemoryMonitor()
        self.quality_assessor = QualityAssessment(self.config["quality_assessment"])
        
        # Initialize models and processors
        self.model = None
        self.audio_analyzer = None
        
        # Pipeline state
        self.is_initialized = False
        self.processing_stats = {
            "total_processed": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0
        }
        
        self.logger.info("TJA Generation Pipeline initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the pipeline"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config["logging"]["level"]))
        
        if not logger.handlers:
            # Console handler
            if self.config["logging"]["console_logging"]:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                formatter = logging.Formatter(self.config["logging"]["format"])
                console_handler.setFormatter(formatter)
                logger.addHandler(console_handler)
            
            # File handler
            if self.config["logging"]["file_logging"]:
                log_dir = Path(self.config["directories"]["logs_dir"])
                log_dir.mkdir(exist_ok=True)
                
                log_file = log_dir / "phase4_pipeline.log"
                file_handler = logging.FileHandler(log_file)
                file_handler.setLevel(logging.DEBUG)
                formatter = logging.Formatter(self.config["logging"]["format"])
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
        
        return logger
    
    def initialize(self) -> bool:
        """
        Initialize all pipeline components
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing TJA Generation Pipeline...")
            
            with MemoryContext(self.memory_monitor, "pipeline_initialization"):
                # Initialize Phase 3 model
                self._initialize_model()
                
                # Initialize Phase 2 audio analyzer
                self._initialize_audio_analyzer()
                
                # Validate system requirements
                self._validate_system_requirements()
            
            self.is_initialized = True
            self.logger.info("Pipeline initialization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Pipeline initialization failed: {e}")
            self.logger.debug(traceback.format_exc())
            return False
    
    def _initialize_model(self):
        """Initialize the Phase 3 deep learning model"""
        self.logger.info("Loading Phase 3 model...")
        
        model_config = self.config["phase3_integration"]["model_config"]
        checkpoint_path = self.config["phase3_integration"]["model_checkpoint_path"]
        fallback_path = self.config["phase3_integration"]["fallback_model_path"]
        
        # Create model
        self.model = TJAGeneratorModel(model_config)
        
        # Load checkpoint
        model_path = Path(checkpoint_path)
        if not model_path.exists():
            model_path = Path(fallback_path)
        
        if model_path.exists():
            self.logger.info(f"Loading model from {model_path}")
            checkpoint = torch.load(model_path, map_location='cpu')
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.logger.warning("No trained model found, using randomly initialized weights")
        
        # Move to GPU if available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = self.model.to(device)
        self.model.eval()
        
        self.logger.info(f"Model loaded successfully on {device}")
    
    def _initialize_audio_analyzer(self):
        """Initialize the Phase 2 TJA audio feature analyzer"""
        self.logger.info("Initializing TJA audio feature analyzer...")

        analyzer_config = self.config["phase2_integration"]
        self.audio_analyzer = TjaAudioFeatureAnalyzer(analyzer_config)

        self.logger.info("TJA audio feature analyzer initialized successfully")
    
    def _validate_system_requirements(self):
        """Validate system meets requirements for TJA generation"""
        self.logger.info("Validating system requirements...")
        
        # Check GPU availability
        if not torch.cuda.is_available():
            raise TJAGenerationError("CUDA not available - GPU required for optimal performance")
        
        # Check memory
        memory_stats = self.memory_monitor.get_memory_stats()
        if memory_stats.gpu_total_gb < 6.0:
            self.logger.warning(f"GPU memory ({memory_stats.gpu_total_gb:.1f}GB) below recommended 8GB")
        
        if memory_stats.system_memory_gb < 8.0:
            self.logger.warning(f"System memory below recommended 16GB")
        
        # Check directories
        for dir_key, dir_path in self.config["directories"].items():
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        self.logger.info("System requirements validated")
    
    def generate_tja(self, audio_file_path: str, 
                    difficulty_levels: List[int] = [8, 9, 10],
                    output_path: Optional[str] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate TJA file from audio input
        
        Args:
            audio_file_path: Path to input audio file
            difficulty_levels: List of difficulty levels to generate (8, 9, 10)
            output_path: Optional output path for TJA file
            metadata: Optional metadata for TJA file
            
        Returns:
            Generation results including file paths and quality metrics
        """
        if not self.is_initialized:
            if not self.initialize():
                raise TJAGenerationError("Pipeline initialization failed")
        
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting TJA generation for {audio_file_path}")
            
            with MemoryContext(self.memory_monitor, "tja_generation"):
                # Step 1: Validate and preprocess audio
                audio_info = self._validate_audio_file(audio_file_path)
                
                # Step 2: Extract audio features (Phase 2)
                audio_features = self._extract_audio_features(audio_file_path)
                
                # Step 3: Generate TJA sequences (Phase 3)
                tja_sequences = self._generate_sequences(audio_features, difficulty_levels)
                
                # Step 4: Create TJA file content
                tja_content = self._create_tja_content(tja_sequences, audio_info, metadata)
                
                # Step 5: Quality assessment
                quality_metrics = self._assess_quality(tja_sequences, audio_features)
                
                # Step 6: Save TJA file
                output_file_path = self._save_tja_file(tja_content, output_path, audio_file_path)
                
                # Step 7: Generate results
                processing_time = time.time() - start_time
                results = self._create_results(
                    output_file_path, tja_sequences, quality_metrics, 
                    processing_time, audio_info
                )
                
                # Update statistics
                self._update_statistics(processing_time, True)
                
                self.logger.info(f"TJA generation completed successfully in {processing_time:.2f}s")
                return results
                
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_statistics(processing_time, False)
            
            self.logger.error(f"TJA generation failed: {e}")
            self.logger.debug(traceback.format_exc())
            
            raise TJAGenerationError(f"Generation failed: {str(e)}") from e
    
    def _validate_audio_file(self, audio_file_path: str) -> Dict[str, Any]:
        """Validate audio file and extract basic information"""
        audio_path = Path(audio_file_path)
        
        if not audio_path.exists():
            raise TJAGenerationError(f"Audio file not found: {audio_file_path}")
        
        # Check file extension
        supported_formats = self.config["file_formats"]["audio"]["supported_formats"]
        if audio_path.suffix.lower() not in supported_formats:
            raise TJAGenerationError(f"Unsupported audio format: {audio_path.suffix}")
        
        # Check file size (basic validation)
        file_size_mb = audio_path.stat().st_size / (1024 * 1024)
        max_size_mb = self.config["api"]["file_upload"]["max_file_size_mb"]
        
        if file_size_mb > max_size_mb:
            raise TJAGenerationError(f"Audio file too large: {file_size_mb:.1f}MB > {max_size_mb}MB")
        
        return {
            "file_path": str(audio_path),
            "file_name": audio_path.name,
            "file_size_mb": file_size_mb,
            "format": audio_path.suffix.lower()
        }
    
    def _extract_audio_features(self, audio_file_path: str) -> torch.Tensor:
        """Extract audio features using Phase 2 pipeline"""
        self.logger.info("Extracting audio features...")
        
        # Create temporary catalog entry for single file processing
        audio_path = Path(audio_file_path)
        temp_catalog = {
            "songs": [{
                "song_id": audio_path.stem,
                "audio_file_path": str(audio_path.absolute()),
                "metadata": {"title": audio_path.stem}
            }]
        }
        
        # Process audio features
        results = self.audio_analyzer.process_audio_features(temp_catalog, test_mode=True, test_count=1)
        
        if not results["processing_results"]:
            raise TJAGenerationError("Audio feature extraction failed")
        
        # Load the extracted features
        feature_result = results["processing_results"][0]
        if not feature_result["success"]:
            raise TJAGenerationError(f"Feature extraction failed: {feature_result.get('error', 'Unknown error')}")
        
        # Load feature tensor
        feature_path = feature_result["feature_tensor_path"]
        feature_data = torch.load(feature_path, map_location='cpu')
        
        if isinstance(feature_data, dict):
            features = feature_data["features"]
        else:
            features = feature_data
        
        self.logger.info(f"Audio features extracted: {features.shape}")
        return features

    def _generate_sequences(self, audio_features: torch.Tensor,
                           difficulty_levels: List[int]) -> Dict[int, torch.Tensor]:
        """Generate TJA sequences using Phase 3 model"""
        self.logger.info("Generating TJA sequences...")

        device = next(self.model.parameters()).device
        audio_features = audio_features.unsqueeze(0).to(device)  # Add batch dimension

        sequences = {}
        generation_params = self.config["phase3_integration"]["generation_parameters"]

        for difficulty in difficulty_levels:
            self.logger.info(f"Generating difficulty level {difficulty}...")

            # Map difficulty levels (8,9,10) to model indices (0,1,2)
            model_difficulty = difficulty - 8

            with torch.no_grad():
                try:
                    # Generate sequence
                    outputs = self.model(
                        audio_features=audio_features,
                        difficulty=torch.tensor([model_difficulty]).to(device),
                        return_loss=False
                    )

                    # Convert logits to sequence
                    generated_sequence = torch.argmax(outputs['logits'], dim=-1)
                    sequences[difficulty] = generated_sequence[0].cpu()  # Remove batch dimension

                    self.logger.info(f"Generated sequence for difficulty {difficulty}: {generated_sequence.shape}")

                except Exception as e:
                    self.logger.error(f"Failed to generate sequence for difficulty {difficulty}: {e}")
                    # Create fallback sequence
                    fallback_length = min(200, audio_features.shape[1] // 2)
                    sequences[difficulty] = torch.zeros(fallback_length, dtype=torch.long)

        return sequences

    def _create_tja_content(self, sequences: Dict[int, torch.Tensor],
                           audio_info: Dict[str, Any],
                           metadata: Optional[Dict[str, Any]]) -> str:
        """Create TJA file content from generated sequences"""
        self.logger.info("Creating TJA file content...")

        # Default metadata
        default_metadata = {
            "TITLE": metadata.get("title", audio_info["file_name"]) if metadata else audio_info["file_name"],
            "SUBTITLE": metadata.get("subtitle", "") if metadata else "",
            "BPM": metadata.get("bpm", 120.0) if metadata else 120.0,
            "WAVE": audio_info["file_name"],
            "OFFSET": metadata.get("offset", 0.0) if metadata else 0.0,
            "DEMOSTART": metadata.get("demostart", 10.0) if metadata else 10.0,
            "GENRE": metadata.get("genre", "ポップス") if metadata else "ポップス",
            "SCOREMODE": 2,
            "MAKER": "TJA Generator v1.0"
        }

        # Build TJA content
        tja_lines = []

        # Header
        for key, value in default_metadata.items():
            tja_lines.append(f"{key}:{value}")

        tja_lines.append("")  # Empty line after header

        # Generate courses for each difficulty
        difficulty_names = {8: "Oni", 9: "Oni", 10: "Oni"}

        for difficulty, sequence in sequences.items():
            tja_lines.append(f"COURSE:{difficulty_names[difficulty]}")
            tja_lines.append(f"LEVEL:{difficulty}")
            tja_lines.append(f"BALLOON:")
            tja_lines.append(f"SCOREINIT:300")
            tja_lines.append(f"SCOREDIFF:100")
            tja_lines.append("")

            tja_lines.append("#START")

            # Convert sequence to TJA notation
            tja_notation = self._sequence_to_tja_notation(sequence)
            tja_lines.extend(tja_notation)

            tja_lines.append("#END")
            tja_lines.append("")

        return "\n".join(tja_lines)

    def _sequence_to_tja_notation(self, sequence: torch.Tensor) -> List[str]:
        """Convert model output sequence to TJA notation"""
        # Note mapping: 0=blank, 1=don, 2=ka, 3=don_big, 4=ka_big, 5=drumroll, 6=end_roll, 7=special
        note_map = {0: "0", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "7"}

        # Convert sequence to string
        sequence_str = "".join(note_map.get(int(note), "0") for note in sequence)

        # Split into measures (16 notes per measure for simplicity)
        measures = []
        measure_length = 16

        for i in range(0, len(sequence_str), measure_length):
            measure = sequence_str[i:i + measure_length]
            if len(measure) < measure_length:
                measure = measure.ljust(measure_length, "0")  # Pad with blanks
            measures.append(measure + ",")

        # Ensure last measure ends properly
        if measures:
            measures[-1] = measures[-1].rstrip(",")

        return measures

    def _assess_quality(self, sequences: Dict[int, torch.Tensor],
                       audio_features: torch.Tensor) -> Dict[str, Any]:
        """Assess quality of generated sequences"""
        if not self.config["quality_assessment"]["enabled"]:
            return {"quality_assessment_disabled": True}

        self.logger.info("Assessing generation quality...")

        quality_results = {}

        for difficulty, sequence in sequences.items():
            metrics = self.quality_assessor.evaluate_sequence(
                sequence.numpy(),
                audio_features.numpy(),
                difficulty_level=difficulty
            )
            # Convert TJAQualityMetrics to dict for consistency
            if hasattr(metrics, 'to_dict'):
                quality_results[difficulty] = metrics.to_dict()
            else:
                quality_results[difficulty] = metrics

        # Overall quality score
        if quality_results:
            overall_scores = []
            for result in quality_results.values():
                if hasattr(result, 'overall_score'):
                    overall_scores.append(result.overall_score)
                elif isinstance(result, dict) and 'overall_score' in result:
                    overall_scores.append(result['overall_score'])
                else:
                    overall_scores.append(0.0)
            quality_results["overall_quality"] = np.mean(overall_scores) if overall_scores else 0.0

        return quality_results

    def _save_tja_file(self, tja_content: str, output_path: Optional[str],
                      audio_file_path: str) -> str:
        """Save TJA content to file"""
        if output_path is None:
            audio_path = Path(audio_file_path)
            output_path = str(audio_path.with_suffix(".tja"))

        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Write TJA file with proper encoding
        encoding = self.config["file_formats"]["tja"]["encoding"]
        with open(output_path, 'w', encoding=encoding) as f:
            f.write(tja_content)

        self.logger.info(f"TJA file saved: {output_path}")
        return str(output_path)

    def _create_results(self, output_file_path: str, sequences: Dict[int, torch.Tensor],
                       quality_metrics: Dict[str, Any], processing_time: float,
                       audio_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive results dictionary"""
        return {
            "success": True,
            "output_file_path": output_file_path,
            "processing_time_seconds": processing_time,
            "audio_info": audio_info,
            "generated_difficulties": list(sequences.keys()),
            "sequence_lengths": {diff: len(seq) for diff, seq in sequences.items()},
            "quality_metrics": quality_metrics,
            "pipeline_stats": self.processing_stats.copy(),
            "memory_usage": self.memory_monitor.get_memory_summary(),
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        }

    def _update_statistics(self, processing_time: float, success: bool):
        """Update pipeline processing statistics"""
        self.processing_stats["total_processed"] += 1
        self.processing_stats["total_processing_time"] += processing_time

        if success:
            self.processing_stats["successful_generations"] += 1
        else:
            self.processing_stats["failed_generations"] += 1

        # Update average processing time
        if self.processing_stats["total_processed"] > 0:
            self.processing_stats["average_processing_time"] = (
                self.processing_stats["total_processing_time"] /
                self.processing_stats["total_processed"]
            )

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status and statistics"""
        memory_stats = self.memory_monitor.get_memory_stats()

        return {
            "initialized": self.is_initialized,
            "processing_stats": self.processing_stats.copy(),
            "memory_usage": {
                "gpu_memory_gb": memory_stats.gpu_reserved_gb,
                "gpu_utilization_percent": memory_stats.gpu_utilization_percent,
                "system_memory_percent": memory_stats.system_memory_percent
            },
            "model_loaded": self.model is not None,
            "audio_analyzer_ready": self.audio_analyzer is not None,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        }

    def cleanup(self):
        """Cleanup pipeline resources"""
        self.logger.info("Cleaning up pipeline resources...")

        if self.memory_monitor:
            self.memory_monitor.cleanup_memory(aggressive=True)

        # Clear model from GPU memory
        if self.model is not None:
            del self.model
            self.model = None

        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        self.is_initialized = False
        self.logger.info("Pipeline cleanup completed")
