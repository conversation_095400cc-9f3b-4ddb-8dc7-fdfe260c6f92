"""
Phase 4: Neural Network Architecture

This phase implements the transformer-based neural network architecture for TJA
chart generation. It creates a multi-modal model that processes audio features
and generates corresponding note sequences with difficulty-aware generation
capabilities.

Key Components:
- Multi-scale audio encoder
- Cross-modal attention mechanisms
- Difficulty-aware decoder
- Pattern-guided generation
- Hardware-optimized architecture (RTX 3070)

Input: Audio features [T, 201] and note sequences [T, 45] from Phase 3
Output: Trained model architecture for Phase 5 optimization
"""

from .controller import Phase4Controller
from .tja_generator import TJAGeneratorModel
from .audio_encoder import AudioFeatureEncoder
from .sequence_decoder import SequenceDecoder
from .attention_modules import MultiHeadAttention, CrossModalAttention, TemporalAttention
from .pattern_library import PatternLibrary
from .loss_functions import TJAGenerationLoss, FocalLoss, TemporalConsistencyLoss

# Import existing phase4 modules
from .pipeline import TJAGenerationPipeline
from .cli import TJAGenerationCLI
from .quality_assessment import QualityAssessment, TJAQualityMetrics
from .deployment import DeploymentManager
from .config import PHASE_4_CONFIG

# Optional API import
try:
    from .api import TJAGenerationAPI
except ImportError:
    TJAGenerationAPI = None

__version__ = "1.0.0"
__all__ = [
    'Phase4Controller',
    'TJAGeneratorModel',
    'AudioFeatureEncoder',
    'SequenceDecoder',
    'MultiHeadAttention',
    'CrossModalAttention',
    'TemporalAttention',
    'PatternLibrary',
    'TJAGenerationLoss',
    'FocalLoss',
    'TemporalConsistencyLoss',
    "TJAGenerationPipeline",
    "TJAGenerationAPI",
    "TJAGenerationCLI",
    "QualityAssessment",
    "TJAQualityMetrics",
    "DeploymentManager",
    "PHASE_4_CONFIG"
]
