from typing import Dict, Optional
"""
TJA Generator Model

Main model class that combines audio encoder, sequence decoder, and pattern library
for end-to-end TJA generation from audio features.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from .audio_encoder import AudioFeatureEncoder
from .sequence_decoder import SequenceDecoder
from .attention_modules import CrossModalAttention
from .pattern_library import Pattern<PERSON>ibrary
from .loss_functions import TJAGenerationLoss


class TJAGeneratorModel(nn.Module):
    """
    Complete TJA generation model combining:
    1. Audio feature encoder (processes [T, 201] features)
    2. Cross-modal attention (aligns audio with rhythmic patterns)
    3. Sequence decoder (generates TJA note sequences)
    4. Pattern library (provides pattern guidance)
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        self.hidden_dims = config.get("hidden_dims", 256)
        self.vocab_size = config.get("note_types", 8)
        self.max_seq_len = config.get("max_sequence_length", 2000)
        
        # Audio feature encoder
        self.audio_encoder = AudioFeatureEncoder(config)
        
        # Cross-modal attention between audio and patterns (simplified for memory)
        # Disable cross-modal attention for memory optimization
        self.use_cross_modal_attention = False
        if self.use_cross_modal_attention:
            # Ensure num_heads is compatible with hidden_dims
            num_heads = min(config.get("num_attention_heads", 8), self.hidden_dims // 4)
            if num_heads == 0:
                num_heads = 1
            self.cross_modal_attention = CrossModalAttention(
                d_model=self.hidden_dims,
                num_heads=num_heads,
                dropout=config.get("dropout", 0.1)
            )
        else:
            self.cross_modal_attention = None
        
        # Sequence decoder
        self.sequence_decoder = SequenceDecoder(config)
        
        # Pattern library
        self.pattern_library = PatternLibrary(config)
        
        # Loss function
        self.loss_function = TJAGenerationLoss(config)
        
        # Model state
        self.training_step = 0
        self.validation_step = 0
        
    def forward(self, audio_features: torch.Tensor,
                target_sequence: Optional[torch.Tensor] = None,
                target_timing: Optional[torch.Tensor] = None,
                difficulty: Optional[torch.Tensor] = None,
                audio_mask: Optional[torch.Tensor] = None,
                return_loss: bool = True) -> Dict[str, torch.Tensor]:
        """
        Forward pass of TJA generator
        
        Args:
            audio_features: [batch, time, 201] audio features from Phase 2
            target_sequence: [batch, seq_len] target note sequence (for training)
            target_timing: [batch, seq_len] target timing sequence (for training)
            difficulty: [batch] difficulty level (0-2 for Oni 8-10)
            audio_mask: [batch, time] audio attention mask
            return_loss: Whether to compute and return loss
            
        Returns:
            Dictionary containing model outputs and optionally loss
        """
        batch_size = audio_features.shape[0]
        
        # Set default difficulty if not provided
        if difficulty is None:
            difficulty = torch.ones(batch_size, dtype=torch.long, device=audio_features.device)
        
        # 1. Encode audio features
        audio_encoding = self.audio_encoder(audio_features, audio_mask)
        encoded_audio = audio_encoding["encoded_features"]
        
        # 2. Generate pattern context for cross-modal attention (simplified)
        try:
            if target_sequence is not None and target_timing is not None:
                # Training: use target sequence to generate pattern context
                pattern_embeddings = self.pattern_library.encode_sequence_patterns(
                    target_sequence, target_timing, difficulty
                )
            else:
                # Inference: create simple pattern context
                pattern_length = min(encoded_audio.shape[1] // 8, 10)  # Smaller for memory
                pattern_embeddings = torch.zeros(
                    batch_size, pattern_length, self.pattern_library.embedding_dim,
                    device=encoded_audio.device
                )
        except Exception as e:
            # Fallback: empty pattern context
            pattern_embeddings = torch.zeros(
                batch_size, 1, self.pattern_library.embedding_dim,
                device=encoded_audio.device
            )
        
        # 3. Cross-modal attention between audio and patterns (simplified)
        if self.use_cross_modal_attention and self.cross_modal_attention is not None and pattern_embeddings.shape[1] > 0:
            cross_modal_output = self.cross_modal_attention(
                audio_features=encoded_audio,
                rhythm_features=pattern_embeddings,
                audio_mask=audio_mask
            )
            fused_audio_features = cross_modal_output["fused_features"]
        else:
            # Skip cross-modal attention for memory optimization
            fused_audio_features = encoded_audio
            cross_modal_output = {}
        
        # 4. Generate TJA sequence
        decoder_output = self.sequence_decoder(
            audio_features=fused_audio_features,
            target_sequence=target_sequence,
            target_timing=target_timing,
            difficulty=difficulty,
            audio_mask=audio_mask,
            pattern_guidance=True
        )
        
        # 5. Prepare outputs
        outputs = {
            "logits": decoder_output.get("logits"),
            "generated_sequence": decoder_output.get("generated_sequence"),
            "generated_timing": decoder_output.get("generated_timing"),
            "hidden_states": decoder_output.get("hidden_states"),
            "audio_encoding": audio_encoding,
            "cross_modal_attention": cross_modal_output,
            "decoder_output": decoder_output,
            "pattern_embeddings": pattern_embeddings
        }
        
        # 6. Compute loss if requested and targets are provided
        if return_loss and target_sequence is not None:
            # Create sequence mask (not audio mask)
            sequence_mask = (target_sequence != 0).float()  # Non-padding tokens

            targets = {
                "note_sequence": target_sequence,
                "timing_sequence": target_timing,
                "difficulty": difficulty,
                "attention_mask": sequence_mask  # Use sequence mask, not audio mask
            }
            
            loss_output = self.loss_function(decoder_output, targets, audio_features)
            outputs.update(loss_output)
            
            # Compute accuracy metrics
            accuracy_metrics = self.loss_function.compute_accuracy_metrics(
                decoder_output, targets
            )
            outputs["accuracy_metrics"] = accuracy_metrics
        
        return outputs
    
    def generate(self, audio_features: torch.Tensor,
                difficulty: int = 1,
                max_length: Optional[int] = None,
                temperature: float = 1.0,
                top_k: Optional[int] = None,
                top_p: Optional[float] = None,
                beam_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """
        Generate TJA sequence from audio features
        
        Args:
            audio_features: [batch, time, 201] audio features
            difficulty: Difficulty level (0-2 for Oni 8-10)
            max_length: Maximum sequence length
            temperature: Sampling temperature
            top_k: Top-k sampling
            top_p: Nucleus sampling
            beam_size: Beam search size (if provided, uses beam search)
            
        Returns:
            Generated TJA sequence and metadata
        """
        self.eval()
        
        with torch.no_grad():
            batch_size = audio_features.shape[0]
            difficulty_tensor = torch.full(
                (batch_size,), difficulty, dtype=torch.long, device=audio_features.device
            )
            
            if beam_size is not None:
                # Beam search generation
                return self._beam_search_generate(
                    audio_features, difficulty_tensor, beam_size, max_length
                )
            else:
                # Sampling-based generation
                return self._sampling_generate(
                    audio_features, difficulty_tensor, max_length, 
                    temperature, top_k, top_p
                )
    
    def _sampling_generate(self, audio_features: torch.Tensor,
                          difficulty: torch.Tensor,
                          max_length: Optional[int],
                          temperature: float,
                          top_k: Optional[int],
                          top_p: Optional[float]) -> Dict[str, torch.Tensor]:
        """Generate using sampling strategies"""
        # Forward pass without targets (inference mode)
        outputs = self.forward(
            audio_features=audio_features,
            difficulty=difficulty,
            return_loss=False
        )
        
        # Apply sampling strategies to logits if available
        if "logits" in outputs and outputs["logits"] is not None:
            logits = outputs["logits"]
            
            # Apply temperature
            if temperature != 1.0:
                logits = logits / temperature
            
            # Apply top-k filtering
            if top_k is not None:
                top_k_logits, top_k_indices = torch.topk(logits, top_k, dim=-1)
                logits_filtered = torch.full_like(logits, float('-inf'))
                logits_filtered.scatter_(-1, top_k_indices, top_k_logits)
                logits = logits_filtered
            
            # Apply top-p (nucleus) filtering
            if top_p is not None:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True, dim=-1)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                
                # Remove tokens with cumulative probability above the threshold
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                sorted_indices_to_remove[..., 0] = 0
                
                indices_to_remove = sorted_indices_to_remove.scatter(
                    -1, sorted_indices, sorted_indices_to_remove
                )
                logits[indices_to_remove] = float('-inf')
            
            # Sample from filtered distribution
            probs = F.softmax(logits, dim=-1)
            generated_sequence = torch.multinomial(probs.view(-1, self.vocab_size), 1)
            generated_sequence = generated_sequence.view(logits.shape[:-1])
            
            outputs["generated_sequence"] = generated_sequence
        
        return outputs
    
    def _beam_search_generate(self, audio_features: torch.Tensor,
                             difficulty: torch.Tensor,
                             beam_size: int,
                             max_length: Optional[int]) -> Dict[str, torch.Tensor]:
        """Generate using beam search"""
        # Use decoder's beam search method
        return self.sequence_decoder.beam_search_decode(
            audio_features, difficulty, beam_size, max_length
        )
    
    def compute_generation_metrics(self, generated_sequence: torch.Tensor,
                                  target_sequence: torch.Tensor,
                                  difficulty: torch.Tensor) -> Dict[str, float]:
        """
        Compute generation quality metrics
        
        Args:
            generated_sequence: [batch, seq_len] generated sequence
            target_sequence: [batch, seq_len] target sequence
            difficulty: [batch] difficulty levels
            
        Returns:
            Dictionary of quality metrics
        """
        metrics = {}
        
        # Exact match accuracy
        exact_matches = (generated_sequence == target_sequence).all(dim=1).float()
        metrics["exact_match_accuracy"] = exact_matches.mean().item()
        
        # Token-level accuracy
        token_matches = (generated_sequence == target_sequence).float()
        metrics["token_accuracy"] = token_matches.mean().item()
        
        # Note type distribution similarity
        for note_type in range(self.vocab_size):
            gen_count = (generated_sequence == note_type).float().mean()
            target_count = (target_sequence == note_type).float().mean()
            metrics[f"note_{note_type}_distribution_diff"] = abs(gen_count - target_count).item()
        
        # Difficulty appropriateness (simplified)
        # Check if generated sequence has appropriate complexity for difficulty
        complexity_scores = []
        for i, diff in enumerate(difficulty):
            seq = generated_sequence[i]
            # Simple complexity measure: ratio of non-blank notes
            non_blank_ratio = (seq != 0).float().mean()
            
            # Expected complexity ranges for each difficulty
            expected_ranges = {0: (0.3, 0.6), 1: (0.5, 0.8), 2: (0.7, 1.0)}
            expected_min, expected_max = expected_ranges.get(diff.item(), (0.3, 1.0))
            
            # Score based on how well it fits expected range
            if expected_min <= non_blank_ratio <= expected_max:
                complexity_scores.append(1.0)
            else:
                # Penalize based on distance from range
                if non_blank_ratio < expected_min:
                    score = non_blank_ratio / expected_min
                else:
                    score = expected_max / non_blank_ratio
                complexity_scores.append(score)
        
        metrics["difficulty_appropriateness"] = sum(complexity_scores) / len(complexity_scores)
        
        return metrics
    
    def save_checkpoint(self, filepath: str, optimizer_state: Optional[Dict] = None,
                       scheduler_state: Optional[Dict] = None, 
                       training_stats: Optional[Dict] = None):
        """Save model checkpoint"""
        checkpoint = {
            "model_state_dict": self.state_dict(),
            "config": self.config,
            "training_step": self.training_step,
            "validation_step": self.validation_step
        }
        
        if optimizer_state is not None:
            checkpoint["optimizer_state_dict"] = optimizer_state
        
        if scheduler_state is not None:
            checkpoint["scheduler_state_dict"] = scheduler_state
        
        if training_stats is not None:
            checkpoint["training_stats"] = training_stats
        
        torch.save(checkpoint, filepath)
    
    @classmethod
    def load_checkpoint(cls, filepath: str, device: Optional[torch.device] = None):
        """Load model from checkpoint"""
        if device is None:
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        checkpoint = torch.load(filepath, map_location=device)
        
        # Create model with saved config
        model = cls(checkpoint["config"])
        model.load_state_dict(checkpoint["model_state_dict"])
        model.training_step = checkpoint.get("training_step", 0)
        model.validation_step = checkpoint.get("validation_step", 0)
        
        return model, checkpoint
    
    def get_model_size(self) -> Dict[str, int]:
        """Get model size information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        component_sizes = {
            "audio_encoder": sum(p.numel() for p in self.audio_encoder.parameters()),
            "sequence_decoder": sum(p.numel() for p in self.sequence_decoder.parameters()),
        }

        if self.cross_modal_attention is not None:
            component_sizes["cross_modal_attention"] = sum(p.numel() for p in self.cross_modal_attention.parameters())
        else:
            component_sizes["cross_modal_attention"] = 0

        component_sizes["pattern_library"] = sum(p.numel() for p in self.pattern_library.parameters())
        
        return {
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "component_sizes": component_sizes,
            "model_size_mb": total_params * 4 / (1024 * 1024)  # Assuming float32
        }
