"""
Unified TJA Processing System

Consolidates all TJA parsing, validation, and processing functionality from multiple
phases into a single, consistent system implementing SOLID principles.
"""

import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod

from ..utils.base_classes import BaseProcessor, BaseValidator, ProcessingResult, ValidationResult
from ..config.unified_config_manager import UnifiedConfigManager
from ..utils.encoding_detector import detect_file_encoding


@dataclass
class TjaNote:
    """Standardized TJA note representation"""
    note_type: Union[int, str]
    position: float
    measure: int
    timing_ms: float
    branch: str = "normal"
    confidence: float = 1.0


@dataclass
class TjaCommand:
    """Standardized TJA command representation"""
    command_type: str
    value: Any
    position: float
    timing_ms: float


@dataclass
class TjaCourse:
    """Standardized TJA course representation"""
    difficulty: str
    level: int
    notes: List[TjaNote]
    commands: List[TjaCommand]
    metadata: Dict[str, Any]


@dataclass
class TjaChart:
    """Standardized TJA chart representation"""
    title: str
    bpm: float
    offset: float
    wave_file: str
    courses: Dict[str, TjaCourse]
    global_metadata: Dict[str, Any]
    file_path: Optional[str] = None


class TjaParserInterface(ABC):
    """Interface for TJA parsers following Interface Segregation Principle"""
    
    @abstractmethod
    def parse_file(self, file_path: str) -> TjaChart:
        """Parse TJA file and return standardized chart"""
        pass
    
    @abstractmethod
    def validate_format(self, file_path: str) -> ValidationResult:
        """Validate TJA file format"""
        pass


class TjaValidatorInterface(ABC):
    """Interface for TJA validators"""
    
    @abstractmethod
    def validate_chart(self, chart: TjaChart) -> ValidationResult:
        """Validate TJA chart content"""
        pass
    
    @abstractmethod
    def validate_difficulty(self, course: TjaCourse) -> ValidationResult:
        """Validate specific difficulty course"""
        pass


class UnifiedTjaParser(TjaParserInterface):
    """
    Unified TJA parser consolidating parsing functionality
    
    Replaces custom_tja_parser.py with consistent interface
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.UnifiedTjaParser")
        
        # Valid metadata fields according to TJA specification
        self.valid_metadata_fields = {
            'TITLE', 'TITLEEN', 'TITLEJA', 'TITLECN', 'TITLETW', 'TITLEKO',
            'SUBTITLE', 'SUBTITLEEN', 'SUBTITLEJA', 'SUBTITLECN', 'SUBTITLETW', 'SUBTITLEKO',
            'BPM', 'WAVE', 'OFFSET', 'DEMOSTART', 'GENRE', 'MAKER', 'LYRICS',
            'SONGVOL', 'SEVOL', 'SIDE', 'LIFE', 'GAME', 'HEADSCROLL',
            'BGIMAGE', 'BGMOVIE', 'MOVIEOFFSET', 'TAIKOWEBSKIN'
        }
        
        # Valid course types
        self.valid_courses = {
            'COURSE:Easy': 'easy',
            'COURSE:Normal': 'normal', 
            'COURSE:Hard': 'hard',
            'COURSE:Oni': 'oni',
            'COURSE:Edit': 'edit'
        }
    
    def parse_file(self, file_path: str) -> TjaChart:
        """Parse TJA file and return standardized chart"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"TJA file not found: {file_path}")
        
        # Read file with encoding detection
        lines, encoding = self._read_file_with_encoding(str(file_path))
        
        # Parse metadata and courses
        global_metadata = self._parse_global_metadata(lines)
        courses = self._parse_courses(lines, global_metadata)
        
        # Create standardized chart
        chart = TjaChart(
            title=global_metadata.get('TITLE', 'Unknown'),
            bpm=float(global_metadata.get('BPM', 120)),
            offset=float(global_metadata.get('OFFSET', 0)),
            wave_file=global_metadata.get('WAVE', ''),
            courses=courses,
            global_metadata=global_metadata,
            file_path=str(file_path)
        )
        
        self.logger.info(f"Parsed TJA file: {file_path.name} with {len(courses)} courses")
        return chart
    
    def validate_format(self, file_path: str) -> ValidationResult:
        """Validate TJA file format"""
        try:
            chart = self.parse_file(file_path)
            
            errors = []
            warnings = []
            
            # Check required fields
            if not chart.title or chart.title == 'Unknown':
                warnings.append("Missing or empty TITLE field")
            
            if chart.bpm <= 0:
                errors.append("Invalid BPM value")
            
            if not chart.wave_file:
                warnings.append("Missing WAVE field")
            
            if not chart.courses:
                errors.append("No courses found")
            
            # Calculate quality score
            quality_score = max(0.0, 1.0 - len(errors) * 0.3 - len(warnings) * 0.1)
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                quality_score=quality_score,
                errors=errors,
                warnings=warnings,
                metrics={
                    "courses_count": len(chart.courses),
                    "has_wave_field": bool(chart.wave_file),
                    "encoding_detected": True
                }
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                quality_score=0.0,
                errors=[f"Parse error: {str(e)}"],
                warnings=[],
                metrics={}
            )
    
    def _read_file_with_encoding(self, file_path: str) -> Tuple[List[str], str]:
        """Read TJA file with encoding detection"""
        encoding = detect_file_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
            return lines, encoding
        except UnicodeDecodeError:
            # Fallback encodings
            for fallback_encoding in ['utf-8', 'shift-jis', 'cp932']:
                try:
                    with open(file_path, 'r', encoding=fallback_encoding) as f:
                        lines = f.readlines()
                    return lines, fallback_encoding
                except UnicodeDecodeError:
                    continue
            
            raise UnicodeDecodeError(f"Could not decode file with any encoding: {file_path}")
    
    def _parse_global_metadata(self, lines: List[str]) -> Dict[str, Any]:
        """Parse global metadata from TJA lines"""
        metadata = {}
        
        for line in lines:
            line = line.split('//')[0].strip()  # Remove comments
            
            if ':' in line and not line.startswith('#') and not line.startswith('COURSE:'):
                key, value = line.split(':', 1)
                key = key.strip().upper()
                value = value.strip()
                
                if key in self.valid_metadata_fields:
                    # Convert numeric values
                    if key in ['BPM', 'OFFSET', 'DEMOSTART', 'SONGVOL', 'SEVOL', 'MOVIEOFFSET']:
                        try:
                            metadata[key] = float(value)
                        except ValueError:
                            metadata[key] = value
                    else:
                        metadata[key] = value
        
        return metadata
    
    def _parse_courses(self, lines: List[str], global_metadata: Dict[str, Any]) -> Dict[str, TjaCourse]:
        """Parse course data from TJA lines"""
        courses = {}
        current_course = None
        current_course_metadata = {}
        notation_lines = []
        
        for line in lines:
            line = line.split('//')[0].strip()  # Remove comments
            
            if not line:
                continue
            
            # Check for course start
            if line.startswith('COURSE:'):
                # Save previous course if exists
                if current_course and notation_lines:
                    courses[current_course] = self._create_course(
                        current_course, current_course_metadata, notation_lines, global_metadata
                    )
                
                # Start new course
                course_key = line.split(':')[1].strip()
                current_course = self.valid_courses.get(f'COURSE:{course_key}', course_key.lower())
                current_course_metadata = {}
                notation_lines = []
                
            elif current_course and ':' in line and not line.startswith('#'):
                # Course-specific metadata
                key, value = line.split(':', 1)
                key = key.strip().upper()
                value = value.strip()
                current_course_metadata[key] = value
                
            elif current_course and (line.startswith('#') or any(c in line for c in '0123456789')):
                # Notation data
                notation_lines.append(line)
        
        # Save last course
        if current_course and notation_lines:
            courses[current_course] = self._create_course(
                current_course, current_course_metadata, notation_lines, global_metadata
            )
        
        return courses
    
    def _create_course(self, difficulty: str, course_metadata: Dict[str, Any], 
                      notation_lines: List[str], global_metadata: Dict[str, Any]) -> TjaCourse:
        """Create standardized course from parsed data"""
        notes = []
        commands = []
        
        # Parse notation lines
        current_bpm = float(global_metadata.get('BPM', 120))
        current_time_ms = 0.0
        current_measure = 0
        
        for line in notation_lines:
            if line.startswith('#'):
                # Parse command
                command = self._parse_command(line, current_time_ms)
                if command:
                    commands.append(command)
                    if command.command_type == 'BPMCHANGE':
                        current_bpm = float(command.value)
            else:
                # Parse notes
                measure_notes = self._parse_measure(line, current_measure, current_time_ms, current_bpm)
                notes.extend(measure_notes)
                
                # Update timing for next measure
                if measure_notes:
                    measure_duration_ms = (60000 * 4) / current_bpm  # Assuming 4/4 time
                    current_time_ms += measure_duration_ms
                    current_measure += 1
        
        return TjaCourse(
            difficulty=difficulty,
            level=int(course_metadata.get('LEVEL', 0)),
            notes=notes,
            commands=commands,
            metadata=course_metadata
        )
    
    def _parse_command(self, line: str, timing_ms: float) -> Optional[TjaCommand]:
        """Parse TJA command from line"""
        if not line.startswith('#'):
            return None
        
        line = line[1:]  # Remove #
        
        if ' ' in line:
            command_type, value = line.split(' ', 1)
        else:
            command_type = line
            value = None
        
        return TjaCommand(
            command_type=command_type.upper(),
            value=value,
            position=0.0,  # Will be calculated later
            timing_ms=timing_ms
        )
    
    def _parse_measure(self, line: str, measure_num: int, start_time_ms: float, bpm: float) -> List[TjaNote]:
        """Parse notes from measure line"""
        if ',' not in line:
            return []
        
        measure_data = line.replace(',', '').strip()
        notes = []
        
        if not measure_data:
            return notes
        
        # Calculate timing for each note
        measure_duration_ms = (60000 * 4) / bpm  # 4/4 time signature
        note_count = len(measure_data)
        ms_per_note = measure_duration_ms / note_count if note_count > 0 else 0
        
        for i, note_char in enumerate(measure_data):
            if note_char == '0':  # Skip blank notes
                continue
            
            note_timing = start_time_ms + (i * ms_per_note)
            note_position = i / note_count if note_count > 0 else 0.0
            
            # Convert note character to type
            note_type = int(note_char) if note_char.isdigit() else note_char
            
            note = TjaNote(
                note_type=note_type,
                position=note_position,
                measure=measure_num,
                timing_ms=note_timing
            )
            
            notes.append(note)
        
        return notes


class UnifiedTjaValidator(TjaValidatorInterface, BaseValidator):
    """
    Unified TJA validator consolidating validation functionality

    Replaces notation_validator.py and tja_format_validator.py
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)

        # Validation thresholds
        self.min_notes_per_difficulty = {
            'easy': 50,
            'normal': 100,
            'hard': 200,
            'oni': 300,
            'edit': 100
        }

        self.valid_note_types = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9}  # Standard TJA note types

    def validate(self, data: TjaChart) -> ValidationResult:
        """Validate TJA chart according to BaseValidator interface"""
        return self.validate_chart(data)

    def validate_chart(self, chart: TjaChart) -> ValidationResult:
        """Validate complete TJA chart"""
        errors = []
        warnings = []
        metrics = {}

        # Validate global metadata
        if not chart.title or chart.title == 'Unknown':
            warnings.append("Missing or empty title")

        if chart.bpm <= 0 or chart.bpm > 300:
            errors.append(f"Invalid BPM: {chart.bpm}")

        if abs(chart.offset) > 10:  # Offset should be reasonable
            warnings.append(f"Large offset value: {chart.offset}")

        # Validate courses
        course_scores = []
        for difficulty, course in chart.courses.items():
            course_result = self.validate_difficulty(course)

            if not course_result.is_valid:
                errors.extend([f"{difficulty}: {e}" for e in course_result.errors])

            warnings.extend([f"{difficulty}: {w}" for w in course_result.warnings])
            course_scores.append(course_result.quality_score)
            metrics[f"{difficulty}_metrics"] = course_result.metrics

        # Calculate overall quality
        overall_quality = sum(course_scores) / len(course_scores) if course_scores else 0.0

        metrics.update({
            "total_courses": len(chart.courses),
            "average_course_quality": overall_quality,
            "has_wave_file": bool(chart.wave_file),
            "bpm": chart.bpm
        })

        return self._create_validation_result(
            is_valid=len(errors) == 0,
            quality_score=overall_quality,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )

    def validate_difficulty(self, course: TjaCourse) -> ValidationResult:
        """Validate specific difficulty course"""
        errors = []
        warnings = []
        metrics = {}

        # Check minimum note count
        min_notes = self.min_notes_per_difficulty.get(course.difficulty, 100)
        if len(course.notes) < min_notes:
            warnings.append(f"Low note count: {len(course.notes)} (minimum: {min_notes})")

        # Validate note types
        invalid_notes = 0
        for note in course.notes:
            if isinstance(note.note_type, int) and note.note_type not in self.valid_note_types:
                invalid_notes += 1

        if invalid_notes > 0:
            errors.append(f"Invalid note types found: {invalid_notes}")

        # Check timing consistency
        timing_issues = self._check_timing_consistency(course.notes)
        if timing_issues:
            warnings.extend(timing_issues)

        # Calculate quality metrics
        note_density = len(course.notes) / (max(note.timing_ms for note in course.notes) / 1000) if course.notes else 0
        timing_quality = 1.0 - (len(timing_issues) / len(course.notes)) if course.notes else 0

        metrics.update({
            "note_count": len(course.notes),
            "command_count": len(course.commands),
            "note_density_per_second": note_density,
            "timing_quality": timing_quality,
            "invalid_notes": invalid_notes
        })

        # Calculate overall quality score
        quality_score = max(0.0, 1.0 - len(errors) * 0.3 - len(warnings) * 0.1)

        return self._create_validation_result(
            is_valid=len(errors) == 0,
            quality_score=quality_score,
            errors=errors,
            warnings=warnings,
            metrics=metrics
        )

    def _check_timing_consistency(self, notes: List[TjaNote]) -> List[str]:
        """Check for timing consistency issues"""
        issues = []

        if len(notes) < 2:
            return issues

        # Sort notes by timing
        sorted_notes = sorted(notes, key=lambda n: n.timing_ms)

        # Check for overlapping notes
        for i in range(len(sorted_notes) - 1):
            current = sorted_notes[i]
            next_note = sorted_notes[i + 1]

            time_diff = next_note.timing_ms - current.timing_ms

            if time_diff < 50:  # Less than 50ms between notes
                issues.append(f"Notes too close: {time_diff:.1f}ms at {current.timing_ms:.1f}ms")

            if time_diff > 5000:  # More than 5 seconds between notes
                issues.append(f"Large gap between notes: {time_diff:.1f}ms")

        return issues


class UnifiedTjaProcessor(BaseProcessor):
    """
    Unified TJA processing system consolidating all TJA functionality

    Replaces TJAProcessor and provides consistent interface for all TJA operations
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config, "UnifiedTjaProcessor")

        # Initialize configuration
        self.config_manager = UnifiedConfigManager()

        # Initialize components
        self.parser = UnifiedTjaParser(config)
        self.validator = UnifiedTjaValidator(config)

        self.logger.info("UnifiedTjaProcessor initialized with consolidated components")

    def process(self, input_data: Union[str, Dict[str, Any]]) -> ProcessingResult:
        """
        Process TJA data according to BaseProcessor interface

        Args:
            input_data: TJA file path or processing parameters

        Returns:
            ProcessingResult with parsed and validated TJA data
        """
        with self._processing_context("unified_tja_processing"):
            try:
                if isinstance(input_data, str):
                    # Single TJA file processing
                    result = self.process_tja_file(input_data)
                else:
                    # Batch processing or custom parameters
                    result = self._process_tja_batch(input_data)

                return ProcessingResult(
                    success=True,
                    data=result,
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )

            except Exception as e:
                return ProcessingResult(
                    success=False,
                    error_message=str(e),
                    processing_time_seconds=0.0,
                    memory_usage_mb=self.memory_monitor.get_current_usage()
                )

    def process_tja_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process single TJA file with parsing and validation

        Args:
            file_path: Path to TJA file

        Returns:
            Dictionary containing parsed chart and validation results
        """
        # Parse TJA file
        chart = self.parser.parse_file(file_path)

        # Validate chart
        validation_result = self.validator.validate_chart(chart)

        # Resolve audio file path
        audio_path = self._resolve_audio_file(chart)

        return {
            "chart": asdict(chart),
            "validation": asdict(validation_result),
            "audio_path": audio_path,
            "processing_metadata": {
                "file_path": file_path,
                "courses_found": len(chart.courses),
                "total_notes": sum(len(course.notes) for course in chart.courses.values()),
                "is_valid": validation_result.is_valid,
                "quality_score": validation_result.quality_score
            }
        }

    def _resolve_audio_file(self, chart: TjaChart) -> Optional[str]:
        """Resolve audio file path from TJA chart"""
        if not chart.wave_file:
            return None

        if not chart.file_path:
            return chart.wave_file

        # Try relative to TJA file
        tja_dir = Path(chart.file_path).parent
        audio_path = tja_dir / chart.wave_file

        if audio_path.exists():
            return str(self.config_manager.resolve_path(audio_path))

        # Try common audio extensions
        base_name = Path(chart.wave_file).stem
        for ext in ['.mp3', '.wav', '.ogg', '.flac']:
            audio_path = tja_dir / f"{base_name}{ext}"
            if audio_path.exists():
                return str(self.config_manager.resolve_path(audio_path))

        return None

    def _process_tja_batch(self, batch_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process batch of TJA files"""
        tja_files = batch_data.get("tja_files", [])
        results = {}

        for tja_file in tja_files:
            try:
                result = self.process_tja_file(tja_file)
                results[tja_file] = result
            except Exception as e:
                self.logger.error(f"Failed to process {tja_file}: {e}")
                results[tja_file] = {"error": str(e)}

        return {
            "batch_results": results,
            "processed_count": len([r for r in results.values() if "error" not in r]),
            "failed_count": len([r for r in results.values() if "error" in r])
        }
