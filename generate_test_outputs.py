#!/usr/bin/env python3
"""
Generate Minimal Test Outputs

Creates standardized minimal test outputs for all phases to verify
system integrity without processing large datasets.
"""

import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.schemas.validation_schemas import (
    generate_all_test_outputs, 
    validate_all_test_outputs,
    create_minimal_test_output
)


def main():
    """Generate and validate minimal test outputs"""
    print("🧪 Generating Minimal Test Outputs for TJA Generator")
    print("=" * 60)
    
    # Create test outputs directory
    test_dir = "test_outputs"
    Path(test_dir).mkdir(exist_ok=True)
    
    try:
        # Generate test outputs for all phases
        print("📝 Generating minimal test outputs...")
        output_files = generate_all_test_outputs(test_dir)
        
        print(f"✅ Generated {len(output_files)} test output files:")
        for file_path in output_files:
            print(f"   - {file_path}")
        
        # Validate all generated outputs
        print("\n🔍 Validating generated test outputs...")
        validation_results = validate_all_test_outputs(test_dir)
        
        # Display validation results
        print("\n📊 Validation Results:")
        print(f"Overall Valid: {'✅ YES' if validation_results['overall_valid'] else '❌ NO'}")
        print(f"Valid Phases: {validation_results['summary']['valid_phases']}/{validation_results['summary']['total_phases']}")
        print(f"Total Errors: {validation_results['summary']['total_errors']}")
        print(f"Total Warnings: {validation_results['summary']['total_warnings']}")
        
        # Show phase-by-phase results
        print("\n📋 Phase-by-Phase Results:")
        for phase_key, phase_result in validation_results["phase_results"].items():
            phase_num = phase_key.replace("phase_", "")
            status = "✅ VALID" if phase_result.get("overall_valid", False) else "❌ INVALID"
            print(f"   Phase {phase_num}: {status}")
            
            if not phase_result.get("overall_valid", False):
                if "error" in phase_result:
                    print(f"      Error: {phase_result['error']}")
                elif "summary" in phase_result:
                    summary = phase_result["summary"]
                    if summary.get("total_errors", 0) > 0:
                        print(f"      Errors: {summary['total_errors']}")
                    if summary.get("total_warnings", 0) > 0:
                        print(f"      Warnings: {summary['total_warnings']}")
        
        # Save validation report
        report_file = Path(test_dir) / "validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Validation report saved to: {report_file}")
        
        # Display sample output structure
        print("\n📋 Sample Output Structure (Phase 1):")
        sample_file = Path(test_dir) / "phase1_minimal_test.json"
        if sample_file.exists():
            with open(sample_file, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)
            
            # Pretty print first few levels
            print("```json")
            print(json.dumps({
                "phase_metadata": sample_data.get("phase_metadata", {}),
                "metrics": {k: "..." for k in sample_data.get("metrics", {}).keys()},
                "data": {k: "..." for k in sample_data.get("data", {}).keys()},
                "outputs": sample_data.get("outputs", {})
            }, indent=2))
            print("```")
        
        # Return appropriate exit code
        return 0 if validation_results['overall_valid'] else 1
        
    except Exception as e:
        print(f"❌ Error generating test outputs: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
