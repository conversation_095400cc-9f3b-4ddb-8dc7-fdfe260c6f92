#!/usr/bin/env python3
"""
Comprehensive System Testing Script

This script tests the refactored TJA Generator system to ensure all phases
work correctly with the new compliant output structures.
"""

import sys
import json
import time
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('system_test_results.log')
        ]
    )
    return logging.getLogger(__name__)

class SystemTester:
    """Comprehensive system testing"""
    
    def __init__(self, test_mode: bool = True, test_count: int = 5):
        self.logger = setup_logging()
        self.test_mode = test_mode
        self.test_count = test_count
        self.test_results = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "test_mode": test_mode,
            "test_count": test_count,
            "phases_tested": {},
            "overall_success": False,
            "issues": [],
            "performance_metrics": {}
        }
    
    def test_phase1_execution(self) -> bool:
        """Test Phase 1 execution with compliant output"""
        self.logger.info("🔄 Testing Phase 1: Data Analysis and Preprocessing")
        
        try:
            # Import Phase 1 controller
            from src.phase_1.controller import Phase1Controller
            from src.shared.config.unified_config_manager import UnifiedConfigManager
            from src.shared.utils.resource_manager import ResourceManager
            
            # Initialize controller
            config_manager = UnifiedConfigManager()
            resource_manager = ResourceManager()
            controller = Phase1Controller(config_manager, resource_manager)
            
            # Create mock arguments
            class MockArgs:
                def __init__(self):
                    self.test = self.test_mode
                    self.count = self.test_count
                    self.validate_only = False
                    self.data_dir = None
            
            args = MockArgs()
            
            # Test execution
            start_time = time.time()
            success = controller.execute(args)
            execution_time = time.time() - start_time
            
            self.test_results["phases_tested"]["phase_1"] = {
                "success": success,
                "execution_time_seconds": execution_time,
                "test_mode": self.test_mode,
                "test_count": self.test_count
            }
            
            if success:
                self.logger.info("✅ Phase 1 execution successful")
                
                # Validate output structure
                if self._validate_phase1_output_structure():
                    self.logger.info("✅ Phase 1 output structure compliant")
                    return True
                else:
                    self.logger.error("❌ Phase 1 output structure non-compliant")
                    return False
            else:
                self.logger.error("❌ Phase 1 execution failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Phase 1 test error: {e}")
            self.test_results["issues"].append(f"Phase 1 test error: {e}")
            return False
    
    def test_phase2_execution(self) -> bool:
        """Test Phase 2 execution with compliant output"""
        self.logger.info("🔄 Testing Phase 2: Audio Feature Extraction")
        
        # Check if Phase 1 output exists
        catalog_path = Path("data/processed/catalog.json")
        if not catalog_path.exists():
            self.logger.warning("⚠️ Phase 1 catalog not found, skipping Phase 2 test")
            self.test_results["phases_tested"]["phase_2"] = {
                "success": False,
                "skipped": True,
                "reason": "Phase 1 catalog not found"
            }
            return True  # Not a failure, just skipped
        
        try:
            # Import Phase 2 controller
            from src.phase_2.controller import Phase2Controller
            from src.shared.config.unified_config_manager import UnifiedConfigManager
            from src.shared.utils.resource_manager import ResourceManager
            
            # Initialize controller
            config_manager = UnifiedConfigManager()
            resource_manager = ResourceManager()
            controller = Phase2Controller(config_manager, resource_manager)
            
            # Create mock arguments
            class MockArgs:
                def __init__(self):
                    self.test = self.test_mode
                    self.count = self.test_count
                    self.validate_only = False
                    self.catalog = None
            
            args = MockArgs()
            
            # Test execution
            start_time = time.time()
            success = controller.execute(args)
            execution_time = time.time() - start_time
            
            self.test_results["phases_tested"]["phase_2"] = {
                "success": success,
                "execution_time_seconds": execution_time,
                "test_mode": self.test_mode,
                "test_count": self.test_count
            }
            
            if success:
                self.logger.info("✅ Phase 2 execution successful")
                
                # Validate output structure
                if self._validate_phase2_output_structure():
                    self.logger.info("✅ Phase 2 output structure compliant")
                    return True
                else:
                    self.logger.error("❌ Phase 2 output structure non-compliant")
                    return False
            else:
                self.logger.error("❌ Phase 2 execution failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Phase 2 test error: {e}")
            self.test_results["issues"].append(f"Phase 2 test error: {e}")
            return False
    
    def test_data_flow_integrity(self) -> bool:
        """Test data flow integrity between phases"""
        self.logger.info("🔄 Testing data flow integrity")
        
        try:
            # Check Phase 1 -> Phase 2 data flow
            catalog_path = Path("data/processed/catalog.json")
            if catalog_path.exists():
                with open(catalog_path, 'r', encoding='utf-8') as f:
                    phase1_catalog = json.load(f)
                
                # Validate catalog structure
                required_fields = ["phase_metadata", "songs", "processing_statistics"]
                missing_fields = [field for field in required_fields if field not in phase1_catalog]
                
                if missing_fields:
                    self.test_results["issues"].append(f"Phase 1 catalog missing fields: {missing_fields}")
                    return False
                
                # Check song entries
                songs = phase1_catalog.get("songs", [])
                phase2_ready_count = sum(1 for song in songs if song.get("phase_2_ready", False))
                
                self.logger.info(f"📊 Phase 1 catalog: {len(songs)} songs, {phase2_ready_count} Phase 2 ready")
            
            # Check Phase 2 -> Phase 3 data flow
            phase3_catalog_path = Path("data/processed/phase3_catalog.json")
            if phase3_catalog_path.exists():
                with open(phase3_catalog_path, 'r', encoding='utf-8') as f:
                    phase3_catalog = json.load(f)
                
                songs = phase3_catalog.get("songs", [])
                phase3_ready_count = sum(1 for song in songs if song.get("phase_3_ready", False))
                
                self.logger.info(f"📊 Phase 3 catalog: {len(songs)} songs, {phase3_ready_count} Phase 3 ready")
            
            self.logger.info("✅ Data flow integrity validated")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Data flow integrity test error: {e}")
            self.test_results["issues"].append(f"Data flow integrity error: {e}")
            return False
    
    def _validate_phase1_output_structure(self) -> bool:
        """Validate Phase 1 output structure"""
        required_paths = [
            "data/processed/catalog.json",
            "data/processed/reference_metadata/",
            "data/processed/notation_data/",
            "data/processed/validation_reports/"
        ]
        
        for path_str in required_paths:
            path = Path(path_str)
            if not path.exists():
                self.test_results["issues"].append(f"Missing Phase 1 output: {path_str}")
                return False
        
        return True
    
    def _validate_phase2_output_structure(self) -> bool:
        """Validate Phase 2 output structure"""
        required_paths = [
            "data/processed/phase3_catalog.json",
            "data/processed/audio_features/"
        ]
        
        for path_str in required_paths:
            path = Path(path_str)
            if not path.exists():
                self.test_results["issues"].append(f"Missing Phase 2 output: {path_str}")
                return False
        
        return True
    
    def test_system_performance(self) -> bool:
        """Test system performance metrics"""
        self.logger.info("🔄 Testing system performance")
        
        try:
            from src.shared.utils.hardware_monitor import get_system_info
            
            system_info = get_system_info()
            
            self.test_results["performance_metrics"] = {
                "cpu_cores": system_info['cpu']['logical_cores'],
                "memory_total_gb": system_info['memory']['total_gb'],
                "memory_available_gb": system_info['memory']['available_gb'],
                "gpu_available": system_info['gpu']['cuda_available'],
                "gpu_name": system_info['gpu'].get('name', 'N/A'),
                "gpu_memory_gb": system_info['gpu'].get('memory_total_gb', 0)
            }
            
            # Performance recommendations
            recommendations = []
            
            if system_info['memory']['total_gb'] < 30:
                recommendations.append(f"Consider upgrading RAM: {system_info['memory']['total_gb']:.1f}GB < 32GB recommended")
            
            if system_info['cpu']['logical_cores'] < 12:
                recommendations.append(f"Consider upgrading CPU: {system_info['cpu']['logical_cores']} cores < 16 recommended")
            
            if not system_info['gpu']['cuda_available']:
                recommendations.append("CUDA not available - GPU acceleration disabled")
            
            if recommendations:
                self.test_results["performance_recommendations"] = recommendations
            
            self.logger.info("✅ System performance metrics collected")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Performance test error: {e}")
            self.test_results["issues"].append(f"Performance test error: {e}")
            return False
    
    def run_comprehensive_tests(self) -> bool:
        """Run comprehensive system tests"""
        self.logger.info("🚀 Starting comprehensive system testing...")
        
        test_steps = [
            ("System Performance", self.test_system_performance),
            ("Phase 1 Execution", self.test_phase1_execution),
            ("Phase 2 Execution", self.test_phase2_execution),
            ("Data Flow Integrity", self.test_data_flow_integrity)
        ]
        
        overall_success = True
        
        for step_name, test_func in test_steps:
            self.logger.info(f"\n📋 {step_name}")
            try:
                success = test_func()
                if not success:
                    overall_success = False
                    self.logger.error(f"❌ {step_name} test failed")
                else:
                    self.logger.info(f"✅ {step_name} test passed")
            except Exception as e:
                self.logger.error(f"❌ {step_name} test error: {e}")
                overall_success = False
                self.test_results["issues"].append(f"{step_name} test error: {e}")
        
        self.test_results["overall_success"] = overall_success
        
        # Save test results
        with open("system_test_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2)
        
        return overall_success
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        self.logger.info("\n" + "="*80)
        self.logger.info("COMPREHENSIVE SYSTEM TEST REPORT")
        self.logger.info("="*80)
        
        if self.test_results["overall_success"]:
            self.logger.info("🎉 OVERALL STATUS: ALL TESTS PASSED")
        else:
            self.logger.info("❌ OVERALL STATUS: SOME TESTS FAILED")
        
        # Test configuration
        self.logger.info(f"\n⚙️  Test Configuration:")
        self.logger.info(f"  Test Mode: {'Enabled' if self.test_mode else 'Disabled'}")
        self.logger.info(f"  Test Count: {self.test_count} files")
        
        # Phase test results
        self.logger.info("\n📊 Phase Test Results:")
        for phase_name, phase_result in self.test_results["phases_tested"].items():
            if phase_result.get("skipped"):
                status = f"⏭️  SKIPPED ({phase_result.get('reason', 'Unknown')})"
            else:
                status = "✅ PASSED" if phase_result["success"] else "❌ FAILED"
                if not phase_result.get("skipped"):
                    status += f" ({phase_result.get('execution_time_seconds', 0):.2f}s)"
            
            self.logger.info(f"  {phase_name}: {status}")
        
        # Performance metrics
        if "performance_metrics" in self.test_results:
            perf = self.test_results["performance_metrics"]
            self.logger.info(f"\n🖥️  System Performance:")
            self.logger.info(f"  CPU: {perf['cpu_cores']} cores")
            self.logger.info(f"  Memory: {perf['memory_total_gb']:.1f}GB total, {perf['memory_available_gb']:.1f}GB available")
            self.logger.info(f"  GPU: {perf['gpu_name']} ({'Available' if perf['gpu_available'] else 'Not Available'})")
            if perf['gpu_available']:
                self.logger.info(f"  GPU Memory: {perf['gpu_memory_gb']:.1f}GB")
        
        # Issues
        if self.test_results["issues"]:
            self.logger.info("\n⚠️  Issues Found:")
            for issue in self.test_results["issues"]:
                self.logger.info(f"  • {issue}")
        
        # Performance recommendations
        if "performance_recommendations" in self.test_results:
            self.logger.info("\n💡 Performance Recommendations:")
            for rec in self.test_results["performance_recommendations"]:
                self.logger.info(f"  • {rec}")
        
        self.logger.info(f"\n📄 Detailed results saved to: system_test_results.json")
        self.logger.info(f"📄 Log file saved to: system_test_results.log")

def main():
    """Main testing entry point"""
    parser = argparse.ArgumentParser(description="Comprehensive System Testing")
    parser.add_argument("--full-test", action="store_true", help="Run full test (not test mode)")
    parser.add_argument("--count", type=int, default=5, help="Number of test files to process")
    
    args = parser.parse_args()
    
    test_mode = not args.full_test
    test_count = args.count
    
    tester = SystemTester(test_mode=test_mode, test_count=test_count)
    
    try:
        success = tester.run_comprehensive_tests()
        tester.generate_test_report()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing cancelled by user")
        return 1
    except Exception as e:
        print(f"❌ Fatal testing error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
