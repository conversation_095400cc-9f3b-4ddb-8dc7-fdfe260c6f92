"""
Core Components Test Suite

Tests fundamental components including base classes, unified processors,
configuration management, and path handling.
"""

import sys
import unittest
import time
import tempfile
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.base_classes import BaseProcessor, BaseValidator, ProcessingResult, ValidationResult
from src.config.unified_config_manager import UnifiedConfigManager
from src.path_management.path_manager import PathManager, PathType
from src.file_io.unified_io_manager import UnifiedIoManager
from src.utils.resource_manager import ResourceManager
from src.utils.memory_monitor import MemoryMonitor


class TestBaseClasses(unittest.TestCase):
    """Test base classes functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_config = {"test_param": "test_value"}
    
    def test_base_processor_initialization(self):
        """Test BaseProcessor initialization"""
        class TestProcessor(BaseProcessor):
            def process(self, input_data):
                return ProcessingResult(success=True, data=input_data)
        
        processor = TestProcessor(self.test_config)
        self.assertIsNotNone(processor.logger)
        self.assertIsNotNone(processor.memory_monitor)
        self.assertIsNotNone(processor.resource_manager)
    
    def test_processing_result_structure(self):
        """Test ProcessingResult data structure"""
        result = ProcessingResult(
            success=True,
            data={"test": "data"},
            processing_time_seconds=1.5,
            memory_usage_mb=100.0
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.data["test"], "data")
        self.assertEqual(result.processing_time_seconds, 1.5)
        self.assertEqual(result.memory_usage_mb, 100.0)
    
    def test_validation_result_structure(self):
        """Test ValidationResult data structure"""
        result = ValidationResult(
            is_valid=True,
            quality_score=0.85,
            errors=[],
            warnings=["Minor warning"],
            metrics={"accuracy": 0.95}
        )
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.quality_score, 0.85)
        self.assertEqual(len(result.warnings), 1)
        self.assertEqual(result.metrics["accuracy"], 0.95)


class TestConfigurationSystem(unittest.TestCase):
    """Test unified configuration management"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config_manager = UnifiedConfigManager()
    
    def test_phase_config_retrieval(self):
        """Test phase configuration retrieval"""
        phase1_config = self.config_manager.get_phase_config(1)
        
        self.assertIsInstance(phase1_config, dict)
        self.assertIn("hardware", phase1_config)
        self.assertIn("paths", phase1_config)
        self.assertIn("processing", phase1_config)
    
    def test_hardware_detection(self):
        """Test hardware configuration detection"""
        hardware_config = self.config_manager.hardware_config
        
        self.assertIsNotNone(hardware_config.target_gpu)
        self.assertGreater(hardware_config.max_vram_gb, 0)
        self.assertGreater(hardware_config.parallel_workers, 0)
    
    def test_path_configuration(self):
        """Test path configuration"""
        path_config = self.config_manager.path_config
        
        self.assertIsNotNone(path_config.workspace_root)
        self.assertIsNotNone(path_config.data_directory)
        self.assertIsNotNone(path_config.output_directory)


class TestPathManagement(unittest.TestCase):
    """Test path management system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.path_manager = PathManager()
    
    def test_path_resolution(self):
        """Test path resolution functionality"""
        data_path = self.path_manager.get_standardized_path(PathType.DATA_RAW)
        
        self.assertIsInstance(data_path, Path)
        self.assertTrue(data_path.is_absolute())
    
    def test_path_normalization(self):
        """Test path normalization"""
        test_path = "test\\path\\with\\backslashes"
        normalized = self.path_manager.normalize_path_separators(test_path)
        
        self.assertNotIn("\\", normalized)
        self.assertIn("/", normalized)
    
    def test_directory_creation(self):
        """Test directory creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_path = Path(temp_dir) / "test_subdir"
            created_path = self.path_manager.ensure_directory_exists(test_path)
            
            self.assertTrue(created_path.exists())
            self.assertTrue(created_path.is_dir())
    
    def test_filename_standardization(self):
        """Test filename standardization"""
        test_filename = "Test File With Spaces & Special!Chars.txt"
        standardized = self.path_manager.standardize_file_name(test_filename)
        
        self.assertNotIn(" ", standardized)
        self.assertNotIn("&", standardized)
        self.assertNotIn("!", standardized)


class TestUnifiedIoManager(unittest.TestCase):
    """Test unified I/O management"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.io_manager = UnifiedIoManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_json_file_handling(self):
        """Test JSON file read/write operations"""
        test_data = {"test": "data", "number": 42}
        test_file = Path(self.temp_dir) / "test.json"
        
        # Test write
        write_result = self.io_manager.write_file(str(test_file), test_data)
        self.assertTrue(write_result.success)
        
        # Test read
        read_result = self.io_manager.read_file(str(test_file))
        self.assertTrue(read_result.success)
        self.assertEqual(read_result.data, test_data)
    
    def test_text_file_handling(self):
        """Test text file read/write operations"""
        test_content = "This is test content\nWith multiple lines"
        test_file = Path(self.temp_dir) / "test.txt"
        
        # Test write
        write_result = self.io_manager.write_file(str(test_file), test_content)
        self.assertTrue(write_result.success)
        
        # Test read
        read_result = self.io_manager.read_file(str(test_file))
        self.assertTrue(read_result.success)
        self.assertEqual(read_result.data, test_content)
    
    def test_batch_operations(self):
        """Test batch file operations"""
        test_files = {
            str(Path(self.temp_dir) / "file1.json"): {"data": 1},
            str(Path(self.temp_dir) / "file2.json"): {"data": 2},
            str(Path(self.temp_dir) / "file3.txt"): "Text content"
        }
        
        # Test batch write
        write_results = self.io_manager.batch_write_files(test_files)
        
        for file_path, result in write_results.items():
            self.assertTrue(result.success, f"Failed to write {file_path}")
        
        # Test batch read
        read_results = self.io_manager.batch_read_files(list(test_files.keys()))
        
        for file_path, result in read_results.items():
            self.assertTrue(result.success, f"Failed to read {file_path}")


class TestResourceManagement(unittest.TestCase):
    """Test resource management system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.resource_manager = ResourceManager()
        self.memory_monitor = MemoryMonitor()
    
    def test_resource_allocation(self):
        """Test resource allocation and deallocation"""
        component_name = "test_component"
        memory_gb = 0.1  # Small allocation for testing
        
        # Test allocation
        success, message = self.resource_manager.allocate_resources(
            component_name, memory_gb=memory_gb
        )
        
        self.assertTrue(success, f"Resource allocation failed: {message}")
        
        # Test deallocation
        self.resource_manager.deallocate_resources(component_name)
        
        # Verify component is no longer in allocated resources
        self.assertNotIn(component_name, self.resource_manager.allocated_resources)
    
    def test_memory_monitoring(self):
        """Test memory monitoring functionality"""
        current_usage = self.memory_monitor.get_current_usage()
        
        self.assertIsInstance(current_usage, float)
        self.assertGreater(current_usage, 0)
    
    def test_memory_cleanup(self):
        """Test memory cleanup functionality"""
        initial_usage = self.memory_monitor.get_current_usage()
        
        # Force cleanup
        self.resource_manager.cleanup_memory()
        
        # Memory usage should be stable or reduced
        post_cleanup_usage = self.memory_monitor.get_current_usage()
        self.assertIsInstance(post_cleanup_usage, float)


class CoreComponentsTestSuite:
    """Main test suite for core components"""
    
    def __init__(self):
        self.test_classes = [
            TestBaseClasses,
            TestConfigurationSystem,
            TestPathManagement,
            TestUnifiedIoManager,
            TestResourceManagement
        ]
    
    def run_tests(self) -> Dict[str, Any]:
        """Run all core component tests"""
        results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_results": {},
            "summary": {}
        }
        
        total_tests = 0
        passed_tests = 0
        
        for test_class in self.test_classes:
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
            runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
            result = runner.run(suite)
            
            class_name = test_class.__name__
            tests_run = result.testsRun
            failures = len(result.failures)
            errors = len(result.errors)
            success_count = tests_run - failures - errors
            
            results["test_results"][class_name] = {
                "tests_run": tests_run,
                "passed": success_count,
                "failed": failures,
                "errors": errors,
                "success_rate": success_count / tests_run if tests_run > 0 else 0
            }
            
            total_tests += tests_run
            passed_tests += success_count
        
        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "overall_success_rate": passed_tests / total_tests if total_tests > 0 else 0
        }
        
        return results


if __name__ == "__main__":
    # Run tests when executed directly
    test_suite = CoreComponentsTestSuite()
    results = test_suite.run_tests()
    
    print("🧪 Core Components Test Results:")
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Passed: {results['summary']['passed_tests']}")
    print(f"Success Rate: {results['summary']['overall_success_rate']:.1%}")
    
    for class_name, class_results in results["test_results"].items():
        print(f"\n{class_name}:")
        print(f"  Tests: {class_results['tests_run']}")
        print(f"  Passed: {class_results['passed']}")
        print(f"  Success Rate: {class_results['success_rate']:.1%}")
