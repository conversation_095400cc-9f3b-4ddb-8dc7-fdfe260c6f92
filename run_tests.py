#!/usr/bin/env python3
"""
TJA Generator - Consolidated Test Runner

Unified test runner for all phases and components with comprehensive
reporting and validation capabilities.
"""

import sys
import argparse
import time
import json
from pathlib import Path
from typing import Any, Dict, List

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import test suites
from tests.test_core_components import CoreComponentsTestSuite

# Import validation utilities
from src.utils.hardware_monitor import get_system_info
from src.config.unified_config_manager import UnifiedConfigManager
from src.path_management.path_manager import PathManager


class ConsolidatedTestRunner:
    """
    Consolidated test runner for the entire TJA Generator system
    
    Provides comprehensive testing with detailed reporting and
    validation of all system components.
    """
    
    def __init__(self):
        self.config_manager = UnifiedConfigManager()
        self.path_manager = PathManager()
        
        # Initialize test suites
        self.test_suites = {
            "core_components": CoreComponentsTestSuite(),
            # Additional test suites would be added here
        }
        
        # Test results storage
        self.results = {
            "test_session": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "system_info": get_system_info(),
                "test_environment": self._get_test_environment_info()
            },
            "suite_results": {},
            "summary": {}
        }
    
    def _get_test_environment_info(self) -> Dict[str, Any]:
        """Get test environment information"""
        return {
            "workspace_root": str(self.path_manager.workspace_root),
            "python_version": sys.version,
            "platform": sys.platform,
            "available_test_suites": list(self.test_suites.keys())
        }
    
    def run_suite(self, suite_name: str) -> Dict[str, Any]:
        """
        Run a specific test suite
        
        Args:
            suite_name: Name of the test suite to run
            
        Returns:
            Test results dictionary
        """
        if suite_name not in self.test_suites:
            raise ValueError(f"Unknown test suite: {suite_name}")
        
        print(f"\n🧪 Running {suite_name} test suite...")
        
        suite = self.test_suites[suite_name]
        start_time = time.time()
        
        try:
            results = suite.run_tests()
            results["execution_time"] = time.time() - start_time
            results["status"] = "completed"
            
            # Display results
            self._display_suite_results(suite_name, results)
            
            return results
            
        except Exception as e:
            error_result = {
                "status": "failed",
                "error": str(e),
                "execution_time": time.time() - start_time
            }
            
            print(f"❌ Test suite {suite_name} failed: {e}")
            return error_result
    
    def run_all_suites(self) -> Dict[str, Any]:
        """Run all available test suites"""
        print("🎯 Running all test suites...")
        
        for suite_name in self.test_suites.keys():
            self.results["suite_results"][suite_name] = self.run_suite(suite_name)
        
        # Calculate overall summary
        self._calculate_overall_summary()
        
        return self.results
    
    def run_quick_validation(self) -> bool:
        """
        Run quick validation tests for essential functionality
        
        Returns:
            bool: True if all essential tests pass
        """
        print("⚡ Running quick validation tests...")
        
        essential_tests = [
            self._test_system_imports,
            self._test_configuration_loading,
            self._test_path_resolution,
            self._test_basic_functionality
        ]
        
        passed = 0
        total = len(essential_tests)
        
        for test_func in essential_tests:
            try:
                if test_func():
                    passed += 1
                    print(f"  ✅ {test_func.__name__}")
                else:
                    print(f"  ❌ {test_func.__name__}")
            except Exception as e:
                print(f"  💥 {test_func.__name__}: {e}")
        
        success_rate = passed / total
        print(f"\n⚡ Quick validation: {passed}/{total} tests passed ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% pass rate required
    
    def _test_system_imports(self) -> bool:
        """Test that all essential modules can be imported"""
        try:
            from src.config.unified_config_manager import UnifiedConfigManager
            from src.path_management.path_manager import PathManager
            return True
        except ImportError:
            return False
    
    def _test_configuration_loading(self) -> bool:
        """Test configuration system loading"""
        try:
            config = self.config_manager.get_phase_config(1)
            return "hardware" in config and "paths" in config
        except Exception:
            return False
    
    def _test_path_resolution(self) -> bool:
        """Test path resolution functionality"""
        try:
            from src.path_management.path_manager import PathType
            data_path = self.path_manager.get_standardized_path(PathType.DATA_RAW)
            return data_path.is_absolute()
        except Exception:
            return False
    
    def _test_basic_functionality(self) -> bool:
        """Test basic system functionality"""
        try:
            from src.utils.memory_monitor import MemoryMonitor
            monitor = MemoryMonitor()
            usage = monitor.get_current_usage()
            return isinstance(usage, float) and usage > 0
        except Exception:
            return False
    
    def _display_suite_results(self, suite_name: str, results: Dict[str, Any]):
        """Display test suite results"""
        if "summary" in results:
            summary = results["summary"]
            print(f"  📊 {suite_name} Results:")
            print(f"    Total Tests: {summary.get('total_tests', 0)}")
            print(f"    Passed: {summary.get('passed_tests', 0)}")
            print(f"    Success Rate: {summary.get('overall_success_rate', 0):.1%}")
            print(f"    Execution Time: {results.get('execution_time', 0):.2f}s")
    
    def _calculate_overall_summary(self):
        """Calculate overall test summary"""
        total_tests = 0
        passed_tests = 0
        total_execution_time = 0
        
        for suite_name, suite_results in self.results["suite_results"].items():
            if "summary" in suite_results:
                summary = suite_results["summary"]
                total_tests += summary.get("total_tests", 0)
                passed_tests += summary.get("passed_tests", 0)
            
            total_execution_time += suite_results.get("execution_time", 0)
        
        self.results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "overall_success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "total_execution_time": total_execution_time,
            "test_suites_run": len(self.results["suite_results"])
        }
    
    def save_results(self, output_file: str = "test_results.json"):
        """Save test results to file"""
        try:
            output_path = self.path_manager.resolve_path(output_file)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            print(f"📄 Test results saved to: {output_path}")
            
        except Exception as e:
            print(f"⚠️ Failed to save test results: {e}")
    
    def generate_report(self) -> str:
        """Generate human-readable test report"""
        if not self.results["summary"]:
            return "No test results available"
        
        summary = self.results["summary"]
        
        report = f"""
🎯 TJA Generator Test Report
{'=' * 50}

Test Session: {self.results['test_session']['timestamp']}
System: {self.results['test_session']['system_info']['gpu']['name']}
Memory: {self.results['test_session']['system_info']['memory']['total_gb']:.1f}GB

Overall Results:
  Total Tests: {summary['total_tests']}
  Passed: {summary['passed_tests']}
  Success Rate: {summary['overall_success_rate']:.1%}
  Execution Time: {summary['total_execution_time']:.2f}s
  Test Suites: {summary['test_suites_run']}

Status: {'✅ PASSED' if summary['overall_success_rate'] >= 0.8 else '❌ FAILED'}
"""
        
        return report


def create_argument_parser() -> argparse.ArgumentParser:
    """Create argument parser for test runner"""
    parser = argparse.ArgumentParser(
        description="TJA Generator Consolidated Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run all tests
  python run_tests.py --all
  
  # Run specific test suite
  python run_tests.py --suite core_components
  
  # Quick validation only
  python run_tests.py --quick
  
  # Save results to custom file
  python run_tests.py --all --output custom_results.json
        """
    )
    
    parser.add_argument(
        '--all', action='store_true',
        help='Run all test suites'
    )
    parser.add_argument(
        '--suite', type=str,
        help='Run specific test suite'
    )
    parser.add_argument(
        '--quick', action='store_true',
        help='Run quick validation tests only'
    )
    parser.add_argument(
        '--output', type=str, default='test_results.json',
        help='Output file for test results'
    )
    parser.add_argument(
        '--verbose', '-v', action='store_true',
        help='Enable verbose output'
    )
    
    return parser


def main():
    """Main entry point"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    print("🧪 TJA Generator - Consolidated Test Runner")
    print("🔧 Hardware-optimized testing for RTX 3070")
    print(f"📅 {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        runner = ConsolidatedTestRunner()
        
        if args.quick:
            success = runner.run_quick_validation()
            return 0 if success else 1
        
        elif args.suite:
            results = runner.run_suite(args.suite)
            runner.results["suite_results"][args.suite] = results
            runner._calculate_overall_summary()
        
        elif args.all:
            runner.run_all_suites()
        
        else:
            print("❌ Must specify --all, --suite, or --quick")
            return 1
        
        # Save results and generate report
        runner.save_results(args.output)
        
        report = runner.generate_report()
        print(report)
        
        # Return appropriate exit code
        if runner.results["summary"]:
            success_rate = runner.results["summary"]["overall_success_rate"]
            return 0 if success_rate >= 0.8 else 1
        else:
            return 1
        
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
