#!/usr/bin/env python3
"""
Final Code and File Cleanup

Removes remaining redundant files, unused imports, dead code,
and obsolete documentation after the comprehensive refactoring.
"""

import sys
import ast
import re
import shutil
from pathlib import Path
from typing import List, Set, Dict, Any


class FinalCleanup:
    """Comprehensive cleanup of redundant code and files"""
    
    def __init__(self):
        self.workspace_root = Path(".")
        self.cleanup_stats = {
            "files_removed": 0,
            "imports_cleaned": 0,
            "dead_code_removed": 0,
            "comments_cleaned": 0
        }
        
        # Files to remove (remaining redundant files)
        self.files_to_remove = [
            # Old main entry points (if any remain)
            "main_phase1.py",
            "main_phase2.py", 
            "main_phase3.py",
            "main_phase4.py",
            "main_phase5.py",
            "main_phase6.py",
            "production_phase6.py",
            
            # Redundant test files (if any remain)
            "test_refactoring_validation.py",
            
            # Temporary files
            "refactor_src_structure.py",
            "final_cleanup.py",  # This file itself after completion
            
            # Obsolete documentation
            "README_Phase1.md",
            "PRODUCTION_PHASE6_README.md",
            
            # Any remaining summary files
            "COMPREHENSIVE_REFACTORING_SUMMARY.md",
            "ENTERPRISE_ENHANCEMENT_SUMMARY.md",
            "FINAL_PRODUCTION_SUMMARY.md",
        ]
        
        # Directories to clean up
        self.directories_to_clean = [
            "src_backup",  # Backup directory after validation
            "__pycache__",  # Python cache directories
        ]
    
    def remove_redundant_files(self):
        """Remove redundant files that are no longer needed"""
        print("🗑️ Removing redundant files...")
        
        removed_count = 0
        
        for file_path in self.files_to_remove:
            full_path = self.workspace_root / file_path
            
            if full_path.exists():
                try:
                    full_path.unlink()
                    removed_count += 1
                    print(f"   Removed: {file_path}")
                except Exception as e:
                    print(f"   ⚠️ Failed to remove {file_path}: {e}")
        
        self.cleanup_stats["files_removed"] = removed_count
        print(f"   Removed {removed_count} redundant files")
    
    def clean_pycache_directories(self):
        """Remove all __pycache__ directories"""
        print("🧹 Cleaning __pycache__ directories...")
        
        removed_count = 0
        
        for pycache_dir in self.workspace_root.rglob("__pycache__"):
            try:
                shutil.rmtree(pycache_dir)
                removed_count += 1
                print(f"   Removed: {pycache_dir}")
            except Exception as e:
                print(f"   ⚠️ Failed to remove {pycache_dir}: {e}")
        
        print(f"   Removed {removed_count} __pycache__ directories")
    
    def clean_unused_imports(self):
        """Clean unused imports from Python files"""
        print("📦 Cleaning unused imports...")
        
        cleaned_files = 0
        
        # Get all Python files
        python_files = list(self.workspace_root.rglob("*.py"))
        python_files = [f for f in python_files if "__pycache__" not in str(f)]
        
        for py_file in python_files:
            try:
                if self._clean_file_imports(py_file):
                    cleaned_files += 1
                    print(f"   Cleaned imports: {py_file}")
            except Exception as e:
                print(f"   ⚠️ Error cleaning {py_file}: {e}")
        
        self.cleanup_stats["imports_cleaned"] = cleaned_files
        print(f"   Cleaned imports in {cleaned_files} files")
    
    def _clean_file_imports(self, file_path: Path) -> bool:
        """Clean imports in a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Parse the AST to find imports
            try:
                tree = ast.parse(content)
            except SyntaxError:
                return False
            
            # Find all imports
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            imports.append(f"{node.module}.{alias.name}")
            
            # Remove obviously unused imports (basic heuristic)
            lines = content.split('\n')
            cleaned_lines = []
            
            for line in lines:
                # Skip empty import lines or imports that are clearly unused
                if (line.strip().startswith('import ') or 
                    line.strip().startswith('from ')) and self._is_import_unused(line, content):
                    continue
                cleaned_lines.append(line)
            
            cleaned_content = '\n'.join(cleaned_lines)
            
            # Write back if changed
            if cleaned_content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                return True
            
            return False
            
        except Exception:
            return False
    
    def _is_import_unused(self, import_line: str, content: str) -> bool:
        """Basic heuristic to detect unused imports"""
        # Extract imported name
        if 'import ' in import_line:
            if ' as ' in import_line:
                # Handle "import x as y"
                imported_name = import_line.split(' as ')[-1].strip()
            else:
                # Handle "import x" or "from x import y"
                parts = import_line.split()
                if 'from' in parts:
                    imported_name = parts[-1]
                else:
                    imported_name = parts[-1].split('.')[-1]
            
            # Check if the imported name is used elsewhere in the file
            # (excluding the import line itself)
            content_without_import = content.replace(import_line, '')
            
            # Simple check: if the name appears in the content
            if imported_name in content_without_import:
                return False  # Likely used
            else:
                return True   # Likely unused
        
        return False
    
    def remove_dead_code(self):
        """Remove commented-out code blocks"""
        print("💀 Removing dead code and excessive comments...")
        
        cleaned_files = 0
        
        python_files = list(self.workspace_root.rglob("*.py"))
        python_files = [f for f in python_files if "__pycache__" not in str(f)]
        
        for py_file in python_files:
            try:
                if self._clean_dead_code(py_file):
                    cleaned_files += 1
                    print(f"   Cleaned dead code: {py_file}")
            except Exception as e:
                print(f"   ⚠️ Error cleaning {py_file}: {e}")
        
        self.cleanup_stats["dead_code_removed"] = cleaned_files
        print(f"   Cleaned dead code in {cleaned_files} files")
    
    def _clean_dead_code(self, file_path: Path) -> bool:
        """Remove dead code from a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            original_lines = lines[:]
            cleaned_lines = []
            
            in_multiline_comment = False
            consecutive_empty_lines = 0
            
            for line in lines:
                stripped = line.strip()
                
                # Skip excessive empty lines (more than 2 consecutive)
                if not stripped:
                    consecutive_empty_lines += 1
                    if consecutive_empty_lines <= 2:
                        cleaned_lines.append(line)
                    continue
                else:
                    consecutive_empty_lines = 0
                
                # Skip commented-out code (heuristic: lines that look like code but are commented)
                if (stripped.startswith('#') and 
                    len(stripped) > 1 and 
                    not stripped.startswith('# ') and
                    not stripped.startswith('##') and
                    ('=' in stripped or '(' in stripped or 'def ' in stripped or 'class ' in stripped)):
                    continue  # Skip commented-out code
                
                # Skip TODO comments that are too old or generic
                if stripped.startswith('# TODO') and ('fix' in stripped.lower() or 'implement' in stripped.lower()):
                    continue
                
                cleaned_lines.append(line)
            
            # Write back if changed
            if len(cleaned_lines) != len(original_lines):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(cleaned_lines)
                return True
            
            return False
            
        except Exception:
            return False
    
    def consolidate_duplicate_functions(self):
        """Identify and report duplicate functions (for manual review)"""
        print("🔍 Identifying duplicate functions...")
        
        function_signatures = {}
        duplicates = []
        
        python_files = list(self.workspace_root.rglob("*.py"))
        python_files = [f for f in python_files if "__pycache__" not in str(f)]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find function definitions
                function_pattern = r'def\s+(\w+)\s*\([^)]*\):'
                functions = re.findall(function_pattern, content)
                
                for func_name in functions:
                    if func_name in function_signatures:
                        function_signatures[func_name].append(py_file)
                    else:
                        function_signatures[func_name] = [py_file]
                        
            except Exception:
                continue
        
        # Find duplicates
        for func_name, files in function_signatures.items():
            if len(files) > 1:
                duplicates.append((func_name, files))
        
        if duplicates:
            print("   ⚠️ Potential duplicate functions found (manual review needed):")
            for func_name, files in duplicates[:5]:  # Show first 5
                print(f"      {func_name}: {[str(f) for f in files]}")
        else:
            print("   ✅ No obvious duplicate functions found")
    
    def generate_cleanup_report(self) -> Dict[str, Any]:
        """Generate cleanup report"""
        return {
            "cleanup_timestamp": "2025-07-25T20:15:00Z",
            "statistics": self.cleanup_stats,
            "summary": {
                "total_operations": sum(self.cleanup_stats.values()),
                "cleanup_successful": True
            }
        }
    
    def run_final_cleanup(self):
        """Execute complete final cleanup"""
        print("🧹 Starting Final Code and File Cleanup")
        print("=" * 50)
        
        try:
            # Step 1: Remove redundant files
            self.remove_redundant_files()
            
            # Step 2: Clean __pycache__ directories
            self.clean_pycache_directories()
            
            # Step 3: Clean unused imports
            self.clean_unused_imports()
            
            # Step 4: Remove dead code
            self.remove_dead_code()
            
            # Step 5: Identify duplicate functions
            self.consolidate_duplicate_functions()
            
            # Generate report
            report = self.generate_cleanup_report()
            
            print("\n✅ Final cleanup completed successfully!")
            print(f"Files removed: {self.cleanup_stats['files_removed']}")
            print(f"Imports cleaned: {self.cleanup_stats['imports_cleaned']}")
            print(f"Dead code cleaned: {self.cleanup_stats['dead_code_removed']}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Final cleanup failed: {e}")
            return False


def main():
    """Main entry point"""
    cleanup = FinalCleanup()
    success = cleanup.run_final_cleanup()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
