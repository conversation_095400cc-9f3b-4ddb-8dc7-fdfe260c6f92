{"overall_valid": true, "phase_results": {"phase_1": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase1_minimal_test.json", "file_valid": true}, "phase_2": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase2_minimal_test.json", "file_valid": true}, "phase_3": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase3_minimal_test.json", "file_valid": true}, "phase_4": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase4_minimal_test.json", "file_valid": true}, "phase_5": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase5_minimal_test.json", "file_valid": true}, "phase_6": {"overall_valid": true, "validation_time_seconds": 0.0, "structure_validation": {"valid": true, "errors": [], "warnings": [], "missing_fields": []}, "types_validation": {"valid": true, "errors": [], "warnings": []}, "ranges_validation": {"valid": true, "errors": [], "warnings": []}, "summary": {"total_errors": 0, "total_warnings": 0}, "file_path": "test_outputs\\phase6_minimal_test.json", "file_valid": true}}, "summary": {"total_phases": 6, "valid_phases": 6, "total_errors": 0, "total_warnings": 0}}