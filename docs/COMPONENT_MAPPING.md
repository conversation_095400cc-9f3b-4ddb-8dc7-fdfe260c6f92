# TJA Generator - Component Mapping Guide

**Version:** 2.0.0  
**Date:** 2025-07-25  
**Purpose:** Migration guide showing old vs. new component names and locations

---

## 📋 Directory Structure Changes

### **Root Level Changes**

| Old Structure | New Structure | Status | Notes |
|---------------|---------------|--------|-------|
| `main_phase1.py` | `main.py --phase 1` | ✅ REPLACED | Unified entry point |
| `main_phase2.py` | `main.py --phase 2` | ✅ REPLACED | Unified entry point |
| `main_phase3.py` | `main.py --phase 3` | ✅ REPLACED | Unified entry point |
| `main_phase4.py` | `main.py --phase 4` | ✅ REPLACED | Unified entry point |
| `main_phase5.py` | `main.py --phase 5` | ✅ REPLACED | Unified entry point |
| `main_phase6.py` | `main.py --phase 6` | ✅ REPLACED | Unified entry point |
| `test_*.py` (20+ files) | `run_tests.py` | ✅ CONSOLIDATED | Unified test runner |

### **Source Directory Changes**

| Old Path | New Path | Change Type | Migration Required |
|----------|----------|-------------|-------------------|
| `src/audio/` | `src/audio_processing/` | 🔄 CONSOLIDATED | Update imports |
| `src/audio_processing/` | `src/audio_processing/` | ✅ ENHANCED | Files merged from audio/ |
| `src/cleanup/` | `src/system_cleanup/` | 🔄 RENAMED | Update imports |
| `src/io/` | `src/file_io/` | 🔄 RENAMED | Update imports |
| `src/monitoring/` | `src/system_monitoring/` | 🔄 RENAMED | Update imports |
| `src/optimization/` | `src/performance_optimization/` | 🔄 RENAMED | Update imports |
| `src/orchestration/` | `src/system_orchestration/` | 🔄 RENAMED | Update imports |
| `src/paths/` | `src/path_management/` | 🔄 RENAMED | Update imports |
| `src/phase4/` | `src/phases/implementations/phase4/` | 🔄 MOVED | Update imports |
| `src/phase5/` | `src/phases/implementations/phase5/` | 🔄 MOVED | Update imports |
| `src/phase6/` | `src/phases/implementations/phase6/` | 🔄 MOVED | Update imports |
| `src/tja/` | `src/tja_processing/` | 🔄 RENAMED | Update imports |
| `src/validation/` | `src/data_validation/` | 🔄 RENAMED | Update imports |

### **New Directories Added**

| New Directory | Purpose | Contents |
|---------------|---------|----------|
| `src/schemas/` | Standardized data schemas | `output_schemas.py`, `validation_schemas.py` |
| `src/phases/implementations/` | Phase-specific implementations | Consolidated phase4, phase5, phase6 |

---

## 🔧 Import Statement Changes

### **Configuration Management**
```python
# OLD
from src.config.unified_config_manager import UnifiedConfigManager

# NEW (unchanged)
from src.config.unified_config_manager import UnifiedConfigManager
```

### **Path Management**
```python
# OLD
from src.paths.path_manager import PathManager, PathType

# NEW
from src.path_management.path_manager import PathManager, PathType
```

### **File I/O Operations**
```python
# OLD
from src.io.unified_io_manager import UnifiedIoManager

# NEW
from src.file_io.unified_io_manager import UnifiedIoManager
```

### **Audio Processing**
```python
# OLD
from src.audio.unified_audio_processor import UnifiedAudioProcessor
from src.audio_processing.feature_extractor import AudioFeatureExtractor

# NEW
from src.audio_processing.unified_audio_processor import UnifiedAudioProcessor
from src.audio_processing.feature_extractor import AudioFeatureExtractor
```

### **Data Validation**
```python
# OLD
from src.validation.feature_validator import FeatureValidator
from src.validation.alignment_validator import AlignmentValidator

# NEW
from src.data_validation.feature_validator import FeatureValidator
from src.data_validation.alignment_validator import AlignmentValidator
```

### **TJA Processing**
```python
# OLD
from src.tja.unified_tja_processor import UnifiedTjaProcessor

# NEW
from src.tja_processing.unified_tja_processor import UnifiedTjaProcessor
```

### **System Components**
```python
# OLD
from src.monitoring.system_monitor import SystemMonitor
from src.orchestration.system_orchestrator import SystemOrchestrator
from src.optimization.performance_optimizer import PerformanceOptimizer
from src.cleanup.system_cleaner import SystemCleaner

# NEW
from src.system_monitoring.system_monitor import SystemMonitor
from src.system_orchestration.system_orchestrator import SystemOrchestrator
from src.performance_optimization.performance_optimizer import PerformanceOptimizer
from src.system_cleanup.system_cleaner import SystemCleaner
```

### **Phase Controllers**
```python
# OLD
from src.phases.phase1_controller import Phase1Controller

# NEW (unchanged)
from src.phases.phase1_controller import Phase1Controller
```

### **Phase Implementations**
```python
# OLD
from src.phase4.pipeline import TJAGenerationPipeline
from src.phase5.advanced_trainer import AdvancedTJATrainer
from src.phase6.inference_system import TJAInferenceSystem

# NEW
from src.phases.implementations.phase4.pipeline import TJAGenerationPipeline
from src.phases.implementations.phase5.advanced_trainer import AdvancedTJATrainer
from src.phases.implementations.phase6.inference_system import TJAInferenceSystem
```

---

## 📊 Data Schema Changes

### **New Standardized Schemas**

| Schema Type | Location | Purpose |
|-------------|----------|---------|
| `StandardizedOutput` | `src/schemas/output_schemas.py` | Base output structure for all phases |
| `PhaseResult` | `src/schemas/output_schemas.py` | Enhanced result with error tracking |
| `ProcessingMetrics` | `src/schemas/output_schemas.py` | Standardized performance metrics |
| `ValidationMetrics` | `src/schemas/output_schemas.py` | Quality and validation metrics |
| `HardwareMetrics` | `src/schemas/output_schemas.py` | Hardware utilization metrics |

### **Schema Usage Examples**

```python
# Import new schemas
from src.schemas.output_schemas import (
    StandardizedOutput, PhaseResult, ProcessingMetrics,
    ValidationMetrics, HardwareMetrics
)

# Create standardized output
output = StandardizedOutput(
    phase_number=1,
    phase_name="Data Analysis and Preprocessing",
    success=True,
    processing_metrics=ProcessingMetrics(1.5, 100.0),
    validation_metrics=ValidationMetrics(True, 0.95)
)

# Save to file
output.save_to_file("phase1_output.json")
```

---

## 🎯 Command Line Interface Changes

### **Phase Execution**

| Old Command | New Command | Notes |
|-------------|-------------|-------|
| `python main_phase1.py` | `python main.py --phase 1` | Unified entry point |
| `python main_phase2.py --test` | `python main.py --phase 2 --test` | Consistent arguments |
| `python main_phase3.py --config cfg.json` | `python main.py --phase 3 --config cfg.json` | Same functionality |

### **Pipeline Execution**

| Operation | Command | Description |
|-----------|---------|-------------|
| Run Phases 1-3 | `python main.py --pipeline 1 3` | Complete preprocessing pipeline |
| Run Phases 3-5 | `python main.py --pipeline 3 5` | Training pipeline |
| Run single phase | `python main.py --phase 4` | Individual phase execution |

### **Testing Commands**

| Old Command | New Command | Notes |
|-------------|-------------|-------|
| `python test_phase1.py` | `python run_tests.py --suite core_components` | Consolidated testing |
| Multiple test files | `python run_tests.py --all` | Run all tests |
| Quick validation | `python run_tests.py --quick` | System health check |

---

## 🔄 Migration Checklist

### **For Developers**

- [ ] Update all import statements according to mapping table
- [ ] Replace individual phase entry points with unified `main.py`
- [ ] Update test scripts to use `run_tests.py`
- [ ] Migrate to new standardized data schemas
- [ ] Update configuration management calls
- [ ] Test all functionality after migration

### **For Users**

- [ ] Update command line scripts to use new entry point
- [ ] Update any automation scripts with new commands
- [ ] Verify all existing data files are compatible
- [ ] Test phase execution with new interface
- [ ] Update documentation references

### **For System Administrators**

- [ ] Update deployment scripts
- [ ] Modify monitoring configurations
- [ ] Update backup procedures for new directory structure
- [ ] Test system integration after migration
- [ ] Update operational procedures

---

## 🚨 Breaking Changes

### **Removed Components**

| Component | Replacement | Migration Path |
|-----------|-------------|----------------|
| `main_phase*.py` files | `main.py --phase N` | Update scripts and automation |
| Individual test files | `run_tests.py` | Use consolidated test runner |
| `src/audio/` directory | `src/audio_processing/` | Update imports |

### **Changed Interfaces**

| Interface | Change | Impact |
|-----------|--------|--------|
| Phase execution | Unified CLI | Update command line usage |
| Import paths | Directory renames | Update all import statements |
| Test execution | Consolidated runner | Update test automation |

### **Deprecated Features**

| Feature | Status | Replacement |
|---------|--------|-------------|
| Direct phase file execution | ❌ REMOVED | Use `main.py --phase N` |
| Old directory imports | ❌ DEPRECATED | Use new directory names |
| Individual test execution | ❌ REPLACED | Use `run_tests.py` |

---

## ✅ Validation Steps

### **Post-Migration Validation**

1. **Import Validation**
   ```bash
   python run_tests.py --quick
   ```

2. **Phase Execution Validation**
   ```bash
   python main.py --phase 1 --test --count 5
   ```

3. **Schema Validation**
   ```bash
   python generate_test_outputs.py
   ```

4. **System Integration Validation**
   ```bash
   python run_tests.py --all
   ```

### **Success Criteria**

- ✅ All imports resolve correctly
- ✅ All phases execute without errors
- ✅ Data schemas validate successfully
- ✅ Test suite passes completely
- ✅ Performance characteristics maintained

---

**This mapping guide ensures smooth migration from TJA Generator v1.0 to v2.0 while preserving all functionality and improving system architecture.** 🎵🥁
