# TJA Generator - 統一文檔索引

## 文檔結構說明

本項目的文檔分為兩個層次：
- **概覽文檔** (`docs/phase_*.md`): 簡潔的階段概述，包含實施狀態和關鍵指標
- **詳細規範** (`docs/development_phases/Phase_*_RFP.md`): 完整的技術規範和實施要求

## 階段文檔對照表

| 階段 | 概覽文檔 | 詳細規範 | 實施狀態 |
|------|----------|----------|----------|
| Phase 1 | [phase_1.md](phase_1.md) | [Phase_1_RFP.md](development_phases/Phase_1_Data_Analysis_and_Preprocessing_RFP.md) | ✅ 完成 |
| Phase 2 | [phase_2.md](phase_2.md) | [Phase_2_RFP.md](development_phases/Phase_2_Audio_Feature_Extraction_RFP.md) | 🔄 準備中 |
| Phase 3 | [phase_3.md](phase_3.md) | [Phase_3_RFP.md](development_phases/Phase_3_TJA_Sequence_Processing_RFP.md) | 🔄 準備中 |
| Phase 4 | [phase_4.md](phase_4.md) | [Phase_4_RFP.md](development_phases/Phase_4_Neural_Network_Architecture_RFP.md) | 🔄 準備中 |
| Phase 5 | [phase_5.md](phase_5.md) | [Phase_5_RFP.md](development_phases/Phase_5_Model_Training_Optimization_RFP.md) | 🔄 準備中 |
| Phase 6 | [phase_6.md](phase_6.md) | [Phase_6_RFP.md](development_phases/Phase_6_Inference_Pipeline_Validation_RFP.md) | 🔄 準備中 |

## 硬體環境規範

所有階段都針對以下硬體環境進行優化：

```python
HARDWARE_SPECS = {
    "gpu": {
        "model": "NVIDIA GeForce RTX 3070",
        "vram_gb": 8.0,
        "cuda_version": "12.1",
        "pytorch_version": "2.5.1+cu121",
        "memory_constraint": "6.8GB usable (safety margin)"
    },
    "cpu": {
        "physical_cores": 8,
        "logical_cores": 16,
        "recommended_workers": 12,
        "optimization_target": "parallel_processing"
    },
    "memory": {
        "total_ram_gb": 31.8,
        "available_ram_gb": 28.0,
        "recommended_cache_gb": 16,
        "processing_buffer_gb": 8
    }
}
```

## 數據流程概覽

```
Phase 1: Raw TJA/Audio → Processed Data (data/processed/)
    ↓
Phase 2: Audio Features → Feature Tensors [T, 201]
    ↓
Phase 3: TJA Sequences → Temporal Encoding
    ↓
Phase 4: Neural Network → Model Architecture
    ↓
Phase 5: Model Training → Optimized Model
    ↓
Phase 6: Inference Pipeline → Production System
```

## 輸出結構標準

### Phase 1 輸出結構
```
data/processed/
├── catalog.json                    # 主要輸出目錄
├── reference_metadata/             # 參考數據（非訓練用）
│   ├── song_info.json
│   ├── genre_mappings.json
│   ├── creator_info.json
│   └── audio_pairing_report.json
├── notation_data/                  # 訓練數據
│   ├── pure_sequences/             # 原始音符序列
│   ├── timing_structures/          # BPM、時間、小節數據
│   ├── pattern_features/           # 提取的節奏模式
│   └── difficulty_progressions/    # 跨難度分析
└── validation_reports/             # 質量評估報告
```

### Phase 2 輸出結構
```
data/processed/audio_features/
├── spectral_features/              # 頻譜特徵
├── rhythmic_features/              # 節奏特徵
├── temporal_features/              # 時間特徵
├── combined_features/              # 組合特徵
└── alignment_data/                 # 對齊數據
```

## 關鍵性能指標

| 階段 | 處理速度 | 記憶體使用 | 成功率 | 硬體評分 |
|------|----------|------------|--------|----------|
| Phase 1 | 420+ files/sec | 46KB/file | 100% | 100/100 |
| Phase 2 | Real-time | <4GB VRAM | TBD | TBD |
| Phase 3 | TBD | TBD | TBD | TBD |
| Phase 4 | TBD | TBD | TBD | TBD |
| Phase 5 | TBD | TBD | TBD | TBD |
| Phase 6 | TBD | TBD | TBD | TBD |

## 使用指南

### 快速開始
1. 閱讀對應階段的概覽文檔了解基本信息
2. 查看詳細規範文檔了解技術要求
3. 檢查實施狀態確認可用性
4. 參考性能指標設定期望

### 開發者指南
1. **實施新階段**: 先閱讀RFP規範文檔
2. **調試問題**: 檢查概覽文檔中的關鍵文件
3. **性能優化**: 參考硬體環境規範
4. **數據流程**: 確保輸出符合下一階段的輸入要求

## 參考資料

### 技術規範
- [TJA格式規範](references/tja_spec/)
- [TJA解析器參考](references/tja_parser/)

### 系統架構
- [ARCHITECTURE_V2.md](../ARCHITECTURE_V2.md) - 系統架構文檔
- [REFACTORING_COMPLETION_REPORT.md](../REFACTORING_COMPLETION_REPORT.md) - 重構完成報告

### 實施狀態
- [README.md](../README.md) - 項目概述
- [main.py](../main.py) - 統一入口點

## 更新日誌

- **2025-07-25**: 創建統一文檔索引
- **2025-07-25**: 完成Phase 1重構和文檔合規性
- **2025-07-25**: 建立標準化輸出結構

## 維護說明

本文檔應在以下情況下更新：
1. 新階段實施完成時
2. 性能指標發生變化時
3. 硬體環境升級時
4. 數據流程結構改變時
