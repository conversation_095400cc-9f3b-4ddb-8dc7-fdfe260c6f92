# TJA Generator v2.0 - Unified Architecture Documentation

**Version:** 2.0.0  
**Date:** 2025-07-25  
**Status:** Production Ready

---

## 🏗️ System Overview

The TJA Generator v2.0 features a completely refactored architecture with unified entry points, standardized data schemas, and optimized directory structure following enterprise-grade standards.

### **Key Architectural Improvements**

- ✅ **Unified Entry Point**: Single `main.py` for all phase operations
- ✅ **Standardized Controllers**: Consistent phase controller pattern
- ✅ **Optimized Data Schemas**: Standardized inter-phase communication
- ✅ **Clean Directory Structure**: Logical organization with clear naming
- ✅ **Enterprise Standards**: SOLID principles and comprehensive error handling

---

## 📁 Directory Structure

### **Root Level**
```
TJAGenerator/
├── main.py                    # Unified entry point for all phases
├── run_tests.py              # Consolidated test runner
├── generate_test_outputs.py  # Minimal test output generation
├── cleanup_system.py         # System cleanup utility
├── src/                      # Source code (refactored structure)
├── tests/                    # Consolidated test suite
├── docs/                     # Documentation
├── data/                     # Data directories
├── outputs/                  # Phase outputs
└── logs/                     # System logs
```

### **Source Code Structure (src/)**
```
src/
├── audio_processing/         # Audio processing and feature extraction
│   ├── feature_extractor.py
│   ├── gpu_optimizer.py
│   ├── rhythmic_processor.py
│   ├── spectral_processor.py
│   ├── temporal_aligner.py
│   └── unified_audio_processor.py
├── config/                   # Configuration management
│   └── unified_config_manager.py
├── data_validation/          # Data validation and quality assurance
│   ├── alignment_validator.py
│   ├── feature_validator.py
│   └── system_validator.py
├── file_io/                  # File input/output operations
│   └── unified_io_manager.py
├── model/                    # Neural network models
│   ├── attention_modules.py
│   ├── audio_encoder.py
│   ├── loss_functions.py
│   ├── pattern_library.py
│   ├── sequence_decoder.py
│   └── tja_generator.py
├── parsing/                  # TJA file parsing
│   ├── custom_tja_parser.py
│   └── tja_format_validator.py
├── path_management/          # Path handling and resolution
│   └── path_manager.py
├── phases/                   # Phase controllers and implementations
│   ├── base_phase_controller.py
│   ├── phase1_controller.py
│   ├── phase2_controller.py
│   ├── phase3_controller.py
│   ├── phase4_controller.py
│   ├── phase5_controller.py
│   ├── phase6_controller.py
│   └── implementations/      # Phase-specific implementations
│       ├── phase4/
│       ├── phase5/
│       └── phase6/
├── pipeline/                 # Processing pipelines
│   ├── audio_analyzer.py
│   └── batch_processor.py
├── preprocessing/            # Data preprocessing
│   ├── data_analyzer.py
│   ├── metadata_separator.py
│   ├── notation_validator.py
│   └── tja_processor.py
├── schemas/                  # Standardized data schemas
│   ├── output_schemas.py
│   └── validation_schemas.py
├── system_cleanup/           # System maintenance
│   └── system_cleaner.py
├── system_monitoring/        # System monitoring
│   └── system_monitor.py
├── system_orchestration/     # System orchestration
│   └── system_orchestrator.py
├── tja_processing/           # TJA-specific processing
│   └── unified_tja_processor.py
├── training/                 # Model training
│   ├── data_loader.py
│   ├── metrics.py
│   ├── optimizer.py
│   └── trainer.py
└── utils/                    # Utility functions
    ├── base_classes.py
    ├── encoding_detector.py
    ├── hardware_monitor.py
    ├── memory_monitor.py
    └── resource_manager.py
```

---

## 🎯 Phase Architecture

### **Unified Phase Controller Pattern**

All phases now follow a consistent controller pattern inheriting from `BasePhaseController`:

```python
class BasePhaseController(BaseProcessor):
    """Abstract base class for all phase controllers"""
    
    def __init__(self, phase_number: int, phase_name: str, ...):
        # Standardized initialization
    
    def execute(self, args: argparse.Namespace) -> bool:
        # Unified execution interface
    
    def _validate_prerequisites(self, args) -> bool:
        # Phase-specific prerequisite validation
    
    def _prepare_execution_params(self, args) -> Dict[str, Any]:
        # Parameter preparation
    
    def _execute_phase(self, params) -> bool:
        # Phase-specific logic implementation
```

### **Phase Descriptions**

| Phase | Name | Controller | Purpose |
|-------|------|------------|---------|
| 1 | Data Analysis and Preprocessing | `Phase1Controller` | Process ~2,800 TJA files with metadata separation |
| 2 | Audio Feature Extraction | `Phase2Controller` | Extract [T, 201] feature tensors with temporal alignment |
| 3 | Model Training and Generation | `Phase3Controller` | Train transformer-based TJA generation model |
| 4 | Integration and Deployment | `Phase4Controller` | Complete system integration with CLI/API |
| 5 | Training Optimization | `Phase5Controller` | Advanced training with hyperparameter optimization |
| 6 | Inference and Validation | `Phase6Controller` | Production inference pipeline with validation |

---

## 📊 Standardized Data Schemas

### **Output Schema Structure**

All phases now use standardized output schemas for consistent inter-phase communication:

```python
@dataclass
class StandardizedOutput:
    phase_number: int
    phase_name: str
    success: bool
    timestamp: str
    version: str = "2.0.0"
    
    # Core metrics
    processing_metrics: ProcessingMetrics
    validation_metrics: ValidationMetrics
    hardware_metrics: HardwareMetrics
    
    # Phase-specific data
    data: Dict[str, Any]
    
    # Inter-phase communication
    next_phase_ready: bool
    handoff_data: Dict[str, Any]
```

### **Schema Validation**

Comprehensive validation framework ensures data integrity:

- **Structure Validation**: Required fields and sections
- **Type Validation**: Data type consistency
- **Range Validation**: Value range constraints
- **Inter-phase Validation**: Handoff data compatibility

---

## 🚀 Usage Examples

### **Single Phase Execution**
```bash
# Run Phase 1 with test data
python main.py --phase 1 --test --count 50

# Run Phase 2 with custom catalog
python main.py --phase 2 --catalog custom_catalog.json

# Run Phase 6 inference
python main.py --phase 6 --mode inference --input-audio song.wav
```

### **Pipeline Execution**
```bash
# Run complete pipeline (Phases 1-3)
python main.py --pipeline 1 3

# Run training pipeline (Phases 3-5)
python main.py --pipeline 3 5 --config training_config.json
```

### **Testing and Validation**
```bash
# Run quick system validation
python run_tests.py --quick

# Run comprehensive test suite
python run_tests.py --all

# Generate minimal test outputs
python generate_test_outputs.py
```

---

## 🔧 Configuration Management

### **Unified Configuration System**

The `UnifiedConfigManager` provides centralized configuration for all phases:

```python
config_manager = UnifiedConfigManager()

# Get phase-specific configuration
phase1_config = config_manager.get_phase_config(1)

# Get hardware configuration
hardware_config = config_manager.hardware_config

# Get path configuration
path_config = config_manager.path_config
```

### **Hardware Optimization**

Automatic hardware detection and optimization for RTX 3070:

- **Memory Management**: Dynamic allocation with 70-80% utilization targets
- **Batch Sizing**: Optimal batch sizes for GPU memory
- **Parallel Processing**: CPU core utilization optimization
- **Resource Monitoring**: Real-time resource usage tracking

---

## 📈 Performance Characteristics

### **System Performance Targets**

| Metric | Target | Current |
|--------|--------|---------|
| Startup Time | <5 seconds | ✅ Achieved |
| Memory Usage | <70% available RAM | ✅ Optimized |
| GPU Utilization | 70-80% during processing | ✅ Optimized |
| Processing Speed | Maintain throughput | ✅ Preserved |

### **Scalability Features**

- **Dynamic Resource Allocation**: Adapts to available hardware
- **Batch Processing**: Configurable batch sizes for memory optimization
- **Parallel Execution**: Multi-core CPU utilization
- **Memory Cleanup**: Automatic resource deallocation

---

## 🛠️ Development Guidelines

### **Adding New Phases**

1. Create phase controller inheriting from `BasePhaseController`
2. Implement required abstract methods
3. Add phase to unified entry point
4. Create standardized output schema
5. Add comprehensive tests

### **Extending Functionality**

1. Follow SOLID principles
2. Use standardized data schemas
3. Implement proper error handling
4. Add comprehensive logging
5. Include validation and tests

### **Code Standards**

- **Naming**: snake_case for files/functions, PascalCase for classes
- **Documentation**: Comprehensive docstrings and type hints
- **Error Handling**: Enterprise-grade exception handling
- **Testing**: Unit tests for all new functionality
- **Validation**: Schema validation for all data structures

---

## 📚 Migration Guide

### **From v1.0 to v2.0**

| v1.0 Component | v2.0 Component | Migration Notes |
|----------------|----------------|-----------------|
| `main_phase1.py` | `python main.py --phase 1` | Use unified entry point |
| `src/paths/` | `src/path_management/` | Update import statements |
| `src/io/` | `src/file_io/` | Update import statements |
| `src/validation/` | `src/data_validation/` | Update import statements |
| Individual test files | `run_tests.py` | Use consolidated test runner |

### **Breaking Changes**

- **Entry Points**: All `main_phase*.py` files removed
- **Directory Structure**: Several directories renamed
- **Import Paths**: Update all import statements
- **Configuration**: Use `UnifiedConfigManager` for all config

### **Backward Compatibility**

- **Data Formats**: All existing data formats supported
- **Functionality**: All original features preserved
- **Hardware Optimization**: RTX 3070 optimizations maintained
- **Performance**: Processing speed and quality maintained

---

**The TJA Generator v2.0 represents a complete architectural evolution while maintaining full functionality and performance characteristics of the original system.** 🎵🥁
