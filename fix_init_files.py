#!/usr/bin/env python3
"""
Fix broken __init__.py files after import cleanup
"""

import re
from pathlib import Path


def fix_init_file(file_path: Path):
    """Fix a broken __init__.py file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the file has broken import statements
        lines = content.split('\n')
        fixed_lines = []
        in_import_block = False
        import_items = []
        
        for line in lines:
            stripped = line.strip()
            
            # Check for broken import pattern (indented import items without from statement)
            if (stripped and 
                not stripped.startswith('#') and 
                not stripped.startswith('"""') and
                not stripped.startswith("'''") and
                not stripped.startswith('from ') and
                not stripped.startswith('import ') and
                not stripped.startswith('__all__') and
                not stripped.startswith(')') and
                ',' in stripped and
                (stripped.endswith(',') or stripped.endswith(')'))):
                
                # This looks like a broken import item
                if not in_import_block:
                    # Start of broken import block - need to add 'from' statement
                    module_name = file_path.parent.name
                    if module_name == 'src':
                        module_name = file_path.stem
                    
                    # Try to guess the module name from the file structure
                    if file_path.parent.name in ['config', 'path_management', 'file_io', 'data_validation']:
                        if file_path.parent.name == 'config':
                            fixed_lines.append('from .unified_config_manager import (')
                        elif file_path.parent.name == 'path_management':
                            fixed_lines.append('from .path_manager import (')
                        elif file_path.parent.name == 'file_io':
                            fixed_lines.append('from .unified_io_manager import (')
                        elif file_path.parent.name == 'data_validation':
                            fixed_lines.append('from .feature_validator import FeatureValidator')
                            fixed_lines.append('from .alignment_validator import AlignmentValidator')
                            fixed_lines.append('from .system_validator import (')
                    else:
                        fixed_lines.append(f'from .{module_name} import (')
                    
                    in_import_block = True
                
                # Add the import item
                fixed_lines.append(f'    {stripped}')
                
            elif stripped == ')':
                if in_import_block:
                    fixed_lines.append(')')
                    in_import_block = False
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        # Write back the fixed content
        fixed_content = '\n'.join(fixed_lines)
        
        if fixed_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False


def main():
    """Fix all broken __init__.py files"""
    print("🔧 Fixing broken __init__.py files...")
    
    src_dir = Path("src")
    fixed_count = 0
    
    for init_file in src_dir.rglob("__init__.py"):
        if fix_init_file(init_file):
            fixed_count += 1
    
    print(f"Fixed {fixed_count} __init__.py files")


if __name__ == "__main__":
    main()
