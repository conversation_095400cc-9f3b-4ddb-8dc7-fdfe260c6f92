#!/usr/bin/env python3
"""
Source Directory Structure Refactoring

Refactors the src/ directory to follow consistent naming conventions
and logical organization with proper import updates.
"""

import sys
import shutil
import re
from pathlib import Path
from typing import Dict, List, Tuple

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


class SourceDirectoryRefactor:
    """Handles systematic refactoring of source directory structure"""
    
    def __init__(self):
        self.src_root = Path("src")
        self.backup_root = Path("src_backup")
        
        # Define refactoring mappings: old_path -> new_path
        self.directory_mappings = {
            # Consolidate audio processing
            "audio": "audio_processing",  # Merge into audio_processing
            
            # Rename poorly named directories
            "tja": "tja_processing",
            "io": "file_io",
            "paths": "path_management",
            
            # Consolidate phase-specific directories under phases
            "phase4": "phases/implementations/phase4",
            "phase5": "phases/implementations/phase5", 
            "phase6": "phases/implementations/phase6",
            
            # Rename for clarity
            "monitoring": "system_monitoring",
            "orchestration": "system_orchestration",
            "optimization": "performance_optimization",
            "validation": "data_validation",
            "cleanup": "system_cleanup"
        }
        
        # Track import updates needed
        self.import_updates = []
        
        # Files that need import updates
        self.files_to_update = []
    
    def create_backup(self):
        """Create backup of current src directory"""
        if self.backup_root.exists():
            shutil.rmtree(self.backup_root)
        
        shutil.copytree(self.src_root, self.backup_root)
        print(f"✅ Created backup: {self.backup_root}")
    
    def analyze_current_structure(self):
        """Analyze current directory structure and plan changes"""
        print("📋 Current Directory Structure Analysis:")
        print("=" * 50)
        
        current_dirs = []
        for item in self.src_root.iterdir():
            if item.is_dir() and not item.name.startswith('__'):
                current_dirs.append(item.name)
        
        print("Current directories:")
        for dir_name in sorted(current_dirs):
            new_name = self.directory_mappings.get(dir_name, dir_name)
            status = "→ RENAME" if new_name != dir_name else "✓ KEEP"
            print(f"  {dir_name:<25} {status:<10} {new_name if new_name != dir_name else ''}")
        
        print(f"\nTotal directories: {len(current_dirs)}")
        print(f"Directories to rename: {len([d for d in current_dirs if d in self.directory_mappings])}")
    
    def consolidate_audio_directories(self):
        """Consolidate audio and audio_processing directories"""
        audio_dir = self.src_root / "audio"
        audio_processing_dir = self.src_root / "audio_processing"
        
        if audio_dir.exists() and audio_processing_dir.exists():
            print("🔄 Consolidating audio directories...")
            
            # Move files from audio/ to audio_processing/
            for file_path in audio_dir.glob("*.py"):
                if file_path.name != "__init__.py":
                    target_path = audio_processing_dir / file_path.name
                    if not target_path.exists():
                        shutil.move(str(file_path), str(target_path))
                        print(f"   Moved: {file_path.name} → audio_processing/")
            
            # Update __init__.py in audio_processing
            self._update_audio_processing_init()
            
            # Remove empty audio directory
            if not any(audio_dir.glob("*.py")):
                shutil.rmtree(audio_dir)
                print("   Removed empty audio/ directory")
    
    def _update_audio_processing_init(self):
        """Update audio_processing __init__.py to include moved modules"""
        init_file = self.src_root / "audio_processing" / "__init__.py"
        
        if init_file.exists():
            # Read current content
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add import for unified_audio_processor if not present
            if "unified_audio_processor" not in content:
                # Add import line
                import_line = "from .unified_audio_processor import UnifiedAudioProcessor\n"
                
                # Find the __all__ section and update it
                if "__all__" in content:
                    content = re.sub(
                        r"(__all__ = \[)(.*?)(\])",
                        r"\1\2    'UnifiedAudioProcessor',\n\3",
                        content,
                        flags=re.DOTALL
                    )
                else:
                    content += "\n__all__ = ['UnifiedAudioProcessor']\n"
                
                # Add import at the top
                lines = content.split('\n')
                import_inserted = False
                for i, line in enumerate(lines):
                    if line.startswith('from .') and not import_inserted:
                        lines.insert(i, import_line.strip())
                        import_inserted = True
                        break
                
                if not import_inserted:
                    lines.insert(-1, import_line.strip())
                
                content = '\n'.join(lines)
                
                # Write updated content
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("   Updated audio_processing/__init__.py")
    
    def rename_directories(self):
        """Rename directories according to mapping"""
        print("\n🔄 Renaming directories...")
        
        for old_name, new_name in self.directory_mappings.items():
            old_path = self.src_root / old_name
            
            # Skip if already processed (like audio -> audio_processing consolidation)
            if old_name == "audio":
                continue
                
            if old_path.exists():
                # Handle nested paths (like phases/implementations/phase4)
                if "/" in new_name:
                    new_path = self.src_root / new_name
                    new_path.parent.mkdir(parents=True, exist_ok=True)
                else:
                    new_path = self.src_root / new_name
                
                # Move directory
                if not new_path.exists():
                    shutil.move(str(old_path), str(new_path))
                    print(f"   {old_name} → {new_name}")
                    
                    # Track import update needed
                    self.import_updates.append((old_name, new_name))
    
    def find_files_with_imports(self):
        """Find all Python files that might need import updates"""
        print("\n🔍 Finding files with imports to update...")
        
        python_files = []
        
        # Search in src directory
        for py_file in self.src_root.rglob("*.py"):
            if "__pycache__" not in str(py_file):
                python_files.append(py_file)
        
        # Search in root directory for main files
        for py_file in Path(".").glob("*.py"):
            python_files.append(py_file)
        
        # Search in tests directory
        tests_dir = Path("tests")
        if tests_dir.exists():
            for py_file in tests_dir.rglob("*.py"):
                python_files.append(py_file)
        
        self.files_to_update = python_files
        print(f"   Found {len(python_files)} Python files to check")
    
    def update_imports(self):
        """Update import statements in all Python files"""
        print("\n📝 Updating import statements...")
        
        # Create import mapping
        import_mappings = {}
        for old_name, new_name in self.import_updates:
            # Handle nested paths
            if "/" in new_name:
                new_import_path = new_name.replace("/", ".")
            else:
                new_import_path = new_name
            
            import_mappings[f"src.{old_name}"] = f"src.{new_import_path}"
            import_mappings[f"from src.{old_name}"] = f"from src.{new_import_path}"
            import_mappings[f"import src.{old_name}"] = f"import src.{new_import_path}"
        
        updated_files = 0
        
        for file_path in self.files_to_update:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Apply import mappings
                for old_import, new_import in import_mappings.items():
                    content = content.replace(old_import, new_import)
                
                # Write back if changed
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    updated_files += 1
                    print(f"   Updated imports in: {file_path}")
                    
            except Exception as e:
                print(f"   ⚠️ Error updating {file_path}: {e}")
        
        print(f"   Updated imports in {updated_files} files")
    
    def create_new_init_files(self):
        """Create __init__.py files for new directories"""
        print("\n📄 Creating __init__.py files for new directories...")
        
        # Find directories that need __init__.py files
        for dir_path in self.src_root.rglob("*"):
            if (dir_path.is_dir() and 
                not dir_path.name.startswith('__') and 
                not (dir_path / "__init__.py").exists()):
                
                # Create basic __init__.py
                init_file = dir_path / "__init__.py"
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(f'"""\n{dir_path.name.replace("_", " ").title()} Module\n"""\n')
                
                print(f"   Created: {init_file}")
    
    def validate_refactoring(self):
        """Validate the refactoring was successful"""
        print("\n✅ Validating refactoring...")
        
        # Check that old directories are gone (except those that should remain)
        issues = []
        
        for old_name in self.directory_mappings.keys():
            if old_name == "audio":  # This was consolidated, not renamed
                continue
                
            old_path = self.src_root / old_name
            if old_path.exists():
                issues.append(f"Old directory still exists: {old_name}")
        
        # Check that new directories exist
        for old_name, new_name in self.directory_mappings.items():
            if old_name == "audio":
                continue
                
            new_path = self.src_root / new_name
            if not new_path.exists():
                issues.append(f"New directory not found: {new_name}")
        
        if issues:
            print("   ⚠️ Issues found:")
            for issue in issues:
                print(f"      {issue}")
            return False
        else:
            print("   ✅ All validations passed")
            return True
    
    def run_refactoring(self):
        """Execute the complete refactoring process"""
        print("🚀 Starting Source Directory Structure Refactoring")
        print("=" * 60)
        
        try:
            # Step 1: Analyze current structure
            self.analyze_current_structure()
            
            # Step 2: Create backup
            self.create_backup()
            
            # Step 3: Consolidate audio directories
            self.consolidate_audio_directories()
            
            # Step 4: Rename directories
            self.rename_directories()
            
            # Step 5: Find files that need import updates
            self.find_files_with_imports()
            
            # Step 6: Update imports
            self.update_imports()
            
            # Step 7: Create missing __init__.py files
            self.create_new_init_files()
            
            # Step 8: Validate refactoring
            success = self.validate_refactoring()
            
            if success:
                print("\n🎉 Source directory refactoring completed successfully!")
                print(f"📁 Backup available at: {self.backup_root}")
                return True
            else:
                print("\n❌ Refactoring completed with issues")
                return False
                
        except Exception as e:
            print(f"\n💥 Refactoring failed: {e}")
            return False


def main():
    """Main entry point"""
    refactor = SourceDirectoryRefactor()
    success = refactor.run_refactoring()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
