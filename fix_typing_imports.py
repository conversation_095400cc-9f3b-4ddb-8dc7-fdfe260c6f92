#!/usr/bin/env python3
"""
Fix missing typing imports across all Python files
"""

import re
from pathlib import Path
from typing import Set


def analyze_typing_needs(content: str) -> Set[str]:
    """Analyze what typing imports are needed"""
    needed_types = set()
    
    # Common typing patterns
    patterns = {
        'Dict': r'\bDict\[',
        'List': r'\bList\[',
        'Optional': r'\bOptional\[',
        'Union': r'\bUnion\[',
        'Any': r'\bAny\b',
        'Tuple': r'\bTuple\[',
        'Callable': r'\bCallable\[',
        'Iterator': r'\bIterator\[',
        'Generator': r'\bGenerator\[',
    }
    
    for type_name, pattern in patterns.items():
        if re.search(pattern, content):
            needed_types.add(type_name)
    
    # Also check for type hints in function signatures
    if re.search(r':\s*(Dict|List|Optional|Union|Any|Tuple|Callable)', content):
        for match in re.finditer(r':\s*(Dict|List|Optional|Union|Any|Tuple|Callable)', content):
            needed_types.add(match.group(1))
    
    return needed_types


def fix_typing_imports(file_path: Path) -> bool:
    """Fix typing imports in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Skip if file doesn't need typing
        needed_types = analyze_typing_needs(content)
        if not needed_types:
            return False
        
        # Check if typing import already exists
        has_typing_import = re.search(r'^from typing import', content, re.MULTILINE)
        
        if has_typing_import:
            # Update existing import
            def update_import(match):
                existing_imports = match.group(1)
                # Parse existing imports
                existing_set = set()
                for imp in existing_imports.split(','):
                    existing_set.add(imp.strip())
                
                # Combine with needed types
                all_types = existing_set.union(needed_types)
                
                # Format nicely
                if len(all_types) <= 3:
                    return f"from typing import {', '.join(sorted(all_types))}"
                else:
                    formatted = "from typing import (\n    " + ",\n    ".join(sorted(all_types)) + "\n)"
                    return formatted
            
            content = re.sub(r'^from typing import ([^(\n]+)', update_import, content, flags=re.MULTILINE)
            content = re.sub(r'^from typing import \(\s*([^)]+)\s*\)', 
                           lambda m: update_import(type('obj', (object,), {'group': lambda x: m.group(1).replace('\n', '').replace(' ', '')})()),
                           content, flags=re.MULTILINE | re.DOTALL)
        else:
            # Add new typing import
            lines = content.split('\n')
            
            # Find the right place to insert (after other imports)
            insert_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_index = i + 1
                elif line.strip() and not line.strip().startswith('#') and not line.strip().startswith('"""'):
                    break
            
            # Format import
            if len(needed_types) <= 3:
                import_line = f"from typing import {', '.join(sorted(needed_types))}"
            else:
                import_line = "from typing import (\n    " + ",\n    ".join(sorted(needed_types)) + "\n)"
            
            lines.insert(insert_index, import_line)
            content = '\n'.join(lines)
        
        # Write back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False


def main():
    """Fix typing imports in all Python files"""
    print("🔧 Fixing typing imports across all Python files...")
    
    src_dir = Path("src")
    fixed_count = 0
    
    # Get all Python files
    python_files = list(src_dir.rglob("*.py"))
    
    # Also check root level files
    for py_file in Path(".").glob("*.py"):
        if py_file.name not in ["fix_typing_imports.py", "fix_init_files.py"]:
            python_files.append(py_file)
    
    for py_file in python_files:
        if "__pycache__" not in str(py_file):
            if fix_typing_imports(py_file):
                print(f"   Fixed: {py_file}")
                fixed_count += 1
    
    print(f"Fixed typing imports in {fixed_count} files")


if __name__ == "__main__":
    main()
