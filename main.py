#!/usr/bin/env python3
"""
TJA Generator - Unified Entry Point
Hardware-optimized for RTX 3070 with 32GB RAM

Consolidated entry point for all phases of the TJA generation system.
Implements enterprise-grade error handling, resource management, and
comprehensive logging with SOLID principles.
"""

import sys
import argparse
import logging
import time
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import unified components
try:
    from src.shared.config.unified_config_manager import UnifiedConfigManager
    from src.shared.utils.resource_manager import ResourceManager, cleanup_global_resources
    from src.shared.utils.memory_monitor import MemoryMonitor
    from src.shared.utils.hardware_monitor import get_system_info
    
    # Import phase controllers
    from src.phase_1.controller import Phase1Controller
    from src.phase_2.controller import Phase2Controller
    from src.phase_3.controller import Phase3Controller
    from src.phase_4.controller import Phase4Controller
    from src.phase_5.controller import Phase5Controller
    from src.phase_6.controller import Phase6Controller
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are available")
    sys.exit(1)


class TJAGeneratorMain:
    """
    Unified TJA Generator main controller
    
    Provides centralized entry point for all phases with consistent
    initialization, resource management, and error handling.
    """
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.config_manager = UnifiedConfigManager()
        self.resource_manager = ResourceManager()
        self.memory_monitor = MemoryMonitor()
        
        # Initialize phase controllers
        self.phase_controllers = {
            1: Phase1Controller,
            2: Phase2Controller,
            3: Phase3Controller,
            4: Phase4Controller,
            5: Phase5Controller,
            6: Phase6Controller
        }
        
        self.logger.info("TJA Generator unified system initialized")
    
    def _setup_logging(self):
        """Setup unified logging system"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('tja_generator.log')
            ]
        )
        return logging.getLogger(__name__)
    
    def run_phase(self, phase_number: int, args: argparse.Namespace) -> bool:
        """Run a single phase"""
        if phase_number not in self.phase_controllers:
            self.logger.error(f"Invalid phase number: {phase_number}")
            return False
        
        start_time = time.time()
        
        try:
            # Display system information
            self._display_system_info(phase_number)
            
            # Initialize phase controller
            controller_class = self.phase_controllers[phase_number]
            controller = controller_class(
                config_manager=self.config_manager,
                resource_manager=self.resource_manager
            )
            
            # Execute phase
            self.logger.info(f"Starting Phase {phase_number} execution...")
            success = controller.execute(args)
            
            execution_time = time.time() - start_time
            
            if success:
                self.logger.info(f"Phase {phase_number} completed successfully in {execution_time:.2f}s")
            else:
                self.logger.error(f"Phase {phase_number} failed after {execution_time:.2f}s")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Phase {phase_number} execution failed: {e}")
            return False
    
    def run_pipeline(self, start_phase: int, end_phase: int, args: argparse.Namespace) -> bool:
        """Run a pipeline of phases"""
        self.logger.info(f"Starting pipeline execution: Phase {start_phase} to {end_phase}")
        
        for phase_number in range(start_phase, end_phase + 1):
            self.logger.info(f"Pipeline: Executing Phase {phase_number}")
            
            success = self.run_phase(phase_number, args)
            if not success:
                self.logger.error(f"Pipeline failed at Phase {phase_number}")
                return False
        
        self.logger.info(f"Pipeline completed successfully: Phase {start_phase} to {end_phase}")
        return True
    
    def _display_system_info(self, phase_number: int):
        """Display system information"""
        try:
            system_info = get_system_info()
            
            self.logger.info(f"=== Phase {phase_number} System Information ===")
            self.logger.info(f"CPU: {system_info['cpu']['logical_cores']} cores")
            self.logger.info(f"Memory: {system_info['memory']['total_gb']:.1f}GB total")
            self.logger.info(f"GPU: {'Available' if system_info['gpu']['cuda_available'] else 'Not Available'}")
            
            if system_info['gpu']['cuda_available']:
                self.logger.info(f"GPU Name: {system_info['gpu']['name']}")
                self.logger.info(f"GPU Memory: {system_info['gpu']['memory_total_gb']:.1f}GB")
            
        except Exception as e:
            self.logger.warning(f"Could not retrieve system information: {e}")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create unified argument parser for all phases"""
    parser = argparse.ArgumentParser(
        description="TJA Generator - Unified System for Taiko Chart Generation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Phase Descriptions:
  1  - Data Analysis and Preprocessing
  2  - Audio Feature Extraction and Temporal Alignment  
  3  - TJA Sequence Processing and Temporal Encoding
  4  - Neural Network Architecture
  5  - Model Training Optimization
  6  - Inference Pipeline and Validation

Examples:
  # Run single phase
  python main.py --phase 1
  
  # Run pipeline from phase 1 to 3
  python main.py --pipeline 1 3
  
  # Run with test mode
  python main.py --phase 1 --test --count 5
        """
    )
    
    # Phase selection (mutually exclusive)
    phase_group = parser.add_mutually_exclusive_group(required=True)
    phase_group.add_argument(
        '--phase', 
        type=int, 
        choices=[1, 2, 3, 4, 5, 6],
        help='Run specific phase (1-6)'
    )
    phase_group.add_argument(
        '--pipeline', 
        nargs=2, 
        type=int, 
        metavar=('START', 'END'),
        help='Run pipeline from START phase to END phase'
    )
    
    # Common arguments
    parser.add_argument(
        '--test', 
        action='store_true',
        help='Run in test mode (limited dataset)'
    )
    parser.add_argument(
        '--count', 
        type=int, 
        default=5,
        help='Number of files to process in test mode (default: 5)'
    )
    parser.add_argument(
        '--validate-only', 
        action='store_true',
        help='Run validation only (no processing)'
    )
    parser.add_argument(
        '--config', 
        type=str,
        help='Path to custom configuration file'
    )
    parser.add_argument(
        '--output-dir', 
        type=str,
        help='Custom output directory'
    )
    parser.add_argument(
        '--data-dir', 
        type=str,
        help='Custom data directory (Phase 1 only)'
    )
    parser.add_argument(
        '--catalog', 
        type=str,
        help='Path to catalog file (Phase 2+ only)'
    )
    
    return parser


def main():
    """Main entry point"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # Initialize main controller
        tja_generator = TJAGeneratorMain()
        
        # Execute based on arguments
        if args.phase:
            success = tja_generator.run_phase(args.phase, args)
        elif args.pipeline:
            start_phase, end_phase = args.pipeline
            if start_phase > end_phase or start_phase < 1 or end_phase > 6:
                print("Invalid pipeline range. Start must be <= End, both in range 1-6")
                return 1
            success = tja_generator.run_pipeline(start_phase, end_phase, args)
        else:
            print("Must specify either --phase or --pipeline")
            return 1
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1
    finally:
        # Global cleanup
        try:
            cleanup_global_resources()
        except:
            pass


if __name__ == "__main__":
    sys.exit(main())
