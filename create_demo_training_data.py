#!/usr/bin/env python3
"""
Create Demo Training Data

Generate synthetic training data for Phase 3 demonstration
"""

import json
import torch
import numpy as np
from pathlib import Path

def create_demo_training_data():
    """Create synthetic training data for demonstration"""
    print("🎯 Creating Demo Training Data for Phase 3...")
    
    # Create feature tensors directory
    feature_dir = Path("data/processed/audio_features/feature_tensors")
    feature_dir.mkdir(exist_ok=True)
    
    # Load the catalog to get song information
    catalog_path = Path("data/processed/audio_features/phase3_input_catalog.json")
    with open(catalog_path, 'r', encoding='utf-8') as f:
        catalog = json.load(f)
    
    # Create synthetic feature tensors for first 50 songs
    songs_to_process = catalog["songs"][:50]  # Limit to 50 for demo
    created_count = 0
    
    for song in songs_to_process:
        if not song.get("phase3_ready", False):
            continue
            
        # Generate synthetic audio features [time, 201]
        # Use reasonable sequence length for memory efficiency
        sequence_length = np.random.randint(200, 400)  # 4-8 seconds at 50fps
        
        # Create realistic-looking audio features
        features = torch.randn(sequence_length, 201) * 0.5
        
        # Add some structure to make it more realistic
        # Spectral features (first 128 dims) - mel spectrogram-like
        features[:, :128] = torch.abs(features[:, :128])  # Positive values
        
        # MFCC features (next 13 dims) - centered around 0
        features[:, 128:141] = features[:, 128:141] * 2
        
        # Chroma features (next 12 dims) - normalized
        features[:, 141:153] = torch.softmax(features[:, 141:153], dim=1)
        
        # Rhythmic features (next 32 dims) - binary-like
        features[:, 153:185] = torch.sigmoid(features[:, 153:185])
        
        # Temporal features (last 16 dims) - smooth changes
        for i in range(185, 201):
            features[:, i] = torch.cumsum(features[:, i] * 0.1, dim=0)
        
        # Save the feature tensor
        feature_path = feature_dir / f"{song['song_id']}_features.pt"
        torch.save({
            "features": features,
            "metadata": {
                "sequence_length": sequence_length,
                "feature_dims": 201,
                "sample_rate": 44100,
                "hop_length": 882  # ~50fps
            }
        }, feature_path)
        
        # Update the catalog entry with correct path
        song["feature_tensor_path"] = str(feature_path)
        
        created_count += 1
        if created_count % 10 == 0:
            print(f"  Created {created_count} feature tensors...")
    
    # Update the catalog with correct paths
    catalog["songs"] = catalog["songs"][:50]  # Keep only processed songs
    catalog["dataset_statistics"]["total_songs"] = len(catalog["songs"])
    catalog["dataset_statistics"]["phase3_ready_songs"] = created_count
    
    # Save updated catalog
    with open(catalog_path, 'w', encoding='utf-8') as f:
        json.dump(catalog, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created {created_count} synthetic feature tensors")
    print(f"📊 Demo dataset ready with {created_count} songs")
    
    return created_count

def create_demo_phase1_data():
    """Create minimal Phase 1 data for training"""
    print("📝 Creating Demo Phase 1 Data...")
    
    # Load Phase 3 catalog to get song IDs
    catalog_path = Path("data/processed/audio_features/phase3_input_catalog.json")
    with open(catalog_path, 'r', encoding='utf-8') as f:
        phase3_catalog = json.load(f)
    
    # Create Phase 1 catalog structure
    phase1_catalog = {
        "dataset_info": {
            "name": "Demo TJA Dataset",
            "version": "1.0",
            "total_songs": 50,
            "creation_date": "2025-07-25"
        },
        "songs": []
    }
    
    # Create synthetic TJA data for each song
    for song in phase3_catalog["songs"][:50]:
        song_id = song["song_id"]
        
        # Generate synthetic TJA sequence
        sequence_length = np.random.randint(100, 200)  # Reasonable TJA length
        
        # Create note sequence (0=blank, 1=don, 2=ka, 3=don_big, 4=ka_big)
        note_sequence = []
        timing_sequence = []
        
        for i in range(sequence_length):
            # Create realistic note patterns
            if i % 4 == 0:  # Beat positions more likely to have notes
                note_type = np.random.choice([1, 2, 3, 4], p=[0.4, 0.4, 0.1, 0.1])
            else:
                note_type = np.random.choice([0, 1, 2], p=[0.7, 0.2, 0.1])
            
            timing_ms = i * 125.0  # 125ms per 16th note (120 BPM)
            
            note_sequence.append({
                "type": ["blank", "don", "ka", "don_big", "ka_big"][note_type],
                "timing_ms": timing_ms,
                "position": i
            })
            
        # Create Phase 1 song entry
        phase1_song = {
            "song_id": song_id,
            "title": f"Demo Song {song_id}",
            "artist": "Demo Artist",
            "reference_metadata": {
                "bpm": 120,
                "duration_seconds": sequence_length * 0.125,
                "time_signature": "4/4"
            },
            "difficulties": {
                "Oni_9": {  # Focus on Oni 9 difficulty
                    "notation_data": {
                        "note_sequences": note_sequence,
                        "difficulty_level": 9,
                        "note_count": sum(1 for note in note_sequence if note["type"] != "blank")
                    },
                    "validation": {
                        "valid": True,
                        "note_count": sum(1 for note in note_sequence if note["type"] != "blank"),
                        "duration_seconds": sequence_length * 0.125
                    }
                }
            }
        }
        
        phase1_catalog["songs"].append(phase1_song)
    
    # Save Phase 1 catalog
    phase1_path = Path("data/processed/catalog.json")
    with open(phase1_path, 'w', encoding='utf-8') as f:
        json.dump(phase1_catalog, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created Phase 1 catalog with {len(phase1_catalog['songs'])} songs")
    
    return len(phase1_catalog["songs"])

def main():
    """Main function"""
    print("🚀 Setting up Demo Training Environment...")
    
    # Create demo data
    feature_count = create_demo_training_data()
    phase1_count = create_demo_phase1_data()
    
    print(f"\n✅ Demo Training Data Created Successfully!")
    print(f"  Feature tensors: {feature_count}")
    print(f"  Phase 1 songs: {phase1_count}")
    print(f"  Ready for Phase 3 training!")

if __name__ == "__main__":
    main()
